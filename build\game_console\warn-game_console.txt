
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed), backports.tarfile (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._distutils.tests.unix_compat (optional), setuptools._distutils.tests.test_util (delayed)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), backports.tarfile (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), setuptools._distutils.tests.unix_compat (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), test.support (delayed, conditional, optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named fcntl - imported by subprocess (optional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
runtime module named distutils.util - imported by setuptools._core_metadata (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (top-level), setuptools._distutils.extension (delayed), setuptools._distutils.compilers.C.unix (delayed, conditional), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_util (top-level)
runtime module named distutils - imported by distutils._log (top-level), distutils._macos_compat (top-level), distutils._modified (top-level), distutils._msvccompiler (top-level), distutils.archive_util (top-level), distutils.ccompiler (top-level), distutils.cmd (top-level), distutils.command (top-level), distutils.compat (top-level), distutils.core (top-level), distutils.cygwinccompiler (top-level), distutils.debug (top-level), distutils.dep_util (top-level), distutils.dir_util (top-level), distutils.dist (top-level), distutils.errors (top-level), distutils.extension (top-level), distutils.fancy_getopt (top-level), distutils.file_util (top-level), distutils.filelist (top-level), distutils.log (top-level), distutils.spawn (top-level), distutils.sysconfig (top-level), distutils.tests (top-level), distutils.text_file (top-level), distutils.unixccompiler (top-level), distutils.util (top-level), distutils.version (top-level), distutils.versionpredicate (top-level), distutils.zosccompiler (top-level), setuptools.discovery (top-level), setuptools.errors (top-level), setuptools.command.bdist_egg (top-level), setuptools.command.sdist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (delayed), setuptools.installer (top-level), setuptools._shutil (top-level), setuptools.command.bdist_wheel (top-level), setuptools._distutils.util (delayed, conditional), setuptools._distutils.command.build_ext (delayed), setuptools._distutils.command.sdist (top-level), setuptools._distutils.compilers.C.cygwin (delayed), setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_util (top-level), setuptools._distutils.tests.test_version (top-level)
runtime module named distutils.fancy_getopt - imported by setuptools.dist (top-level), setuptools._distutils.compilers.C.base (delayed), setuptools._distutils.cmd (delayed)
runtime module named distutils.debug - imported by setuptools.dist (top-level), setuptools._distutils.compilers.C.base (delayed), setuptools._distutils.cmd (delayed), setuptools._distutils.filelist (delayed), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_filelist (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
runtime module named distutils.sysconfig - imported by setuptools._distutils.util (delayed, conditional), setuptools._distutils.extension (delayed), setuptools._distutils.command.build_ext (delayed), setuptools._distutils.compilers.C.cygwin (delayed), setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_util (top-level)
excluded module named pytest - imported by setuptools._vendor.typeguard._pytest_plugin (conditional), setuptools._distutils.tests.support (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.unix_compat (top-level), setuptools._distutils.tests.test_bdist_dumb (top-level), setuptools._distutils.tests.test_bdist_rpm (top-level), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_check (top-level), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_core (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_extension (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_data (top-level), setuptools._distutils.tests.test_install_headers (top-level), setuptools._distutils.tests.test_install_lib (top-level), setuptools._distutils.tests.test_modified (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_util (top-level), setuptools._distutils.tests.test_version (top-level)
runtime module named distutils.version - imported by setuptools._distutils.tests.test_version (top-level)
missing module named path - imported by setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_sysconfig (top-level), setuptools._distutils.tests.test_text_file (top-level)
missing module named 'jaraco.path' - imported by setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_text_file (top-level)
runtime module named distutils.text_file - imported by setuptools._distutils.extension (delayed), setuptools._distutils.sysconfig (delayed), setuptools._distutils.tests.test_text_file (top-level)
runtime module named distutils.tests - imported by distutils.tests.compat (top-level), distutils.tests.support (top-level), distutils.tests.test_archive_util (top-level), distutils.tests.test_bdist (top-level), distutils.tests.test_bdist_dumb (top-level), distutils.tests.test_bdist_rpm (top-level), distutils.tests.test_build (top-level), distutils.tests.test_build_clib (top-level), distutils.tests.test_build_ext (top-level), distutils.tests.test_build_py (top-level), distutils.tests.test_build_scripts (top-level), distutils.tests.test_check (top-level), distutils.tests.test_clean (top-level), distutils.tests.test_cmd (top-level), distutils.tests.test_config_cmd (top-level), distutils.tests.test_core (top-level), distutils.tests.test_dir_util (top-level), distutils.tests.test_dist (top-level), distutils.tests.test_extension (top-level), distutils.tests.test_file_util (top-level), distutils.tests.test_filelist (top-level), distutils.tests.test_install (top-level), distutils.tests.test_install_data (top-level), distutils.tests.test_install_headers (top-level), distutils.tests.test_install_lib (top-level), distutils.tests.test_install_scripts (top-level), distutils.tests.test_log (top-level), distutils.tests.test_modified (top-level), distutils.tests.test_sdist (top-level), distutils.tests.test_spawn (top-level), distutils.tests.test_sysconfig (top-level), distutils.tests.test_text_file (top-level), distutils.tests.test_util (top-level), distutils.tests.test_version (top-level), distutils.tests.test_versionpredicate (top-level), distutils.tests.unix_compat (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_bdist (top-level), setuptools._distutils.tests.test_bdist_dumb (top-level), setuptools._distutils.tests.test_bdist_rpm (top-level), setuptools._distutils.tests.test_build (top-level), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_check (top-level), setuptools._distutils.tests.test_clean (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_dist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_data (top-level), setuptools._distutils.tests.test_install_headers (top-level), setuptools._distutils.tests.test_install_lib (top-level), setuptools._distutils.tests.test_install_scripts (top-level), setuptools._distutils.tests.test_modified (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_text_file (top-level)
missing module named test.support.unlink - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.skip_unless_symlink - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.rmtree - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.EnvironmentVarGuard - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.DirsOnSysPath - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named test.support.CleanImport - imported by test.support (conditional), setuptools._distutils.tests.compat.py39 (conditional)
missing module named termios - imported by getpass (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), pygments.formatters.img (optional)
missing module named importlib_resources - imported by jaraco.text (optional)
missing module named 'jaraco.envs' - imported by setuptools._distutils.tests.test_sysconfig (top-level)
runtime module named distutils.unixccompiler - imported by setuptools._distutils.tests.test_sysconfig (top-level)
runtime module named distutils.ccompiler - imported by setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_sysconfig (top-level)
runtime module named distutils.spawn - imported by setuptools._distutils.cmd (delayed), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_spawn (top-level)
runtime module named distutils.filelist - imported by setuptools.monkey (top-level), setuptools.command.egg_info (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_sdist (top-level)
runtime module named distutils.command.sdist - imported by setuptools.command.sdist (top-level), setuptools._distutils.tests.test_sdist (top-level)
runtime module named distutils.command - imported by distutils.command._framework_compat (top-level), distutils.command.bdist (top-level), distutils.command.bdist_dumb (top-level), distutils.command.bdist_rpm (top-level), distutils.command.build (top-level), distutils.command.build_clib (top-level), distutils.command.build_ext (top-level), distutils.command.build_py (top-level), distutils.command.build_scripts (top-level), distutils.command.check (top-level), distutils.command.clean (top-level), distutils.command.config (top-level), distutils.command.install (top-level), distutils.command.install_data (top-level), distutils.command.install_egg_info (top-level), distutils.command.install_headers (top-level), distutils.command.install_lib (top-level), distutils.command.install_scripts (top-level), distutils.command.sdist (top-level), setuptools.dist (top-level), setuptools._distutils.dist (delayed), setuptools._distutils.tests.test_build_ext (delayed), setuptools._distutils.tests.test_install (top-level)
runtime module named distutils.archive_util - imported by setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_archive_util (top-level), setuptools._distutils.tests.test_sdist (top-level)
runtime module named distutils._modified - imported by setuptools._distutils.file_util (delayed), setuptools._distutils.tests.test_modified (top-level)
runtime module named distutils._log - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_config_cmd (top-level), setuptools._distutils.tests.test_log (top-level)
runtime module named distutils.command.install_scripts - imported by setuptools._distutils.tests.test_install_scripts (top-level)
runtime module named distutils.extension - imported by setuptools.extension (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_extension (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_lib (top-level)
runtime module named distutils.command.install_lib - imported by setuptools._distutils.tests.test_install_lib (top-level)
runtime module named distutils.command.install_headers - imported by setuptools._distutils.tests.test_install_headers (top-level)
runtime module named distutils.command.install_data - imported by setuptools._distutils.tests.test_install_data (top-level)
runtime module named distutils.command.install - imported by setuptools._distutils.tests.test_install (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by site (delayed, optional), rlcompleter (optional)
excluded module named pydoc - imported by _sitebuiltins (delayed)
runtime module named distutils.file_util - imported by setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_file_util (top-level)
runtime module named distutils.tests.test_dist - imported by setuptools._distutils.tests.test_dist (delayed)
runtime module named distutils.dist - imported by setuptools.config.setupcfg (conditional), setuptools.config._apply_pyprojecttoml (conditional), setuptools.dist (top-level), setuptools._distutils.cmd (delayed, conditional), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_core (top-level), setuptools._distutils.tests.test_dist (top-level)
runtime module named distutils.cmd - imported by setuptools.dist (top-level), setuptools._distutils.dist (delayed), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_dist (top-level)
runtime module named distutils.dir_util - imported by setuptools.command.bdist_egg (top-level), setuptools._distutils.command.sdist (top-level), setuptools._distutils.tests.test_dir_util (top-level)
runtime module named distutils.command.config - imported by setuptools._distutils.tests.test_config_cmd (top-level)
runtime module named distutils.command.clean - imported by setuptools._distutils.tests.test_clean (top-level)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'numpy.typing' - imported by PIL._typing (conditional, optional)
missing module named ctags - imported by pygments.formatters.html (optional)
runtime module named distutils.command.check - imported by setuptools._distutils.tests.test_check (top-level)
runtime module named distutils.command.build_scripts - imported by setuptools._distutils.tests.test_build_scripts (top-level)
runtime module named distutils.command.build_py - imported by setuptools._distutils.tests.test_build_py (top-level)
missing module named xx - imported by setuptools._distutils.tests.test_build_ext (delayed)
runtime module named distutils.tests.support - imported by setuptools._distutils.tests.test_build_ext (top-level)
runtime module named distutils.command.build_clib - imported by setuptools._distutils.tests.test_build_clib (top-level)
runtime module named distutils.command.build - imported by setuptools.command.build (top-level), setuptools._distutils.tests.test_build (top-level)
runtime module named distutils.command.bdist_rpm - imported by setuptools._distutils.tests.test_bdist_rpm (top-level)
runtime module named distutils.command.bdist_dumb - imported by setuptools._distutils.tests.test_bdist_dumb (top-level)
runtime module named distutils.command.bdist - imported by setuptools.command (top-level), setuptools._distutils.tests.test_bdist (top-level)
runtime module named distutils.versionpredicate - imported by setuptools._distutils.dist (delayed)
missing module named _typeshed - imported by pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), setuptools._distutils.dist (conditional)
missing module named 'docutils.utils' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.parsers' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.nodes' - imported by setuptools._distutils.command.check (top-level)
missing module named docutils - imported by setuptools._distutils.command.check (top-level)
runtime module named distutils.log - imported by setuptools.logging (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools.command.bdist_egg (top-level), setuptools.command.sdist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (delayed), setuptools.installer (top-level), setuptools._shutil (top-level), setuptools.command.bdist_wheel (top-level)
missing module named typeshed - imported by typeguard._decorators (conditional)
missing module named 'typeshed.stdlib' - imported by setuptools._vendor.typeguard._decorators (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional), setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional), setuptools._vendor.platformdirs.android (delayed, conditional, optional)
runtime module named distutils.command.build_ext - imported by setuptools (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_install (top-level)
runtime module named distutils.errors - imported by setuptools.errors (top-level), setuptools.config.expand (top-level), setuptools.extension (top-level), setuptools.dist (top-level), setuptools.archive_util (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.installer (top-level), setuptools (top-level), setuptools.msvc (top-level), setuptools._distutils.tests (delayed), setuptools._distutils.tests.test_build_clib (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_check (top-level), setuptools._distutils.tests.test_cmd (top-level), setuptools._distutils.tests.test_dir_util (top-level), setuptools._distutils.tests.test_file_util (top-level), setuptools._distutils.tests.test_filelist (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_lib (top-level), setuptools._distutils.tests.test_modified (top-level), setuptools._distutils.tests.test_sdist (top-level), setuptools._distutils.tests.test_spawn (top-level), setuptools._distutils.tests.test_util (top-level)
runtime module named distutils.core - imported by setuptools.extension (top-level), setuptools.dist (top-level), setuptools (top-level), setuptools._distutils.dist (delayed), setuptools._distutils.tests.support (top-level), setuptools._distutils.tests.test_bdist_dumb (top-level), setuptools._distutils.tests.test_bdist_rpm (top-level), setuptools._distutils.tests.test_build_ext (top-level), setuptools._distutils.tests.test_build_py (top-level), setuptools._distutils.tests.test_build_scripts (top-level), setuptools._distutils.tests.test_core (top-level), setuptools._distutils.tests.test_install (top-level), setuptools._distutils.tests.test_install_scripts (top-level), setuptools._distutils.tests.test_sdist (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional)
missing module named PyQt4 - imported by pyperclip (delayed, conditional, optional)
missing module named PyQt5 - imported by pyperclip (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
missing module named gtk - imported by pyperclip (delayed, conditional, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named OpenGL - imported by pygame (delayed)
excluded module named numpy - imported by pygame.surfarray (top-level), pygame.sndarray (top-level), pygame (delayed)
missing module named pygame.__file__ - imported by pygame (top-level), pygame.sysfont (top-level)
missing module named pygame_static - imported by pygame (conditional, optional)
