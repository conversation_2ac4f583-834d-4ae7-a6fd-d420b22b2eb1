#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误消息转换器
将技术错误信息转换为用户友好的消息
提供错误分类、消息模板和智能转换功能

Author: Game Development Team
Date: 2024
"""

import re
import traceback
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass


class ErrorCategory(Enum):
    """错误分类枚举"""
    SYSTEM = "system"              # 系统错误
    NETWORK = "network"            # 网络错误
    FILE_IO = "file_io"            # 文件IO错误
    DATA_LOADING = "data_loading"  # 数据加载错误
    BATTLE_SYSTEM = "battle_system" # 战斗系统错误
    UI_RENDERING = "ui_rendering"  # UI渲染错误
    GAME_LOGIC = "game_logic"      # 游戏逻辑错误
    RESOURCE = "resource"          # 资源错误
    CONFIGURATION = "configuration" # 配置错误
    UNKNOWN = "unknown"            # 未知错误


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    LOW = 1      # 低级错误，不影响游戏进行
    MEDIUM = 2   # 中级错误，可能影响部分功能
    HIGH = 3     # 高级错误，影响主要功能
    CRITICAL = 4 # 严重错误，可能导致游戏崩溃


@dataclass
class ErrorPattern:
    """错误模式匹配规则"""
    pattern: str                    # 正则表达式模式
    category: ErrorCategory         # 错误分类
    severity: ErrorSeverity         # 错误严重程度
    user_message_template: str      # 用户消息模板
    developer_action: str           # 开发者建议操作
    auto_recovery: bool = False     # 是否支持自动恢复
    recovery_action: str = ""       # 恢复操作描述


class ErrorMessageConverter:
    """
    错误消息转换器
    
    功能:
    1. 将技术错误信息转换为用户友好的消息
    2. 错误分类和严重程度评估
    3. 提供错误恢复建议
    4. 支持自定义错误模式和消息模板
    5. 智能错误识别和处理
    """
    
    def __init__(self):
        """
        初始化错误消息转换器
        """
        # 错误模式规则
        self.error_patterns: List[ErrorPattern] = []
        
        # 错误统计
        self.error_stats = {
            "total_errors": 0,
            "errors_by_category": {},
            "errors_by_severity": {},
            "conversion_success_rate": 0.0
        }
        
        # 初始化默认错误模式
        self._init_default_patterns()
    
    def _init_default_patterns(self):
        """
        初始化默认错误模式
        """
        default_patterns = [
            # 文件IO错误
            ErrorPattern(
                pattern=r"FileNotFoundError.*?'([^']+)'.*?",
                category=ErrorCategory.FILE_IO,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="无法找到游戏文件: {file_name}，请检查游戏安装是否完整",
                developer_action="检查文件路径和权限",
                auto_recovery=True,
                recovery_action="尝试重新加载或使用默认配置"
            ),
            ErrorPattern(
                pattern=r"PermissionError.*?'([^']+)'.*?",
                category=ErrorCategory.FILE_IO,
                severity=ErrorSeverity.HIGH,
                user_message_template="文件访问权限不足: {file_name}，请以管理员身份运行游戏",
                developer_action="检查文件权限设置",
                auto_recovery=False,
                recovery_action="请重新启动游戏并授予必要权限"
            ),
            
            # 数据加载错误
            ErrorPattern(
                pattern=r"KeyError.*?'([^']+)'.*?",
                category=ErrorCategory.DATA_LOADING,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="游戏数据缺失: {key_name}，正在尝试使用默认设置",
                developer_action="检查数据文件完整性",
                auto_recovery=True,
                recovery_action="使用默认数据继续游戏"
            ),
            ErrorPattern(
                pattern=r"json\.decoder\.JSONDecodeError.*?",
                category=ErrorCategory.DATA_LOADING,
                severity=ErrorSeverity.HIGH,
                user_message_template="游戏配置文件损坏，正在重置为默认设置",
                developer_action="检查JSON文件格式",
                auto_recovery=True,
                recovery_action="重置配置文件为默认值"
            ),
            
            # 网络错误
            ErrorPattern(
                pattern=r"ConnectionError.*?",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="网络连接失败，请检查网络设置",
                developer_action="检查网络连接和服务器状态",
                auto_recovery=True,
                recovery_action="切换到离线模式"
            ),
            ErrorPattern(
                pattern=r"TimeoutError.*?",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="网络请求超时，请稍后重试",
                developer_action="检查网络延迟和服务器响应",
                auto_recovery=True,
                recovery_action="自动重试连接"
            ),
            
            # 内存错误
            ErrorPattern(
                pattern=r"MemoryError.*?",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.CRITICAL,
                user_message_template="系统内存不足，请关闭其他程序后重试",
                developer_action="优化内存使用",
                auto_recovery=False,
                recovery_action="建议重启游戏"
            ),
            
            # 类型错误
            ErrorPattern(
                pattern=r"TypeError.*?'([^']+)'.*?",
                category=ErrorCategory.GAME_LOGIC,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="游戏逻辑错误，正在尝试修复",
                developer_action="检查数据类型匹配",
                auto_recovery=True,
                recovery_action="跳过错误操作继续游戏"
            ),
            
            # 属性错误
            ErrorPattern(
                pattern=r"AttributeError.*?'([^']+)'.*?'([^']+)'.*?",
                category=ErrorCategory.GAME_LOGIC,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="游戏组件初始化错误，正在重新加载",
                developer_action="检查对象属性和初始化",
                auto_recovery=True,
                recovery_action="重新初始化相关组件"
            ),
            
            # 索引错误
            ErrorPattern(
                pattern=r"IndexError.*?",
                category=ErrorCategory.GAME_LOGIC,
                severity=ErrorSeverity.LOW,
                user_message_template="数据访问异常，已自动修复",
                developer_action="检查数组边界",
                auto_recovery=True,
                recovery_action="使用安全的数据访问方式"
            ),
            
            # 值错误
            ErrorPattern(
                pattern=r"ValueError.*?",
                category=ErrorCategory.DATA_LOADING,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="数据格式错误，正在使用默认值",
                developer_action="检查数据格式和范围",
                auto_recovery=True,
                recovery_action="使用默认数据值"
            ),
            
            # 导入错误
            ErrorPattern(
                pattern=r"ImportError.*?'([^']+)'.*?",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.HIGH,
                user_message_template="游戏组件加载失败: {module_name}，请重新安装游戏",
                developer_action="检查模块依赖",
                auto_recovery=False,
                recovery_action="重新安装游戏或相关依赖"
            ),
            
            # 模块未找到错误
            ErrorPattern(
                pattern=r"ModuleNotFoundError.*?'([^']+)'.*?",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.HIGH,
                user_message_template="缺少必要的游戏组件: {module_name}，请检查安装",
                developer_action="检查模块安装",
                auto_recovery=False,
                recovery_action="安装缺失的依赖包"
            ),
            
            # 除零错误
            ErrorPattern(
                pattern=r"ZeroDivisionError.*?",
                category=ErrorCategory.GAME_LOGIC,
                severity=ErrorSeverity.LOW,
                user_message_template="计算异常，已自动修复",
                developer_action="检查除法运算",
                auto_recovery=True,
                recovery_action="使用安全的数学运算"
            ),
            
            # 递归错误
            ErrorPattern(
                pattern=r"RecursionError.*?",
                category=ErrorCategory.GAME_LOGIC,
                severity=ErrorSeverity.HIGH,
                user_message_template="游戏逻辑循环错误，正在重置",
                developer_action="检查递归调用",
                auto_recovery=True,
                recovery_action="重置相关游戏状态"
            ),
            
            # 战斗系统特定错误
            ErrorPattern(
                pattern=r".*battle.*error.*",
                category=ErrorCategory.BATTLE_SYSTEM,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="战斗系统异常，正在重新初始化",
                developer_action="检查战斗逻辑",
                auto_recovery=True,
                recovery_action="重置战斗状态"
            ),
            
            # UI渲染错误
            ErrorPattern(
                pattern=r".*pygame.*error.*",
                category=ErrorCategory.UI_RENDERING,
                severity=ErrorSeverity.MEDIUM,
                user_message_template="界面渲染异常，正在修复显示",
                developer_action="检查pygame相关代码",
                auto_recovery=True,
                recovery_action="重新初始化显示组件"
            )
        ]
        
        self.error_patterns.extend(default_patterns)
    
    def convert_error(self, 
                     error_message: str, 
                     exception_type: str = None,
                     traceback_info: str = None,
                     context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        转换错误消息
        
        Args:
            error_message: 原始错误消息
            exception_type: 异常类型
            traceback_info: 堆栈跟踪信息
            context: 错误上下文信息
            
        Returns:
            转换结果字典
        """
        # 更新统计
        self.error_stats["total_errors"] += 1
        
        # 尝试匹配错误模式
        matched_pattern = self._match_error_pattern(error_message, exception_type, traceback_info)
        
        if matched_pattern:
            # 成功匹配，生成用户友好消息
            user_message = self._generate_user_message(matched_pattern, error_message, context)
            
            # 更新统计
            category = matched_pattern.category.value
            severity = matched_pattern.severity.value
            
            if category not in self.error_stats["errors_by_category"]:
                self.error_stats["errors_by_category"][category] = 0
            self.error_stats["errors_by_category"][category] += 1
            
            if severity not in self.error_stats["errors_by_severity"]:
                self.error_stats["errors_by_severity"][severity] = 0
            self.error_stats["errors_by_severity"][severity] += 1
            
            return {
                "success": True,
                "user_message": user_message,
                "category": matched_pattern.category,
                "severity": matched_pattern.severity,
                "auto_recovery": matched_pattern.auto_recovery,
                "recovery_action": matched_pattern.recovery_action,
                "developer_action": matched_pattern.developer_action,
                "original_error": error_message,
                "pattern_matched": True
            }
        else:
            # 未匹配到模式，生成通用消息
            generic_result = self._generate_generic_message(error_message, exception_type)
            
            # 更新统计
            category = "unknown"
            if category not in self.error_stats["errors_by_category"]:
                self.error_stats["errors_by_category"][category] = 0
            self.error_stats["errors_by_category"][category] += 1
            
            return generic_result
    
    def _match_error_pattern(self, 
                           error_message: str, 
                           exception_type: str = None,
                           traceback_info: str = None) -> Optional[ErrorPattern]:
        """
        匹配错误模式
        
        Args:
            error_message: 错误消息
            exception_type: 异常类型
            traceback_info: 堆栈跟踪信息
            
        Returns:
            匹配的错误模式或None
        """
        # 构建完整的错误文本用于匹配
        full_error_text = error_message
        if exception_type:
            full_error_text = f"{exception_type}: {full_error_text}"
        if traceback_info:
            full_error_text += f"\n{traceback_info}"
        
        # 尝试匹配每个模式
        for pattern in self.error_patterns:
            try:
                if re.search(pattern.pattern, full_error_text, re.IGNORECASE | re.DOTALL):
                    return pattern
            except re.error:
                # 正则表达式错误，跳过此模式
                continue
        
        return None
    
    def _generate_user_message(self, 
                             pattern: ErrorPattern, 
                             original_error: str,
                             context: Dict[str, Any] = None) -> str:
        """
        生成用户友好消息
        
        Args:
            pattern: 匹配的错误模式
            original_error: 原始错误消息
            context: 错误上下文
            
        Returns:
            用户友好的错误消息
        """
        try:
            # 提取错误消息中的关键信息
            match = re.search(pattern.pattern, original_error, re.IGNORECASE | re.DOTALL)
            
            # 准备模板变量
            template_vars = {}
            
            if match and match.groups():
                # 根据匹配组数量设置变量
                groups = match.groups()
                if len(groups) >= 1:
                    template_vars["file_name"] = groups[0]
                    template_vars["key_name"] = groups[0]
                    template_vars["module_name"] = groups[0]
                if len(groups) >= 2:
                    template_vars["attribute_name"] = groups[1]
            
            # 添加上下文信息
            if context:
                template_vars.update(context)
            
            # 格式化消息模板
            try:
                user_message = pattern.user_message_template.format(**template_vars)
            except KeyError:
                # 模板变量不足，使用原始模板
                user_message = pattern.user_message_template
            
            return user_message
            
        except Exception:
            # 消息生成失败，返回通用消息
            return self._get_generic_message_by_category(pattern.category)
    
    def _generate_generic_message(self, 
                                error_message: str, 
                                exception_type: str = None) -> Dict[str, Any]:
        """
        生成通用错误消息
        
        Args:
            error_message: 错误消息
            exception_type: 异常类型
            
        Returns:
            通用错误转换结果
        """
        # 根据异常类型推断分类
        category = self._infer_category_from_exception(exception_type)
        severity = ErrorSeverity.MEDIUM  # 默认中等严重程度
        
        # 生成通用用户消息
        user_message = self._get_generic_message_by_category(category)
        
        return {
            "success": False,
            "user_message": user_message,
            "category": category,
            "severity": severity,
            "auto_recovery": False,
            "recovery_action": "请重试操作，如问题持续请联系技术支持",
            "developer_action": "检查错误日志获取详细信息",
            "original_error": error_message,
            "pattern_matched": False
        }
    
    def _infer_category_from_exception(self, exception_type: str = None) -> ErrorCategory:
        """
        从异常类型推断错误分类
        
        Args:
            exception_type: 异常类型
            
        Returns:
            推断的错误分类
        """
        if not exception_type:
            return ErrorCategory.UNKNOWN
        
        exception_type = exception_type.lower()
        
        if "file" in exception_type or "io" in exception_type:
            return ErrorCategory.FILE_IO
        elif "network" in exception_type or "connection" in exception_type:
            return ErrorCategory.NETWORK
        elif "memory" in exception_type:
            return ErrorCategory.SYSTEM
        elif "import" in exception_type or "module" in exception_type:
            return ErrorCategory.SYSTEM
        elif "json" in exception_type or "data" in exception_type:
            return ErrorCategory.DATA_LOADING
        elif "type" in exception_type or "value" in exception_type or "attribute" in exception_type:
            return ErrorCategory.GAME_LOGIC
        else:
            return ErrorCategory.UNKNOWN
    
    def _get_generic_message_by_category(self, category: ErrorCategory) -> str:
        """
        根据分类获取通用错误消息
        
        Args:
            category: 错误分类
            
        Returns:
            通用错误消息
        """
        generic_messages = {
            ErrorCategory.SYSTEM: "系统运行异常，正在尝试自动修复",
            ErrorCategory.NETWORK: "网络连接异常，请检查网络设置",
            ErrorCategory.FILE_IO: "文件操作异常，请检查文件权限",
            ErrorCategory.DATA_LOADING: "数据加载异常，正在使用默认设置",
            ErrorCategory.BATTLE_SYSTEM: "战斗系统异常，正在重新初始化",
            ErrorCategory.UI_RENDERING: "界面显示异常，正在修复",
            ErrorCategory.GAME_LOGIC: "游戏逻辑异常，已自动处理",
            ErrorCategory.RESOURCE: "资源加载异常，正在重试",
            ErrorCategory.CONFIGURATION: "配置异常，正在重置为默认设置",
            ErrorCategory.UNKNOWN: "发生未知错误，正在尝试修复"
        }
        
        return generic_messages.get(category, "发生未知错误，请重试")
    
    def add_custom_pattern(self, pattern: ErrorPattern):
        """
        添加自定义错误模式
        
        Args:
            pattern: 自定义错误模式
        """
        self.error_patterns.append(pattern)
    
    def remove_pattern(self, pattern_regex: str) -> bool:
        """
        移除错误模式
        
        Args:
            pattern_regex: 要移除的模式正则表达式
            
        Returns:
            是否成功移除
        """
        for i, pattern in enumerate(self.error_patterns):
            if pattern.pattern == pattern_regex:
                del self.error_patterns[i]
                return True
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取错误转换统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.error_stats.copy()
        
        # 计算转换成功率
        if stats["total_errors"] > 0:
            successful_conversions = sum(
                count for category, count in stats["errors_by_category"].items() 
                if category != "unknown"
            )
            stats["conversion_success_rate"] = successful_conversions / stats["total_errors"]
        
        return stats
    
    def reset_stats(self):
        """
        重置统计信息
        """
        self.error_stats = {
            "total_errors": 0,
            "errors_by_category": {},
            "errors_by_severity": {},
            "conversion_success_rate": 0.0
        }


# 全局错误消息转换器实例
_global_error_converter: Optional[ErrorMessageConverter] = None


def get_error_converter() -> ErrorMessageConverter:
    """
    获取全局错误消息转换器实例
    
    Returns:
        全局错误消息转换器实例
    """
    global _global_error_converter
    if _global_error_converter is None:
        _global_error_converter = ErrorMessageConverter()
    return _global_error_converter


def convert_error_to_user_message(error_message: str, 
                                 exception_type: str = None,
                                 traceback_info: str = None,
                                 context: Dict[str, Any] = None) -> str:
    """
    快捷函数：将错误转换为用户友好消息
    
    Args:
        error_message: 错误消息
        exception_type: 异常类型
        traceback_info: 堆栈跟踪信息
        context: 错误上下文
        
    Returns:
        用户友好的错误消息
    """
    converter = get_error_converter()
    result = converter.convert_error(error_message, exception_type, traceback_info, context)
    return result["user_message"]