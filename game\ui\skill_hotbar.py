"""
自动技能栏系统
自动管理和释放已启用的技能
"""
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import time
import json
import os
import threading

class AutoSkillHotbar(tk.Toplevel):
    """自动技能栏界面类"""
    
    def __init__(self, parent=None, player=None):
        """
        初始化自动技能栏
        
        Args:
            parent: 父窗口
            player: 玩家对象
        """
        super().__init__(parent)
        self.title("自动技能栏")
        self.geometry("500x120")
        self.resizable(False, False)
        
        # 设置窗口样式
        self.configure(bg="#1a1a1a")
        
        # 保存玩家对象引用
        self.player = player
        
        # 启用的技能列表
        self.enabled_skills = {}
        
        # 技能冷却状态
        self.skill_cooldowns = {}
        
        # 自动释放运行状态
        self.auto_cast_running = True
        
        # UI组件
        self.skill_labels = {}
        self.cooldown_labels = {}
        
        # 创建UI
        self._create_widgets()
        
        # 加载配置
        self._load_auto_cast_config()
        
        # 启动自动释放定时器
        self._start_auto_cast_timer()
        
        # 启动配置监控
        self._start_config_monitor()
        
    def _create_widgets(self):
        """创建UI组件"""
        # 主容器
        main_frame = tk.Frame(self, bg="#1a1a1a")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 标题
        title_label = tk.Label(
            main_frame,
            text="自动技能栏 - 已启用的技能将自动释放",
            bg="#1a1a1a",
            fg="#ffffff",
            font=("微软雅黑", 10, "bold")
        )
        title_label.pack(pady=5)
        
        # 技能显示区域
        self.skills_frame = tk.Frame(main_frame, bg="#1a1a1a")
        self.skills_frame.pack(pady=5, fill=tk.X)
        
        # 控制按钮区域
        control_frame = tk.Frame(main_frame, bg="#1a1a1a")
        control_frame.pack(pady=5)
        
        # 暂停/继续按钮
        self.pause_btn = tk.Button(
            control_frame,
            text="暂停自动释放",
            bg="#ff6666",
            fg="#ffffff",
            activebackground="#ff4444",
            activeforeground="#ffffff",
            command=self._toggle_auto_cast,
            font=("微软雅黑", 9)
        )
        self.pause_btn.pack(side=tk.LEFT, padx=5)
        
        # 手动释放全部按钮
        manual_btn = tk.Button(
            control_frame,
            text="立即释放全部",
            bg="#3a3a3a",
            fg="#ffffff",
            activebackground="#4a4a4a",
            activeforeground="#ffffff",
            command=self._manual_cast_all,
            font=("微软雅黑", 9)
        )
        manual_btn.pack(side=tk.LEFT, padx=5)
        
    def _create_skill_display(self, skill_name):
        """创建技能显示组件"""
        skill_frame = tk.Frame(self.skills_frame, bg="#2a2a2a", relief=tk.RAISED, bd=2)
        skill_frame.pack(side=tk.LEFT, padx=2, pady=2)
        
        # 技能名称
        name_label = tk.Label(
            skill_frame,
            text=skill_name,
            bg="#2a2a2a",
            fg="#ffffff",
            font=("微软雅黑", 8, "bold"),
            width=8,
            height=1
        )
        name_label.pack(padx=2, pady=1)
        
        # 冷却时间显示
        cooldown_label = tk.Label(
            skill_frame,
            text="就绪",
            bg="#2a2a2a",
            fg="#00ff00",
            font=("微软雅黑", 7),
            width=8,
            height=1
        )
        cooldown_label.pack(padx=2, pady=1)
        
        # 状态指示器
        status_label = tk.Label(
            skill_frame,
            text="●",
            bg="#2a2a2a",
            fg="#00ff00",
            font=("微软雅黑", 10)
        )
        status_label.pack()
        
        self.skill_labels[skill_name] = {
            'frame': skill_frame,
            'name': name_label,
            'cooldown': cooldown_label,
            'status': status_label
        }
        
    def _remove_skill_display(self, skill_name):
        """移除技能显示组件"""
        if skill_name in self.skill_labels:
            self.skill_labels[skill_name]['frame'].destroy()
            del self.skill_labels[skill_name]
    
    def _load_auto_cast_config(self):
        """加载自动释放配置"""
        try:
            config_path = os.path.join("saves", "auto_cast_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 更新启用的技能列表
                old_skills = set(self.enabled_skills.keys())
                new_skills = set()
                
                for skill_name, enabled in config.items():
                    if enabled:
                        self.enabled_skills[skill_name] = True
                        new_skills.add(skill_name)
                        
                        # 如果是新技能，创建显示组件
                        if skill_name not in old_skills:
                            self._create_skill_display(skill_name)
                    else:
                        # 如果技能被禁用，从列表中移除
                        if skill_name in self.enabled_skills:
                            del self.enabled_skills[skill_name]
                
                # 移除已被禁用的技能显示
                for skill_name in old_skills - new_skills:
                    self._remove_skill_display(skill_name)
                    
                print(f"已加载自动释放配置，启用技能: {list(self.enabled_skills.keys())}")
                
        except Exception as e:
            print(f"加载自动释放配置失败: {e}")
    
    def _start_config_monitor(self):
        """启动配置文件监控"""
        def monitor_config():
            last_modified = 0
            config_path = os.path.join("saves", "auto_cast_config.json")
            
            while True:
                try:
                    if os.path.exists(config_path):
                        current_modified = os.path.getmtime(config_path)
                        if current_modified > last_modified:
                            last_modified = current_modified
                            # 在主线程中重新加载配置
                            self.after(0, self._load_auto_cast_config)
                    
                    time.sleep(1)  # 每秒检查一次
                except Exception as e:
                    print(f"监控配置文件失败: {e}")
                    time.sleep(5)  # 出错时等待5秒
        
        monitor_thread = threading.Thread(target=monitor_config, daemon=True)
        monitor_thread.start()
    
    def _start_auto_cast_timer(self):
        """启动自动释放定时器"""
        self._auto_cast_cycle()
        
    def _auto_cast_cycle(self):
        """自动释放循环"""
        if self.auto_cast_running:
            # 尝试释放所有已启用且冷却完成的技能
            for skill_name in list(self.enabled_skills.keys()):
                self._try_cast_skill(skill_name)
            
            # 更新冷却显示
            self._update_cooldown_display()
        
        # 每0.5秒执行一次
        self.after(500, self._auto_cast_cycle)
    
    def _try_cast_skill(self, skill_name):
        """尝试释放技能"""
        if not self.player or not hasattr(self.player, 'skill_manager'):
            return
        
        # 检查技能冷却
        if skill_name in self.skill_cooldowns:
            remaining = self.skill_cooldowns[skill_name] - time.time()
            if remaining > 0:
                return  # 还在冷却中
        
        # 检查技能是否被动技能（被动技能不需要释放）
        skill_data = self._get_skill_data(skill_name)
        if skill_data and skill_data.get('type') == '被动':
            return
        
        # 尝试使用技能
        try:
            result = self.player.skill_manager.use_skill(skill_name, target=None, player=self.player)
            
            if result['success']:
                # 设置冷却时间
                cooldown = self._get_skill_cooldown(skill_name)
                if cooldown > 0:
                    self.skill_cooldowns[skill_name] = time.time() + cooldown
                
                print(f"自动释放技能: {skill_name} - {result['message']}")
                
                # 更新状态显示
                self._update_skill_status(skill_name, "释放", "#ffff00")
            else:
                print(f"技能释放失败: {skill_name} - {result['message']}")
                
        except Exception as e:
            print(f"自动释放技能 {skill_name} 时出错: {e}")
    
    def _get_skill_data(self, skill_name):
        """获取技能数据"""
        try:
            from game.models.skill_loader import skill_loader
            # 遍历所有技能分类查找技能
            for category in skill_loader.get_skill_categories():
                skills = skill_loader.get_skills_by_category(category)
                for skill_key, skill_data in skills.items():
                    if skill_data.get('name') == skill_name:
                        return skill_data
        except Exception as e:
            print(f"获取技能数据失败: {e}")
        return None
    
    def _get_skill_cooldown(self, skill_name):
        """获取技能冷却时间"""
        skill_data = self._get_skill_data(skill_name)
        return skill_data.get('cooldown', 0) if skill_data else 0
    
    def _update_cooldown_display(self):
        """更新冷却时间显示"""
        current_time = time.time()
        
        for skill_name in self.enabled_skills:
            if skill_name in self.skill_labels:
                cooldown_label = self.skill_labels[skill_name]['cooldown']
                status_label = self.skill_labels[skill_name]['status']
                
                if skill_name in self.skill_cooldowns:
                    remaining = self.skill_cooldowns[skill_name] - current_time
                    if remaining > 0:
                        cooldown_label.configure(text=f"{remaining:.1f}s", fg="#ff6666")
                        status_label.configure(fg="#ff6666")
                    else:
                        cooldown_label.configure(text="就绪", fg="#00ff00")
                        status_label.configure(fg="#00ff00")
                        del self.skill_cooldowns[skill_name]
                else:
                    cooldown_label.configure(text="就绪", fg="#00ff00")
                    status_label.configure(fg="#00ff00")
    
    def _update_skill_status(self, skill_name, status, color):
        """更新技能状态显示"""
        if skill_name in self.skill_labels:
            status_label = self.skill_labels[skill_name]['status']
            status_label.configure(fg=color)
            
            # 0.5秒后恢复正常颜色
            self.after(500, lambda: status_label.configure(fg="#00ff00"))
    
    def _toggle_auto_cast(self):
        """切换自动释放状态"""
        self.auto_cast_running = not self.auto_cast_running
        
        if self.auto_cast_running:
            self.pause_btn.configure(text="暂停自动释放", bg="#ff6666")
            print("自动释放已启用")
        else:
            self.pause_btn.configure(text="继续自动释放", bg="#00ff00")
            print("自动释放已暂停")
    
    def _manual_cast_all(self):
        """手动释放所有技能"""
        for skill_name in list(self.enabled_skills.keys()):
            self._try_cast_skill(skill_name)
        print("已尝试释放所有启用的技能")


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建自动技能栏
    hotbar = AutoSkillHotbar(root)
    
    # 启动主循环
    root.mainloop() 