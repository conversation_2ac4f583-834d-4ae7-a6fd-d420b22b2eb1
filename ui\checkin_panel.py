import pygame
import sys
import os
from datetime import datetime, date
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from game.managers.checkin_manager import CheckinManager
except ImportError:
    # 如果无法导入，创建一个简单的占位符
    class CheckinManager:
        def __init__(self):
            pass
        def can_checkin_today(self, player):
            return True
        def checkin(self, player):
            return {"success": True, "message": "签到成功！"}
        def get_checkin_calendar(self, player):
            return {}
        def get_daily_reward_config(self):
            return {"gold": 1000, "points": 10}
        def get_consecutive_reward_config(self, days):
            return None
        def get_monthly_reward_config(self, days):
            return None

class CheckinPanel:
    """
    签到面板UI组件
    显示签到日历、奖励信息和签到按钮
    """
    
    def __init__(self, screen, font_manager, ui_manager, player=None):
        """
        初始化签到面板
        
        Args:
            screen: pygame屏幕对象
            font_manager: 字体管理器
            ui_manager: UI管理器
            player: 玩家对象
        """
        self.screen = screen
        self.font_manager = font_manager
        self.ui_manager = ui_manager
        self.player = player
        self.checkin_manager = CheckinManager()
        
        # 面板尺寸和位置
        self.panel_width = 600
        self.panel_height = 500
        self.panel_x = (screen.get_width() - self.panel_width) // 2
        self.panel_y = (screen.get_height() - self.panel_height) // 2
        
        # 颜色定义 - 统一游戏深色风格
        self.colors = {
            'background': (30, 30, 30),
            'panel_bg': (50, 50, 50),
            'border': (100, 100, 100),
            'button_normal': (70, 70, 70),
            'button_hover': (90, 90, 90),
            'button_disabled': (40, 40, 40),
            'button_special': (80, 120, 160),  # 积分商城按钮特殊颜色
            'button_special_hover': (100, 140, 180),
            'text_primary': (255, 255, 255),
            'text_secondary': (200, 200, 200),
            'success': (76, 175, 80),
            'warning': (255, 193, 7),
            'error': (244, 67, 54),
            'calendar_checked': (76, 175, 80),
            'calendar_today': (255, 193, 7),
            'calendar_normal': (70, 70, 70)
        }
        
        # 按钮状态
        self.checkin_button_rect = None
        self.close_button_rect = None
        self.points_shop_button_rect = None
        self.button_hover_state = {'checkin': False, 'close': False, 'points_shop': False}
        
        # 签到状态
        self.last_checkin_result = None
        self.calendar_data = None
        
        # 初始化数据
        self.refresh_data()
    
    def refresh_data(self):
        """
        刷新签到数据
        """
        try:
            # 获取玩家对象（优先使用传入的玩家对象，否则从UI管理器获取）
            player = self.player or getattr(self.ui_manager, 'player', None)
            if player:
                self.calendar_data = self.checkin_manager.get_checkin_calendar(player)
            else:
                self.calendar_data = None
        except Exception as e:
            print(f"刷新签到数据时出错: {e}")
            self.calendar_data = None
    
    def handle_event(self, event):
        """
        处理事件
        
        Args:
            event: pygame事件
            
        Returns:
            bool: 是否处理了事件
        """
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # 左键点击
                mouse_pos = pygame.mouse.get_pos()
                
                # 检查签到按钮点击
                if self.checkin_button_rect and self.checkin_button_rect.collidepoint(mouse_pos):
                    self.perform_checkin()
                    return True
                
                # 检查积分商城按钮点击
                if self.points_shop_button_rect and self.points_shop_button_rect.collidepoint(mouse_pos):
                    self.open_points_shop()
                    return True
                
                # 检查关闭按钮点击
                if self.close_button_rect and self.close_button_rect.collidepoint(mouse_pos):
                    return False  # 返回False表示关闭面板
        
        elif event.type == pygame.MOUSEMOTION:
            # 更新按钮悬停状态
            mouse_pos = pygame.mouse.get_pos()
            
            if self.checkin_button_rect:
                self.button_hover_state['checkin'] = self.checkin_button_rect.collidepoint(mouse_pos)
            
            if self.close_button_rect:
                self.button_hover_state['close'] = self.close_button_rect.collidepoint(mouse_pos)
            
            if self.points_shop_button_rect:
                self.button_hover_state['points_shop'] = self.points_shop_button_rect.collidepoint(mouse_pos)
        
        return True
    
    def perform_checkin(self):
        """
        执行签到操作
        """
        try:
            # 获取玩家对象（优先使用传入的玩家对象，否则从UI管理器获取）
            player = self.player or getattr(self.ui_manager, 'player', None)
            if not player:
                self.last_checkin_result = {
                    'success': False,
                    'message': '无法获取玩家信息',
                    'rewards': {}
                }
                return
            
            # 执行签到
            result = self.checkin_manager.perform_checkin(player)
            self.last_checkin_result = result
            
            # 刷新数据
            self.refresh_data()
            
            # 如果签到成功，显示奖励信息并触发保存
            if result['success']:
                print(f"签到成功: {result['message']}")
                if result['rewards']:
                    print(f"获得奖励: {result['rewards']}")
                
                # 触发自动保存
                if hasattr(self.ui_manager, 'save_manager') and self.ui_manager.save_manager:
                    try:
                        self.ui_manager.save_manager.save_player_data(player, "autosave")
                        print("✅ 签到后自动保存成功")
                    except Exception as save_error:
                        print(f"❌ 签到后自动保存失败: {save_error}")
                else:
                    # 🔧 备用保存方案：使用SaveLoadManager直接保存
                    try:
                        import sys
                        import os
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                        from game.managers.save_load_manager import SaveLoadManager
                        save_manager = SaveLoadManager()
                        save_manager.save_player_data(player, "autosave")
                        print("✅ 签到后备用保存方案成功")
                    except Exception as save_error:
                        print(f"❌ 签到后备用保存失败: {save_error}")
                        # 最后的保存尝试
                        if hasattr(self.ui_manager, 'player') and hasattr(self.ui_manager.player, 'save'):
                            try:
                                self.ui_manager.player.save()
                                print("✅ 签到后玩家数据保存成功")
                            except Exception as final_save_error:
                                print(f"❌ 签到后玩家数据保存失败: {final_save_error}")
                                print("⚠️ 警告：签到成功但无法保存数据，可能会丢失！")
            else:
                print(f"签到失败: {result['message']}")
                
        except Exception as e:
            print(f"执行签到时出错: {e}")
            self.last_checkin_result = {
                'success': False,
                'message': '签到失败，请稍后重试',
                'rewards': {}
            }
    
    def can_checkin_today(self) -> bool:
        """
        检查今天是否可以签到
        
        Returns:
            bool: 是否可以签到
        """
        try:
            player = self.player or getattr(self.ui_manager, 'player', None)
            if not player:
                return False
            return self.checkin_manager.can_checkin_today(player)
        except Exception as e:
            print(f"检查签到状态时出错: {e}")
            return False
    
    def open_points_shop(self):
        """
        打开积分商城
        """
        try:
            print("打开积分商城")
            # 调用UI管理器显示积分商城面板
            if hasattr(self.ui_manager, 'show_points_shop'):
                self.ui_manager.show_points_shop()
            else:
                print("UI管理器不支持积分商城功能")
            # if hasattr(self.ui_manager, 'toggle_points_shop_panel'):
            #     self.ui_manager.toggle_points_shop_panel()
        except Exception as e:
            print(f"打开积分商城时出错: {e}")
    
    def update(self, current_time):
        """
        更新签到面板状态
        
        Args:
            current_time: 当前时间
        """
        if not self.visible:
            return
            
        # 这里可以添加需要定期更新的逻辑
        # 比如检查是否需要刷新签到状态、更新动画等
        pass
    
    def render(self):
        """
        渲染签到面板
        """
        try:
            # 绘制面板背景
            panel_rect = pygame.Rect(self.panel_x, self.panel_y, self.panel_width, self.panel_height)
            pygame.draw.rect(self.screen, self.colors['panel_bg'], panel_rect)
            pygame.draw.rect(self.screen, self.colors['border'], panel_rect, 2)
            
            # 绘制标题
            self._render_title()
            
            # 绘制签到状态信息
            self._render_status_info()
            
            # 绘制签到日历
            self._render_calendar()
            
            # 绘制签到按钮
            self._render_checkin_button()
            
            # 绘制关闭按钮
            self._render_close_button()
            
            # 绘制积分商城按钮
            self._render_points_shop_button()
            
            # 绘制奖励信息（如果有）
            self._render_reward_info()
            
        except Exception as e:
            print(f"渲染签到面板时出错: {e}")
    
    def _render_title(self):
        """
        渲染标题
        """
        title_font = self.font_manager.get_font('default', 24)
        title_text = title_font.render('每日签到', True, self.colors['text_primary'])
        title_rect = title_text.get_rect()
        title_rect.centerx = self.panel_x + self.panel_width // 2
        title_rect.y = self.panel_y + 20
        self.screen.blit(title_text, title_rect)
    
    def _render_status_info(self):
        """
        渲染签到状态信息
        """
        info_font = self.font_manager.get_font('default', 16)
        y_offset = self.panel_y + 60
        
        if self.calendar_data:
            # 连续签到天数
            consecutive_text = f"连续签到: {self.calendar_data['consecutive_days']} 天"
            consecutive_surface = info_font.render(consecutive_text, True, self.colors['text_secondary'])
            self.screen.blit(consecutive_surface, (self.panel_x + 20, y_offset))
            
            # 总签到天数
            total_text = f"总签到: {self.calendar_data['total_days']} 天"
            total_surface = info_font.render(total_text, True, self.colors['text_secondary'])
            self.screen.blit(total_surface, (self.panel_x + 200, y_offset))
            
            # 本月签到天数
            monthly_text = f"本月: {self.calendar_data['monthly_count']} 天"
            monthly_surface = info_font.render(monthly_text, True, self.colors['text_secondary'])
            self.screen.blit(monthly_surface, (self.panel_x + 350, y_offset))
    
    def _render_calendar(self):
        """
        渲染签到日历
        """
        if not self.calendar_data:
            return
        
        calendar_y = self.panel_y + 100
        calendar_width = 400
        calendar_height = 200
        calendar_x = self.panel_x + (self.panel_width - calendar_width) // 2
        
        # 绘制日历背景
        calendar_rect = pygame.Rect(calendar_x, calendar_y, calendar_width, calendar_height)
        pygame.draw.rect(self.screen, self.colors['background'], calendar_rect)
        pygame.draw.rect(self.screen, self.colors['border'], calendar_rect, 1)
        
        # 绘制月份标题
        month_font = self.font_manager.get_font('default', 18)
        month_text = f"{self.calendar_data['year']}年{self.calendar_data['month']}月"
        month_surface = month_font.render(month_text, True, self.colors['text_primary'])
        month_rect = month_surface.get_rect()
        month_rect.centerx = calendar_x + calendar_width // 2
        month_rect.y = calendar_y + 10
        self.screen.blit(month_surface, month_rect)
        
        # 绘制日期格子
        day_font = self.font_manager.get_font('default', 14)
        cell_width = 40
        cell_height = 25
        cols = 7
        start_x = calendar_x + (calendar_width - cols * cell_width) // 2
        start_y = calendar_y + 40
        
        for i, day_data in enumerate(self.calendar_data['days']):
            if i >= 28:  # 限制显示前28天
                break
                
            row = i // cols
            col = i % cols
            
            cell_x = start_x + col * cell_width
            cell_y = start_y + row * cell_height
            cell_rect = pygame.Rect(cell_x, cell_y, cell_width - 2, cell_height - 2)
            
            # 选择颜色
            if day_data['is_today']:
                color = self.colors['calendar_today']
            elif day_data['is_checked']:
                color = self.colors['calendar_checked']
            else:
                color = self.colors['calendar_normal']
            
            # 绘制格子
            pygame.draw.rect(self.screen, color, cell_rect)
            pygame.draw.rect(self.screen, self.colors['border'], cell_rect, 1)
            
            # 绘制日期数字
            day_text = str(day_data['day'])
            day_surface = day_font.render(day_text, True, self.colors['text_primary'])
            day_rect = day_surface.get_rect()
            day_rect.center = cell_rect.center
            self.screen.blit(day_surface, day_rect)
    
    def _render_checkin_button(self):
        """
        渲染签到按钮
        """
        button_width = 120
        button_height = 40
        button_x = self.panel_x + (self.panel_width - button_width) // 2
        button_y = self.panel_y + 320
        
        self.checkin_button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        
        # 确定按钮状态和颜色
        can_checkin = self.can_checkin_today()
        
        if not can_checkin:
            button_color = self.colors['button_disabled']
            button_text = "已签到"
        elif self.button_hover_state['checkin']:
            button_color = self.colors['button_hover']
            button_text = "立即签到"
        else:
            button_color = self.colors['button_normal']
            button_text = "立即签到"
        
        # 绘制按钮
        pygame.draw.rect(self.screen, button_color, self.checkin_button_rect)
        pygame.draw.rect(self.screen, self.colors['border'], self.checkin_button_rect, 2)
        
        # 绘制按钮文字
        button_font = self.font_manager.get_font('default', 16)
        text_surface = button_font.render(button_text, True, (255, 255, 255))
        text_rect = text_surface.get_rect()
        text_rect.center = self.checkin_button_rect.center
        self.screen.blit(text_surface, text_rect)
    
    def _render_close_button(self):
        """
        渲染关闭按钮
        """
        button_size = 30
        button_x = self.panel_x + self.panel_width - button_size - 10
        button_y = self.panel_y + 10
        
        self.close_button_rect = pygame.Rect(button_x, button_y, button_size, button_size)
        
        # 选择颜色
        if self.button_hover_state['close']:
            button_color = self.colors['error']
        else:
            button_color = self.colors['button_normal']
        
        # 绘制按钮
        pygame.draw.rect(self.screen, button_color, self.close_button_rect)
        pygame.draw.rect(self.screen, self.colors['border'], self.close_button_rect, 2)
        
        # 绘制X符号
        button_font = self.font_manager.get_font('default', 16)
        text_surface = button_font.render('×', True, (255, 255, 255))
        text_rect = text_surface.get_rect()
        text_rect.center = self.close_button_rect.center
        self.screen.blit(text_surface, text_rect)
    
    def _render_points_shop_button(self):
        """
        渲染积分商城按钮
        """
        button_width = 120
        button_height = 35
        button_x = self.panel_x + self.panel_width - button_width - 20
        button_y = self.panel_y + 320
        
        self.points_shop_button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        
        # 选择颜色
        if self.button_hover_state['points_shop']:
            button_color = self.colors['button_special_hover']
        else:
            button_color = self.colors['button_special']
        
        # 绘制按钮
        pygame.draw.rect(self.screen, button_color, self.points_shop_button_rect)
        pygame.draw.rect(self.screen, self.colors['border'], self.points_shop_button_rect, 2)
        
        # 绘制按钮文字
        button_font = self.font_manager.get_font('default', 14)
        text_surface = button_font.render('积分商城', True, (255, 255, 255))
        text_rect = text_surface.get_rect()
        text_rect.center = self.points_shop_button_rect.center
        self.screen.blit(text_surface, text_rect)
    
    def _render_reward_info(self):
        """
        渲染奖励信息
        """
        if not self.last_checkin_result:
            return
        
        info_y = self.panel_y + 380
        info_font = self.font_manager.get_font('default', 14)
        
        # 显示签到结果消息
        message = self.last_checkin_result.get('message', '')
        if message:
            color = self.colors['success'] if self.last_checkin_result['success'] else self.colors['error']
            message_surface = info_font.render(message, True, color)
            message_rect = message_surface.get_rect()
            message_rect.centerx = self.panel_x + self.panel_width // 2
            message_rect.y = info_y
            self.screen.blit(message_surface, message_rect)
        
        # 显示奖励详情
        rewards = self.last_checkin_result.get('rewards', {})
        if rewards:
            reward_y = info_y + 25
            reward_texts = []
            
            if 'gold' in rewards:
                reward_texts.append(f"金币 +{rewards['gold']}")
            if 'points' in rewards:
                reward_texts.append(f"积分 +{rewards['points']}")
            if 'yuanbao' in rewards:
                reward_texts.append(f"元宝 +{rewards['yuanbao']}")
            if 'items' in rewards:
                for item in rewards['items']:
                    reward_texts.append(f"{item['name']} x{item['count']}")
            
            # 显示奖励文本
            reward_text = "  ".join(reward_texts)
            if reward_text:
                reward_surface = info_font.render(f"获得奖励: {reward_text}", True, self.colors['success'])
                reward_rect = reward_surface.get_rect()
                reward_rect.centerx = self.panel_x + self.panel_width // 2
                reward_rect.y = reward_y
                self.screen.blit(reward_surface, reward_rect)
    
    def get_daily_reward_info(self) -> str:
        """
        获取每日奖励信息文本
        
        Returns:
            str: 奖励信息
        """
        try:
            daily_reward = self.checkin_manager.config.get('daily_reward', {})
            gold = daily_reward.get('gold', 0)
            points = daily_reward.get('points', 0)
            return f"每日签到可获得: 金币 {gold}, 积分 {points}"
        except Exception as e:
            print(f"获取每日奖励信息时出错: {e}")
            return "每日签到可获得丰厚奖励！"