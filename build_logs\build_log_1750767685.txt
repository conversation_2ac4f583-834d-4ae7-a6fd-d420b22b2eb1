[2025-06-24 20:21:25] [INFO] 🚀 开始萝卜传奇游戏打包检查...
[2025-06-24 20:21:25] [INFO] 项目路径: E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹
[2025-06-24 20:21:25] [INFO] 
🔍 检查Python模块...
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/__init__.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/core/game.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/core/data_manager.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/core/resource_manager.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/core/battle_manager.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/core/map_manager.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/ui/ui_manager.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/models/player_refactored.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/models/enemy.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/managers/save_load_manager.py (核心模块)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: main.py (核心模块)
[2025-06-24 20:21:25] [INFO] 
🎨 检查游戏资源...
[2025-06-24 20:21:25] [INFO] ✅ 找到目录: game/assets/images (包含318个文件) (游戏资源)
[2025-06-24 20:21:25] [INFO] ✅ 找到目录: game/assets/images/characters (包含6个文件) (游戏资源)
[2025-06-24 20:21:25] [INFO] ✅ 找到目录: game/assets/images/enemies (包含65个文件) (游戏资源)
[2025-06-24 20:21:25] [INFO] ✅ 找到目录: game/assets/images/equipment (包含211个文件) (游戏资源)
[2025-06-24 20:21:25] [INFO] ✅ 找到目录: game/assets/images/ui (包含24个文件) (游戏资源)
[2025-06-24 20:21:25] [INFO] ✅ 找到目录: game/assets/images/maps (包含10个文件) (游戏资源)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/assets/images/icon.png (重要资源)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/assets/images/background.png (重要资源)
[2025-06-24 20:21:25] [INFO] 
📊 检查游戏数据文件...
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/monsters.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/monsters.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/equipmengt.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/equipmengt.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/skills.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/skills.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/maps_config.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/maps_config.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/items_config.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/items_config.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/player.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/player.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/potion_config.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/potion_config.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/starter_equipment.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/starter_equipment.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/summons.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/summons.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/points_shop_config.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/points_shop_config.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/checkin_config.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/checkin_config.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/monster_distribution_config.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/monster_distribution_config.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/monsterskills.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/monsterskills.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/ranks.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/ranks.json
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: game/data/drop_rates.json (数据配置)
[2025-06-24 20:21:25] [INFO] ✅ JSON格式验证通过: game/data/drop_rates.json
[2025-06-24 20:21:25] [INFO] 
📦 检查依赖文件...
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: requirements.txt (Python依赖)
[2025-06-24 20:21:25] [INFO] ✅ 找到文件: README.md (项目说明)
[2025-06-24 20:21:25] [INFO] ✅ pygame 模块已安装
[2025-06-24 20:21:26] [INFO] ✅ tkinter 模块已安装
[2025-06-24 20:21:26] [INFO] 
📋 生成详细报告...
[2025-06-24 20:21:26] [INFO] ✅ 报告已生成: E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\build_logs\build_report_1750767686.txt
[2025-06-24 20:21:26] [INFO] 
📝 创建带控制台的spec文件...
[2025-06-24 20:21:26] [INFO] ✅ 创建调试版spec文件: E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\萝卜传奇_调试版.spec
[2025-06-24 20:21:55] [INFO] 
🔨 开始构建可执行文件...
[2025-06-24 20:21:55] [INFO] 🧹 清理旧的build目录
[2025-06-24 20:21:55] [INFO] 🧹 清理旧的dist目录
[2025-06-24 20:21:55] [INFO] 执行命令: C:\Python313\python.exe -m PyInstaller --clean E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\萝卜传奇_调试版.spec
[2025-06-24 20:21:56] [ERROR] ❌ 构建失败
[2025-06-24 20:21:56] [ERROR] 错误输出: Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\__main__.py", line 321, in <module>
    run()
    ~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\__main__.py", line 172, in run
    parser = generate_parser()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\__main__.py", line 137, in generate_parser
    import PyInstaller.building.build_main
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\building\build_main.py", line 28, in <module>
    from PyInstaller.building.api import COLLECT, EXE, MERGE, PYZ
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\building\api.py", line 34, in <module>
    from PyInstaller.building.splash import Splash  # argument type validation in EXE
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\building\splash.py", line 24, in <module>
    from PyInstaller.utils.hooks.tcl_tk import tcltk_info
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\utils\hooks\__init__.py", line 26, in <module>
    from PyInstaller.depend.imphookapi import PostGraphAPI
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\depend\imphookapi.py", line 21, in <module>
    from PyInstaller.lib.modulegraph.modulegraph import (RuntimeModule, RuntimePackage)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 43, in <module>
    from altgraph.ObjectGraph import ObjectGraph
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\altgraph\__init__.py", line 142, in <module>
    import pkg_resources
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pkg_resources\__init__.py", line 2172, in <module>
    register_finder(pkgutil.ImpImporter, find_on_path)
                    ^^^^^^^^^^^^^^^^^^^
AttributeError: module 'pkgutil' has no attribute 'ImpImporter'. Did you mean: 'zipimporter'?

