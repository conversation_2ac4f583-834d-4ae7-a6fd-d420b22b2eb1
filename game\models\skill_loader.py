import json
import os
import logging
from game.core.resource_manager import get_game_data_path

class SkillLoader:
    """技能加载器类，负责从JSON文件加载技能数据并提供访问方法"""

    def __init__(self, skills_file="skills.json"):
        """
        初始化技能加载器

        Args:
            skills_file: 技能数据文件路径
        """
        self.skills_file = get_game_data_path(skills_file)
        self.skills_data = {} # 顶层是职业: {技能标识符: 技能详情}
        self.skill_categories = [] # 存储职业名称 ["warrior", "taoist", "mage"]
        self.all_skill_ids = []
        
        # 设置日志记录
        self.logger = logging.getLogger(__name__)
        
        self.load_skills()

    def load_skills(self):
        """从JSON文件加载技能数据"""
        try:
            # 确保目录存在，如果不存在则创建
            file_dir = os.path.dirname(self.skills_file)
            if file_dir and not os.path.exists(file_dir): # 检查file_dir是否为空
                os.makedirs(file_dir)
                self.logger.info(f"创建目录: {file_dir}")

            if os.path.exists(self.skills_file):
                with open(self.skills_file, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)
                    
                    # 转换数组格式到对象格式以便兼容现有代码
                    self.skills_data = {}
                    self.all_skill_ids = []
                    
                    for category, skills_list in raw_data.items():
                        if isinstance(skills_list, list):
                            # 将数组转换为对象，使用skill_id作为键
                            skills_dict = {}
                            for skill in skills_list:
                                if 'id' in skill:
                                    skill_key = f"skill_{skill['id']}"
                                    skills_dict[skill_key] = skill
                                    self.all_skill_ids.append(skill['id'])
                            self.skills_data[category] = skills_dict
                        else:
                            # 如果已经是对象格式，直接使用
                            self.skills_data[category] = skills_list
                            for skill_data in skills_list.values():
                                if 'id' in skill_data:
                                    self.all_skill_ids.append(skill_data['id'])
                    
                    self.skill_categories = list(self.skills_data.keys())
                    self.logger.info(f"技能数据加载成功: {len(self.skill_categories)} 个职业，共 {len(self.all_skill_ids)} 个技能。")
            else:
                self.logger.info(f"技能文件不存在: {self.skills_file}，将创建一个空的技能数据结构。")
                self.skills_data = {} # 初始化为空字典
                self.skill_categories = []
                self.all_skill_ids = []
                # 可选：如果文件不存在，可以考虑创建一个空的JSON文件
                # self.save_skills()
        except Exception as e:
            self.logger.error(f"加载技能数据出错: {e}")
            self.skills_data = {}
            self.skill_categories = []
            self.all_skill_ids = []

    def save_skills(self):
        """保存技能数据到JSON文件"""
        try:
            file_dir = os.path.dirname(self.skills_file)
            if file_dir and not os.path.exists(file_dir):
                 os.makedirs(file_dir, exist_ok=True)

            with open(self.skills_file, 'w', encoding='utf-8') as f:
                json.dump(self.skills_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"技能数据已保存到: {self.skills_file}")
            return True
        except Exception as e:
            self.logger.error(f"保存技能数据出错: {e}")
            return False

    def get_skill_categories(self):
        """
        获取所有技能类别 (职业)

        Returns:
            技能类别列表 (职业列表)
        """
        return self.skill_categories

    def get_skills_by_category(self, category):
        """
        获取指定类别的所有技能 (以字典形式返回，键为技能标识符)

        Args:
            category: 技能类别 (职业)

        Returns:
            技能字典 {skill_identifier: skill_data}，如果类别不存在则返回空字典
        """
        return self.skills_data.get(category, {})

    def get_skill_by_identifier(self, category, skill_identifier):
        """
        根据技能标识符获取指定类别中的技能

        Args:
            category: 技能类别 (职业)
            skill_identifier: 技能的键 (如 "fireball", "basic_sword")

        Returns:
            技能数据字典，如果不存在则返回None
        """
        return self.skills_data.get(category, {}).get(skill_identifier)

    def get_skill_by_id(self, category, skill_id):
        """
        根据技能的 "id" 属性获取指定类别中的技能

        Args:
            category: 技能类别 (职业)
            skill_id: 技能内部的 "id" 属性值

        Returns:
            技能数据字典，如果不存在则返回None
        """
        category_skills = self.get_skills_by_category(category) # 获取该职业下的技能字典
        
        # 首先尝试直接通过键访问（新格式）
        skill_key = f"skill_{skill_id}"
        if skill_key in category_skills:
            return category_skills[skill_key]
        
        # 如果没找到，遍历所有技能查找（兼容旧格式）
        for skill_data in category_skills.values():
            if skill_data.get("id") == skill_id:
                return skill_data
        return None
    
    def get_skill_identifier_by_id(self, category, skill_id):
        """
        根据技能的 "id" 属性获取其在JSON中的键 (标识符)

        Args:
            category: 技能类别 (职业)
            skill_id: 技能内部的 "id" 属性值

        Returns:
            技能标识符 (键)，如果不存在则返回None
        """
        category_skills = self.get_skills_by_category(category)
        for identifier, skill_data in category_skills.items():
            if skill_data.get("id") == skill_id:
                return identifier
        return None


    def get_skill_by_name(self, category, skill_name):
        """
        根据技能的 "name" 属性获取指定类别中的技能

        Args:
            category: 技能类别 (职业)
            skill_name: 技能内部的 "name" 属性值

        Returns:
            技能数据字典，如果不存在则返回None
        """
        category_skills = self.get_skills_by_category(category)
        for skill_data in category_skills.values(): # 迭代技能数据字典的值
            if skill_data.get("name") == skill_name:
                return skill_data
        return None

    def update_skill_by_identifier(self, category, skill_identifier, updated_data):
        """
        通过技能标识符更新技能数据

        Args:
            category: 技能类别
            skill_identifier: 技能的键 (如 "fireball")
            updated_data: 更新的数据字典

        Returns:
            是否成功更新
        """
        if category not in self.skills_data or skill_identifier not in self.skills_data[category]:
            self.logger.error(f"错误：无法找到技能 {category}/{skill_identifier} 进行更新。")
            return False

        skill_to_update = self.skills_data[category][skill_identifier]
        for key, value in updated_data.items():
            skill_to_update[key] = value
        # self.skills_data[category][skill_identifier] = skill_to_update # 其实是原地修改，这行可以省略
        self.logger.info(f"技能 {category}/{skill_identifier} 已更新。")
        return True

    def update_skill_by_id(self, category, skill_id, updated_data):
        """
        通过技能的 "id" 属性更新技能数据

        Args:
            category: 技能类别
            skill_id: 技能的 "id" 属性值
            updated_data: 更新的数据字典

        Returns:
            是否成功更新
        """
        skill_identifier = self.get_skill_identifier_by_id(category, skill_id)
        if skill_identifier:
            return self.update_skill_by_identifier(category, skill_identifier, updated_data)
        else:
            self.logger.error(f"错误：无法找到ID为 {skill_id} 的技能在类别 {category} 中进行更新。")
            return False


    def learn_skill(self, category, skill_id):
        """
        学习技能 (通过技能 "id" 属性)

        Args:
            category: 技能类别
            skill_id: 技能的 "id" 属性值

        Returns:
            是否成功学习
        """
        return self.update_skill_by_id(category, skill_id, {"learned": True, "level": 1})

    def upgrade_skill(self, category, skill_id):
        """
        升级技能 (通过技能 "id" 属性)

        Args:
            category: 技能类别
            skill_id: 技能的 "id" 属性值

        Returns:
            是否成功升级
        """
        skill = self.get_skill_by_id(category, skill_id) # 这个方法现在应该能正确工作了
        if not skill:
            self.logger.error(f"升级失败：找不到技能 {category} ID {skill_id}")
            return False

        if not skill.get("learned", False):
            self.logger.error(f"升级失败：技能 {skill.get('name')} 尚未学习")
            return False

        current_level = skill.get("level", 0)
        max_level = skill.get("max_level", 3) # 你的数据中 max_level 是 3

        if current_level >= max_level:
            self.logger.error(f"升级失败：技能 {skill.get('name')} 已达到最高等级 {max_level}")
            return False
        
        # 升级前检查角色等级是否满足下一级技能的要求
        level_requirements = skill.get("level_requirements", [])
        if current_level < len(level_requirements): # 确保索引不越界
            next_skill_level_index = current_level # 因为level_requirements是对应升到1,2,3级
            # current_level=0, 表示升1级，查level_requirements[0]
            # current_level=1, 表示升2级，查level_requirements[1]
            # current_level=2, 表示升3级，查level_requirements[2]
            
            # 假设你有一个获取当前角色等级的方法
            # player_level = get_current_player_level() # 你需要实现这个
            # if player_level < level_requirements[next_skill_level_index]:
            #     print(f"升级失败：角色等级不足以学习技能 {skill.get('name')} 的下一等级。需要等级 {level_requirements[next_skill_level_index]}")
            #     return False
            pass # 这里暂时注释掉角色等级检查，因为SkillLoader不知道角色等级

        self.logger.info(f"技能 {skill.get('name')} 从等级 {current_level} 升级到 {current_level + 1}")
        return self.update_skill_by_id(category, skill_id, {"level": current_level + 1})

# 创建全局技能加载器实例
# 确保 skills.json 文件路径正确
skill_loader = SkillLoader()

# --- 示例用法 ---
if __name__ == "__main__":
    # 确保 skills.json 文件存在且路径正确
    # 为了测试，我们先手动创建一个示例的 skills.json (如果不存在)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    skills_json_path = os.path.join(current_dir, "game", "data", "skills.json")
    test_skills_data = {
      "warrior": {
        "basic_sword": {
          "id": 201,
          "name": "基本剑术",
          "level": 0,
          "learned": False,
          "type": "passive",
          "description": "精通基本剑术，提高准确性",
          "max_level": 3,
          "level_requirements": [7, 11, 16],
          "book_pages": 50,
          "enchant_percent": 0,
          "icon_path": "game/assets/images/ui/skill/warrior/基本剑术.png",
          "effects": [
            {"type": "accuracy_boost", "value": 3},
            {"type": "accuracy_boost", "value": 6},
            {"type": "accuracy_boost", "value": 9}
          ],
          "mp_cost": 0,
          "cooldown": 0
        }
      },
      "mage": {
        "fireball": {
          "id": 101,
          "name": "火球术",
          "level": 0,
          "learned": False,
          "type": "active",
          "description": "凝聚自身魔力发射一枚火球攻击目标，\\n造成远程火焰伤害",
          "max_level": 3,
          "level_requirements": [7, 11, 16],
          "book_pages": 100,
          "enchant_percent": 0,
          "icon_path": "game/assets/images/ui/skill/mage/火球术.png",
          "effects": [
            {"type": "direct_damage", "value": 7, "magic_ratio": 0.9, "element": "fire"},
            {"type": "direct_damage", "value": 14, "magic_ratio": 0.9, "element": "fire"},
            {"type": "direct_damage", "value": 22, "magic_ratio": 0.9, "element": "fire"}
          ],
          "mp_cost": 10,
          "cooldown": 3
        }
      }
    }
    # 确保测试文件目录存在
    test_file_dir = os.path.join(current_dir, "game", "data")
    if not os.path.exists(test_file_dir):
        os.makedirs(test_file_dir)
    
    with open(skills_json_path, 'w', encoding='utf-8') as f_test:
        json.dump(test_skills_data, f_test, ensure_ascii=False, indent=2)
    print(f"测试用的 {skills_json_path} 已创建/覆盖。")
    
    test_skill_loader = SkillLoader(skills_file=skills_json_path)

    print("\n--- 所有技能类别 ---")
    print(test_skill_loader.get_skill_categories())

    print("\n---战士的所有技能---")
    warrior_skills = test_skill_loader.get_skills_by_category("warrior")
    for skill_key, skill_detail in warrior_skills.items():
        print(f"  标识符: {skill_key}, 名称: {skill_detail.get('name')}")

    print("\n--- 通过ID获取火球术 (mage, id: 101) ---")
    fireball_by_id = test_skill_loader.get_skill_by_id("mage", 101)
    if fireball_by_id:
        print(f"  找到技能: {fireball_by_id.get('name')}, 等级: {fireball_by_id.get('level')}")
    else:
        print("  未找到火球术 (ID 101)")

    print("\n--- 通过名称获取基本剑术 (warrior, name: 基本剑术) ---")
    basic_sword_by_name = test_skill_loader.get_skill_by_name("warrior", "基本剑术")
    if basic_sword_by_name:
        print(f"  找到技能: {basic_sword_by_name.get('name')}, ID: {basic_sword_by_name.get('id')}")
    else:
        print("  未找到基本剑术")
        
    print("\n--- 通过标识符获取火球术 (mage, fireball) ---")
    fireball_by_identifier = test_skill_loader.get_skill_by_identifier("mage", "fireball")
    if fireball_by_identifier:
        print(f"  找到技能: {fireball_by_identifier.get('name')}, 等级: {fireball_by_identifier.get('level')}")
    else:
        print("  未找到火球术 (标识符 fireball)")

    print("\n--- 学习火球术 (mage, id: 101) ---")
    if test_skill_loader.learn_skill("mage", 101):
        print("  火球术学习成功!")
        fireball_after_learn = test_skill_loader.get_skill_by_id("mage", 101)
        print(f"  火球术当前状态: learned={fireball_after_learn.get('learned')}, level={fireball_after_learn.get('level')}")
    else:
        print("  火球术学习失败。")
        
    print("\n--- 升级火球术 (mage, id: 101) ---")
    if test_skill_loader.upgrade_skill("mage", 101):
        print("  火球术升级成功!")
        fireball_after_upgrade1 = test_skill_loader.get_skill_by_id("mage", 101)
        print(f"  火球术当前状态: level={fireball_after_upgrade1.get('level')}")
    else:
        print("  火球术升级失败。")

    print("\n--- 再次升级火球术 (mage, id: 101) ---")
    if test_skill_loader.upgrade_skill("mage", 101):
        print("  火球术再次升级成功!")
        fireball_after_upgrade2 = test_skill_loader.get_skill_by_id("mage", 101)
        print(f"  火球术当前状态: level={fireball_after_upgrade2.get('level')}")
    else:
        print("  火球术再次升级失败。")
        
    print("\n--- 第三次升级火球术 (mage, id: 101) - 应该达到满级 ---")
    if test_skill_loader.upgrade_skill("mage", 101):
        print("  火球术第三次升级成功!")
        fireball_after_upgrade3 = test_skill_loader.get_skill_by_id("mage", 101)
        print(f"  火球术当前状态: level={fireball_after_upgrade3.get('level')}")
    else:
        print("  火球术第三次升级失败。") # 此时应该失败因为已经满级

    print("\n--- 保存修改后的技能数据 ---")
    test_skill_loader.save_skills()