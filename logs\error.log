 
2025-07-04 19:01:36 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:36 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:36 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:36 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:37 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:38 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:39 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:42 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:42 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:43 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:59 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:59 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:59 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
2025-07-04 19:01:59 - error - ERROR - [ItemSlot] str: 转换装备属性时出错: cannot import name 'convert_equipment_item' from 'game.data.equipment_converter' (e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\equipment_converter.py) | Context: 
