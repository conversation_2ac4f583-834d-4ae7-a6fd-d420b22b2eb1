#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime, date
from typing import Dict, Any, List, Optional

class PointsShopManager:
    """
    积分商城管理器
    负责处理积分商城的所有业务逻辑，包括商品购买、积分扣除、库存管理等
    """
    
    def __init__(self):
        """
        初始化积分商城管理器
        """
        self.config = self._load_config()
        self.purchase_records = {}  # 存储玩家购买记录
        
    def _load_config(self) -> Dict[str, Any]:
        """
        加载积分商城配置文件
        
        Returns:
            Dict[str, Any]: 商城配置数据
        """
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'points_shop_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载积分商城配置失败: {e}")
            # 返回默认配置
            return {
                "categories": {},
                "shop_settings": {
                    "refresh_time": "00:00",
                    "currency_type": "points",
                    "max_daily_purchases": 10,
                    "vip_bonus_enabled": True
                }
            }
    
    def get_categories(self) -> Dict[str, Any]:
        """
        获取所有商城分类
        
        Returns:
            Dict[str, Any]: 分类信息
        """
        return self.config.get('categories', {})
    
    def get_category_items(self, category_name: str) -> List[Dict[str, Any]]:
        """
        获取指定分类的商品列表
        
        Args:
            category_name: 分类名称
            
        Returns:
            List[Dict[str, Any]]: 商品列表
        """
        categories = self.get_categories()
        if category_name in categories:
            return categories[category_name].get('items', [])
        return []
    
    def can_purchase_item(self, player, item_id: str, category_name: str) -> Dict[str, Any]:
        """
        检查玩家是否可以购买指定商品
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            category_name: 分类名称
            
        Returns:
            Dict[str, Any]: 检查结果
        """
        try:
            # 获取商品信息
            items = self.get_category_items(category_name)
            item_info = None
            for item in items:
                if item['item_id'] == item_id:
                    item_info = item
                    break
            
            if not item_info:
                return {'can_purchase': False, 'reason': '商品不存在'}
            
            # 检查玩家积分
            player_points = self._get_player_points(player)
            item_cost = self._calculate_item_cost(player, item_info)
            
            if player_points < item_cost:
                return {
                    'can_purchase': False, 
                    'reason': f'积分不足，需要{item_cost}积分，当前{player_points}积分'
                }
            
            # 检查每日购买限制
            daily_purchases = self._get_daily_purchases(player, item_id)
            daily_limit = item_info.get('daily_limit', -1)
            
            if daily_limit > 0 and daily_purchases >= daily_limit:
                return {
                    'can_purchase': False, 
                    'reason': f'今日购买次数已达上限({daily_limit}次)'
                }
            
            # 检查库存
            stock = item_info.get('stock', -1)
            if stock == 0:
                return {'can_purchase': False, 'reason': '商品已售罄'}
            
            return {
                'can_purchase': True, 
                'cost': item_cost,
                'item_info': item_info
            }
            
        except Exception as e:
            print(f"检查购买条件时出错: {e}")
            return {'can_purchase': False, 'reason': '系统错误'}
    
    def purchase_item(self, player, item_id: str, category_name: str, quantity: int = 1) -> Dict[str, Any]:
        """
        购买商品
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            category_name: 分类名称
            quantity: 购买数量
            
        Returns:
            Dict[str, Any]: 购买结果
        """
        try:
            # 检查是否可以购买
            check_result = self.can_purchase_item(player, item_id, category_name)
            if not check_result['can_purchase']:
                return {
                    'success': False,
                    'message': check_result['reason']
                }
            
            item_info = check_result['item_info']
            total_cost = check_result['cost'] * quantity
            
            # 扣除积分
            if not self._deduct_player_points(player, total_cost):
                return {
                    'success': False,
                    'message': '积分扣除失败'
                }
            
            # 添加物品到玩家背包
            if not self._add_item_to_player(player, item_id, quantity):
                # 如果添加物品失败，退还积分
                self._add_player_points(player, total_cost)
                return {
                    'success': False,
                    'message': '背包空间不足'
                }
            
            # 记录购买
            self._record_purchase(player, item_id, quantity, total_cost)
            
            return {
                'success': True,
                'message': f'成功购买{item_info["name"]} x{quantity}',
                'item_name': item_info['name'],
                'quantity': quantity,
                'cost': total_cost
            }
            
        except Exception as e:
            print(f"购买商品时出错: {e}")
            return {
                'success': False,
                'message': '购买失败，请稍后重试'
            }
    
    def _get_player_points(self, player) -> int:
        """
        获取玩家积分
        
        Args:
            player: 玩家对象
            
        Returns:
            int: 玩家积分
        """
        # 优先使用玩家的get_points方法
        if hasattr(player, 'get_points'):
            return player.get_points()
        # 如果没有该方法，从currencies中读取
        elif hasattr(player, 'currencies') and isinstance(player.currencies, dict):
            return player.currencies.get('points', 0)
        return 0
    
    def _deduct_player_points(self, player, points: int) -> bool:
        """
        扣除玩家积分
        
        Args:
            player: 玩家对象
            points: 要扣除的积分
            
        Returns:
            bool: 是否成功
        """
        try:
            # 优先使用玩家的spend_points方法
            if hasattr(player, 'spend_points'):
                return player.spend_points(points)
            # 如果没有该方法，手动操作currencies
            elif hasattr(player, 'currencies') and isinstance(player.currencies, dict):
                current_points = self._get_player_points(player)
                if current_points >= points:
                    player.currencies['points'] = current_points - points
                    return True
            return False
        except Exception as e:
            print(f"扣除积分时出错: {e}")
            return False
    
    def _add_player_points(self, player, points: int) -> bool:
        """
        增加玩家积分
        
        Args:
            player: 玩家对象
            points: 要增加的积分
            
        Returns:
            bool: 是否成功
        """
        try:
            # 优先使用玩家的add_points方法
            if hasattr(player, 'add_points'):
                player.add_points(points)
                return True
            # 如果没有该方法，手动操作currencies
            elif hasattr(player, 'currencies') and isinstance(player.currencies, dict):
                current_points = self._get_player_points(player)
                player.currencies['points'] = current_points + points
                return True
            return False
        except Exception as e:
            print(f"增加积分时出错: {e}")
            return False
    
    def _calculate_item_cost(self, player, item_info: Dict[str, Any]) -> int:
        """
        计算商品实际价格（考虑VIP折扣）
        
        Args:
            player: 玩家对象
            item_info: 商品信息
            
        Returns:
            int: 实际价格
        """
        base_cost = item_info['points_cost']
        
        # 检查VIP折扣
        if hasattr(player, 'vip_level') and player.vip_level > 0:
            vip_discounts = item_info.get('vip_discount', {})
            vip_key = f'vip_{player.vip_level}'
            if vip_key in vip_discounts:
                discount = vip_discounts[vip_key]
                return int(base_cost * discount)
        
        return base_cost
    
    def _add_item_to_player(self, player, item_id: str, quantity: int) -> bool:
        """
        添加物品到玩家背包
        
        Args:
            player: 玩家对象
            item_id: 物品ID
            quantity: 数量
            
        Returns:
            bool: 是否成功
        """
        try:
            # 这里需要调用背包管理器的方法
            # 暂时简单实现
            if not hasattr(player, 'inventory'):
                player.inventory = []
            
            # 查找是否已有该物品
            for item in player.inventory:
                if item.get('name') == item_id:
                    item['quantity'] = item.get('quantity', 1) + quantity
                    return True
            
            # 添加新物品
            player.inventory.append({
                'name': item_id,
                'quantity': quantity
            })
            return True
            
        except Exception as e:
            print(f"添加物品到背包时出错: {e}")
            return False
    
    def _get_daily_purchases(self, player, item_id: str) -> int:
        """
        获取玩家今日购买次数
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            
        Returns:
            int: 今日购买次数
        """
        today = date.today().strftime('%Y-%m-%d')
        player_id = getattr(player, 'name', 'unknown')
        
        if player_id not in self.purchase_records:
            return 0
        
        daily_records = self.purchase_records[player_id].get(today, {})
        return daily_records.get(item_id, 0)
    
    def _record_purchase(self, player, item_id: str, quantity: int, cost: int):
        """
        记录购买信息
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            quantity: 购买数量
            cost: 花费积分
        """
        today = date.today().strftime('%Y-%m-%d')
        player_id = getattr(player, 'name', 'unknown')
        
        if player_id not in self.purchase_records:
            self.purchase_records[player_id] = {}
        
        if today not in self.purchase_records[player_id]:
            self.purchase_records[player_id][today] = {}
        
        current_count = self.purchase_records[player_id][today].get(item_id, 0)
        self.purchase_records[player_id][today][item_id] = current_count + quantity