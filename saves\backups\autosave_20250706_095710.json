{"basic_info": {"name": "萝卜", "character_class": "道士", "gender": "男", "level": 5, "exp": 11, "exp_to_next_level": 1244}, "stats": {"hp": 31, "mp": 20, "current_hp": 31, "current_mp": 20, "攻击下限": 6, "攻击上限": 8, "防御下限": 0, "防御上限": 3, "魔法攻击下限": 2, "魔法攻击上限": 2, "道术攻击下限": 1, "道术攻击上限": 2, "魔抗": 1, "攻速": 0.9}, "resources": {"gold": 6380, "currencies": {"gold": 6240, "silver": 0, "yuanbao": 0, "coin": 0, "points": 10}}, "vip_info": {"vip_level": 0, "vip_exp": 0}, "kill_statistics": {"total_monsters": 102, "total_bosses": 0, "map_kills": {"比奇省": {"monsters": 102, "bosses": 0}}, "monster_types": {"鸡": 10, "蛤蟆": 16, "多钩猫": 16, "钉耙猫": 18, "稻草人": 18, "鹿": 8, "毒蜘蛛": 6, "食人花": 4, "森林雪人": 6}}, "map_unlock_progress": {}, "location": {"position": [34.337213507788206, 36.01661840798486], "current_map": "新手村"}, "battle_state": {"is_alive": false, "in_battle": false, "is_hunting": false, "auto_hunt_enabled": false}, "inventory": {"inventory": [{"id": "铁手镯_1751721534602", "name": "铁手镯", "type": "equipment", "category": "手镯", "icon_path": "手镯/铁手镯.png", "description": "手镯 - 普通品质", "quantity": 2, "stackable": true, "max_stack": 99, "rarity": "common", "sell_price": 0, "attributes": {}, "level_requirement": 1, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "传统项链_1751727785151", "name": "传统项链", "type": "equipment", "category": "项链", "icon_path": "game/assets/images/equipment/项链/传统项链.png", "description": "项链 - 传说品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "legendary", "sell_price": 50, "attributes": {"accuracy": 5, "attack_speed": 3, "agility": 2}, "level_requirement": 3, "durability": 180, "max_durability": 180, "gender": null, "effects": {}, "skill_level": 1}, {"id": "乌木剑_1751723856605", "name": "乌木剑", "type": "equipment", "category": "武器", "icon_path": "武器/乌木剑.png", "description": "武器 - 精良品质", "quantity": 1, "stackable": true, "max_stack": 99, "rarity": "uncommon", "sell_price": 0, "attributes": {}, "level_requirement": 1, "durability": 120, "max_durability": 120, "gender": null, "effects": {}, "skill_level": 1}, {"id": "传统项链_1751727677305", "name": "传统项链", "type": "equipment", "category": "项链", "icon_path": "game/assets/images/equipment/项链/传统项链.png", "description": "项链 - 精良品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "uncommon", "sell_price": 35, "attributes": {"accuracy": 2}, "level_requirement": 3, "durability": 120, "max_durability": 120, "gender": null, "effects": {}, "skill_level": 1}, {"id": "古铜戒指_1751727785151", "name": "古铜戒指", "type": "equipment", "category": "戒指", "icon_path": "game/assets/images/equipment/戒指/古铜戒指.png", "description": "戒指 - 精良品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "uncommon", "sell_price": 35, "attributes": {"attack": [1, 1]}, "level_requirement": 3, "durability": 120, "max_durability": 120, "gender": null, "effects": {}, "skill_level": 1}, {"id": "布衣(女)_1751727701999", "name": "布衣(女)", "type": "equipment", "category": "防具", "icon_path": "game/assets/images/equipment/防具/布衣(女).png", "description": "防具 - 精良品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "uncommon", "sell_price": 15, "attributes": {"defense": [1, 2], "magic_defense": [0, 2]}, "level_requirement": 1, "durability": 120, "max_durability": 120, "gender": null, "effects": {}, "skill_level": 1}, {"id": "乌木剑_1751727652078", "name": "乌木剑", "type": "equipment", "category": "武器", "icon_path": "game/assets/images/equipment/武器/乌木剑.png", "description": "武器 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 10, "attributes": {"attack": [4, 8], "magic": [0, 1]}, "level_requirement": 1, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "乌木剑_1751727785171", "name": "乌木剑", "type": "equipment", "category": "武器", "icon_path": "game/assets/images/equipment/武器/乌木剑.png", "description": "武器 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 10, "attributes": {"attack": [4, 8], "magic": [0, 1]}, "level_requirement": 1, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "匕首_1751727677305", "name": "匕首", "type": "equipment", "category": "武器", "icon_path": "game/assets/images/equipment/武器/匕首.png", "description": "武器 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 10, "attributes": {"attack": [4, 5]}, "level_requirement": 1, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "匕首_1751727785171", "name": "匕首", "type": "equipment", "category": "武器", "icon_path": "game/assets/images/equipment/武器/匕首.png", "description": "武器 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 10, "attributes": {"attack": [4, 5]}, "level_requirement": 1, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "古铜戒指_1751727615701", "name": "古铜戒指", "type": "equipment", "category": "戒指", "icon_path": "game/assets/images/equipment/戒指/古铜戒指.png", "description": "戒指 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 30, "attributes": {"attack": [0, 1]}, "level_requirement": 3, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "布衣(女)_1751727677323", "name": "布衣(女)", "type": "equipment", "category": "防具", "icon_path": "game/assets/images/equipment/防具/布衣(女).png", "description": "防具 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 10, "attributes": {"defense": [0, 2], "magic_defense": [0, 1]}, "level_requirement": 1, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "木剑_1751727728945", "name": "木剑", "type": "equipment", "category": "武器", "icon_path": "game/assets/images/equipment/武器/木剑.png", "description": "武器 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 10, "attributes": {"attack": [2, 5]}, "level_requirement": 1, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "金项链_1751727615719", "name": "金项链", "type": "equipment", "category": "项链", "icon_path": "game/assets/images/equipment/项链/金项链.png", "description": "项链 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 20, "attributes": {"attack": [0, 1], "agility": 1}, "level_requirement": 2, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "金项链_1751727753697", "name": "金项链", "type": "equipment", "category": "项链", "icon_path": "game/assets/images/equipment/项链/金项链.png", "description": "项链 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 20, "attributes": {"attack": [0, 1], "agility": 1}, "level_requirement": 2, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": "铁手镯_1751727677323", "name": "铁手镯", "type": "equipment", "category": "手镯", "icon_path": "game/assets/images/equipment/手镯/铁手镯.png", "description": "手镯 - 普通品质", "quantity": 1, "stackable": false, "max_stack": 1, "rarity": "common", "sell_price": 30, "attributes": {"accuracy": 1}, "level_requirement": 3, "durability": 100, "max_durability": 100, "gender": null, "effects": {}, "skill_level": 1}, {"id": 4001, "name": "布衣(男)", "type": "equipment", "category": "防具", "icon_path": "防具/布衣(男).png", "description": "", "quantity": 3, "stackable": true, "max_stack": 99, "rarity": "普通", "sell_price": 0, "attributes": {}, "level_requirement": 1, "durability": 60, "max_durability": 60, "gender": null, "effects": {}, "skill_level": 1}], "max_size": 40}, "equipment": {"equipped_items": {"武器": {"id": "乌木剑_1751727638963", "name": "乌木剑", "type": "装备", "slot": "武器", "required_class": [], "durability": 140, "rarity": "rare", "description": "武器 - 稀有品质", "icon_path": "武器/乌木剑.png", "attributes": {"stats": {"攻击下限": 6, "攻击上限": 8, "魔法攻击下限": 2, "魔法攻击上限": 2}, "required_level": 1, "value": 20, "current_durability": 140}}, "头盔": {"id": "魔法头盔_1751727771703", "name": "魔法头盔", "type": "装备", "slot": "头盔", "required_class": [], "durability": 100, "rarity": "common", "description": "头盔 - 普通品质", "icon_path": "头盔/魔法头盔.png", "attributes": {"stats": {"防御下限": 0, "防御上限": 1, "魔抗": 1}, "required_level": 14, "value": 140, "current_durability": 100}}, "胸甲": {"id": "布衣(男)_1751727785171", "name": "布衣(男)", "type": "装备", "slot": "胸甲", "required_class": [], "durability": 100, "rarity": "common", "description": "防具 - 普通品质", "icon_path": "防具/布衣(男).png", "attributes": {"stats": {"防御下限": 0, "防御上限": 2, "魔抗": 0}, "required_level": 1, "value": 10, "current_durability": 100}}, "护腿": null, "靴子": null, "护符": null, "戒指1": null, "戒指2": null, "手镯1": {"id": "铁手镯_1751721534602", "name": "铁手镯", "type": "装备", "slot": "手镯", "required_class": [], "durability": 100, "rarity": "common", "description": "手镯 - 普通品质", "icon_path": "手镯/铁手镯.png", "attributes": {"stats": {}, "required_level": 1, "value": 0, "current_durability": 100}}, "手镯2": {"id": "铁手镯_1751721534602", "name": "铁手镯", "type": "装备", "slot": "手镯", "required_class": [], "durability": 100, "rarity": "common", "description": "手镯 - 普通品质", "icon_path": "手镯/铁手镯.png", "attributes": {"stats": {}, "required_level": 1, "value": 0, "current_durability": 100}}}, "equipment_stats": {"攻击下限": 6, "攻击上限": 8, "防御下限": 0, "防御上限": 3, "魔法攻击下限": 2, "魔法攻击上限": 2, "道术攻击下限": 0, "道术攻击上限": 0, "生命值": 0, "魔法值": 0, "魔抗": 1, "攻速": 0, "暴击率": 0, "准确": 0, "敏捷": 0, "幸运": 0, "掉落率加成": 0, "经验加成": 0, "金币加成": 0}, "equipment_version": 15}, "skills": {"learned_skills": [], "skill_cooldowns": {}, "skill_experience": {}}, "quests": {"active_quests": [], "completed_quests": [], "quest_progress": {}, "daily_reset_time": 0, "weekly_reset_time": 0}, "daily_checkin": {"last_checkin_date": "2025-07-05", "consecutive_days": 1, "total_checkin_days": 1, "monthly_checkin_count": 1, "checkin_rewards_claimed": [], "current_month": "2025-07"}, "save_info": {"save_time": 1751766980.4400563, "save_date": "2025-07-06T09:56:20.440057", "game_version": "1.0.0", "save_name": "autosave"}}