# -*- coding: utf-8 -*-
"""
统一背包管理器
负责管理玩家的背包系统，既支持业务逻辑又支持UI展示
整合了原有的InventoryManager和UIInventoryManager功能
"""

from typing import List, Dict, Optional, Any
import json
import copy


class InventoryManager:
    """
    统一背包管理器类
    同时支持业务逻辑和UI展示需求
    """
    
    def __init__(self, max_size: int = 40):  # 🔧 统一容量为40格
        """
        初始化背包管理器
        
        参数:
            max_size: 背包最大容量
        """
        self.inventory: List[Dict[str, Any]] = []
        self.max_size = max_size
        
        # 🔧 新增：UI支持属性
        self.slots_per_page = 40  # 每页格子数量 8*5=40
        self.current_equipment_page = 0
        self.current_consumable_page = 0
        self.current_skill_book_page = 0
        self.current_warehouse_page = 0
        
        # 🔧 新增：UI兼容属性（为了兼容现有UI代码）
        self.equipment_items = {}  # 格子索引 -> 物品
        self.consumable_items = {}  # 格子索引 -> (物品, 数量)
        self.skill_book_items = {}  # 格子索引 -> (物品, 数量)
        self.warehouse_items = {}  # 格子索引 -> (物品, 数量)
    
    def add_item(self, item, quantity=1) -> bool:
        """添加物品到背包"""
        try:
            # 导入Item类
            from game.systems.item_generator import Item
            
            # 检查背包是否已满
            if len(self.inventory) >= self.max_size:
                return False
            
            # 如果是可堆叠物品，尝试与现有物品合并
            if hasattr(item, 'stackable') and item.stackable:
                for existing_item in self.inventory:
                    if isinstance(existing_item, Item):
                        if (existing_item.name == item.name and 
                            existing_item.quantity + quantity <= existing_item.max_stack):
                            existing_item.quantity += quantity
                            self._sync_ui_containers()
                            return True
                    else:
                        # 兼容字典格式
                        if (hasattr(existing_item, 'name') and existing_item.name == item.name and 
                            existing_item.quantity + quantity <= item.max_stack):
                            existing_item.quantity += quantity
                            self._sync_ui_containers()
                            return True
                        elif isinstance(existing_item, dict):
                            if (existing_item.get('name') == item.name and 
                                existing_item.get('quantity', 1) + quantity <= item.max_stack):
                                existing_item['quantity'] = existing_item.get('quantity', 1) + quantity
                                self._sync_ui_containers()
                                return True
            
            # 创建新物品实例
            if hasattr(item, 'to_dict'):
                new_item = Item(item.to_dict(), quantity)
            else:
                new_item = Item(item.__dict__ if hasattr(item, '__dict__') else item, quantity)
            self.inventory.append(new_item)
            self._sync_ui_containers()
            return True
            
        except Exception as e:
            print(f"❌ 添加物品失败: {e}")
            return False
    
    def _sync_ui_containers(self):
        """
        🔧 新增：同步UI容器数据
        将inventory列表数据同步到UI容器字典中
        """
        # 清空UI容器
        self.equipment_items.clear()
        self.consumable_items.clear()
        self.skill_book_items.clear()
        
        # 重新分类物品到UI容器
        for i, item in enumerate(self.inventory):
            # 安全获取物品类型
            if hasattr(item, 'type'):
                item_type = item.type
            elif isinstance(item, dict):
                item_type = item.get('type', 'consumable')
            else:
                item_type = 'consumable'
            
            if item_type in ['weapon', 'armor', 'helmet', 'ring', 'necklace', 'equipment', '装备']:
                # 装备类型
                self.equipment_items[len(self.equipment_items)] = item
            elif item_type in ['skill_book', '技能书']:
                # 技能书类型
                quantity = getattr(item, 'quantity', 1) if hasattr(item, 'quantity') else item.get('quantity', 1)
                self.skill_book_items[len(self.skill_book_items)] = (item, quantity)
            else:
                # 消耗品类型
                quantity = getattr(item, 'quantity', 1) if hasattr(item, 'quantity') else item.get('quantity', 1)
                self.consumable_items[len(self.consumable_items)] = (item, quantity)
    
    def add_item_by_name(self, item_name: str, quantity: int = 1) -> bool:
        """
        通过物品名称添加物品到背包
        
        参数:
            item_name: 物品名称
            quantity: 数量
            
        返回:
            bool: 是否成功添加
        """
        try:
            from game.systems.item_generator import item_generator
            item = item_generator.create_item(item_name, quantity)
            if item:
                return self.add_item(item)
            return False
        except Exception:
            # 如果物品生成器不可用，使用基础方法
            basic_item = {
                'name': item_name,
                'quantity': quantity,
                'type': 'unknown',
                'stackable': True,
                'max_stack': 10
            }
            return self.add_item(basic_item)
    
    def remove_item(self, item_name: str, quantity: int = 1) -> bool:
        """
        从背包中移除物品
        
        参数:
            item_name: 物品名称
            quantity: 移除数量
            
        返回:
            bool: 是否成功移除
        """
        for i, item in enumerate(self.inventory):
            # 安全获取物品名称
            current_item_name = getattr(item, 'name', '') if hasattr(item, 'name') else item.get('name', '')
            
            if current_item_name == item_name:
                # 安全获取物品数量
                current_quantity = getattr(item, 'quantity', 1) if hasattr(item, 'quantity') else item.get('quantity', 1)
                
                if current_quantity >= quantity:
                    if current_quantity == quantity:
                        # 完全移除
                        self.inventory.pop(i)
                    else:
                        # 减少数量
                        if hasattr(item, 'quantity'):
                            item.quantity = current_quantity - quantity
                        elif isinstance(item, dict):
                            item['quantity'] = current_quantity - quantity
                    self._sync_ui_containers()  # 🔧 同步UI容器
                    return True
                break
        return False
    
    def remove_item_by_slot(self, slot_idx: int, quantity: int = 1, container_type: str = "consumable"):
        """
        🔧 新增：UI兼容方法 - 通过格子索引移除物品
        
        参数:
            slot_idx: 格子索引
            quantity: 移除数量
            container_type: 容器类型
            
        返回:
            tuple: (移除的物品, 移除的数量)
        """
        if container_type == "equipment":
            container = self.equipment_items
            if slot_idx in container:
                item = container[slot_idx]
                # 从主背包中移除
                item_name = getattr(item, 'name', '') if hasattr(item, 'name') else item.get('name', '')
                self.remove_item(item_name, 1)
                return (item, 1)
        else:
            if container_type == "consumable":
                container = self.consumable_items
            elif container_type == "skill_book":
                container = self.skill_book_items
            else:
                return (None, 0)
                
            if slot_idx in container:
                item, existing_quantity = container[slot_idx]
                remove_qty = min(quantity, existing_quantity)
                # 从主背包中移除
                item_name = getattr(item, 'name', '') if hasattr(item, 'name') else item.get('name', '')
                if self.remove_item(item_name, remove_qty):
                    return (item, remove_qty)
                    
        return (None, 0)
    
    def get_item(self, item_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定物品
        
        参数:
            item_name: 物品名称
            
        返回:
            物品数据字典或None
        """
        for item in self.inventory:
            current_item_name = getattr(item, 'name', '') if hasattr(item, 'name') else item.get('name', '')
            if current_item_name == item_name:
                # 返回物品副本
                if hasattr(item, 'to_dict'):
                    return item.to_dict()
                elif hasattr(item, '__dict__'):
                    return item.__dict__.copy()
                elif isinstance(item, dict):
                    return item.copy()
                else:
                    return {'name': current_item_name}
        return None
    
    def has_item(self, item_name: str, quantity: int = 1) -> bool:
        """
        检查背包中是否有指定数量的物品
        
        参数:
            item_name: 物品名称
            quantity: 需要的数量
            
        返回:
            bool: 如果有足够数量返回True
        """
        total_quantity = 0
        for item in self.inventory:
            current_item_name = getattr(item, 'name', '') if hasattr(item, 'name') else item.get('name', '')
            if current_item_name == item_name:
                current_quantity = getattr(item, 'quantity', 1) if hasattr(item, 'quantity') else item.get('quantity', 1)
                total_quantity += current_quantity
                if total_quantity >= quantity:
                    return True
        return False
    
    def get_item_quantity(self, item_name: str) -> int:
        """
        获取指定物品的总数量
        
        参数:
            item_name: 物品名称
            
        返回:
            int: 物品总数量
        """
        total_quantity = 0
        for item in self.inventory:
            current_item_name = getattr(item, 'name', '') if hasattr(item, 'name') else item.get('name', '')
            if current_item_name == item_name:
                current_quantity = getattr(item, 'quantity', 1) if hasattr(item, 'quantity') else item.get('quantity', 1)
                total_quantity += current_quantity
        return total_quantity
    
    def use_item(self, item_name: str, quantity: int = 1) -> bool:
        """
        使用物品（减少数量）
        
        参数:
            item_name: 物品名称
            quantity: 使用数量
            
        返回:
            bool: 是否成功使用
        """
        return self.remove_item(item_name, quantity)
    
    def get_items_by_type(self, item_type: str) -> List[Dict[str, Any]]:
        """
        获取指定类型的所有物品
        
        参数:
            item_type: 物品类型
            
        返回:
            物品列表
        """
        result = []
        for item in self.inventory:
            current_type = getattr(item, 'type', 'consumable') if hasattr(item, 'type') else item.get('type', 'consumable')
            if current_type == item_type:
                result.append(item)
        return result
    
    def get_items_by_page(self, page: int = 0, container_type: str = "consumable") -> Dict[int, Any]:
        """
        🔧 统一方法：获取指定页的物品
        
        参数:
            page: 页码 (当前版本不支持分页，忽略此参数)
            container_type: 容器类型
            
        返回:
            dict: 物品字典，key为索引，value为物品数据
        """
        # 确保UI容器是最新的
        self._sync_ui_containers()
        
        if container_type == "equipment":
            return self.equipment_items.copy()
        elif container_type == "consumable":
            return self.consumable_items.copy()
        elif container_type == "skill_book":
            return self.skill_book_items.copy()
        elif container_type == "warehouse":
            return self.warehouse_items.copy()
        else:
            return {}
    
    def get_total_pages(self, container_type: str = "consumable") -> int:
        """
        🔧 统一方法：获取总页数
        
        参数:
            container_type: 容器类型
            
        返回:
            int: 总页数 (当前版本固定返回1)
        """
        return 1  # 当前版本不支持分页
    
    def add_item_ui(self, item, quantity: int = 1, container_type: str = "consumable") -> bool:
        """
        🔧 新增：UI兼容方法 - 添加物品（支持容器类型参数）
        
        参数:
            item: 物品对象
            quantity: 物品数量
            container_type: 容器类型（为兼容性保留）
            
        返回:
            bool: 添加成功返回True，否则返回False
        """
        # 统一调用add_item方法
        return self.add_item(item, quantity)
    
    def get_inventory_info(self) -> Dict[str, Any]:
        """
        获取背包信息
        
        返回:
            包含背包状态的字典
        """
        return {
            'items': self.inventory.copy(),
            'current_size': len(self.inventory),
            'max_size': self.max_size,
            'free_slots': self.max_size - len(self.inventory)
        }
    
    def sort_inventory(self, sort_by: str = 'type'):
        """
        整理背包
        
        参数:
            sort_by: 排序方式 ('type', 'name', 'value')
        """
        if sort_by == 'type':
            self.inventory.sort(key=lambda x: getattr(x, 'type', '') if hasattr(x, 'type') else x.get('type', ''))
        elif sort_by == 'name':
            self.inventory.sort(key=lambda x: getattr(x, 'name', '') if hasattr(x, 'name') else x.get('name', ''))
        elif sort_by == 'value':
            self.inventory.sort(key=lambda x: getattr(x, 'sell_price', 0) if hasattr(x, 'sell_price') else x.get('value', 0), reverse=True)
        
        # 重新同步UI容器
        self._sync_ui_containers()
    
    def clear_inventory(self):
        """
        清空背包
        """
        self.inventory.clear()
        self._sync_ui_containers()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式（用于保存）
        
        返回:
            字典格式的背包数据
        """
        # 🔧 修复：正确序列化Item对象
        inventory_data = []
        for item in self.inventory:
            if hasattr(item, 'to_dict'):
                # Item对象，使用to_dict方法
                inventory_data.append(item.to_dict())
            elif isinstance(item, dict):
                # 已经是字典格式
                inventory_data.append(item.copy())
            else:
                # 兼容其他格式，尝试获取__dict__
                if hasattr(item, '__dict__'):
                    inventory_data.append(item.__dict__.copy())
                else:
                    # 最后的兜底处理
                    inventory_data.append({
                        'name': getattr(item, 'name', '未知物品'),
                        'type': getattr(item, 'type', 'consumable'),
                        'quantity': getattr(item, 'quantity', 1)
                    })
        
        return {
            'inventory': inventory_data,
            'max_size': self.max_size
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """
        从字典格式加载（用于读取存档）
        
        参数:
            data: 字典格式的背包数据
        """
        # 🔧 修复：正确重建Item对象
        self.inventory = []
        inventory_data = data.get('inventory', [])
        
        # 重建Item对象
        for item_dict in inventory_data:
            try:
                from game.systems.item_generator import Item
                if isinstance(item_dict, dict):
                    # 从字典创建Item对象
                    quantity = item_dict.get('quantity', 1)
                    item = Item(item_dict, quantity)
                    self.inventory.append(item)
                else:
                    # 兼容旧格式
                    self.inventory.append(item_dict)
            except Exception as e:
                print(f"❌ 重建物品失败: {e}, 物品数据: {item_dict}")
                continue
        
        self.max_size = data.get('max_size', 40)  # 🔧 统一为40格
        self._sync_ui_containers()  # 🔧 同步UI容器
    
    def debug_print_status(self):
        """
        🔧 统一方法：打印背包状态调试信息
        """
        print(f"🔍 统一背包调试信息:")
        print(f"   当前物品数量: {len(self.inventory)}")
        print(f"   最大容量: {self.max_size}")
        print(f"   剩余空间: {self.max_size - len(self.inventory)}")
        if self.inventory:
            print(f"   物品列表:")
            for i, item in enumerate(self.inventory):
                item_type = getattr(item, 'type', '未知类型') if hasattr(item, 'type') else item.get('type', '未知类型')
                item_name = getattr(item, 'name', '未知') if hasattr(item, 'name') else item.get('name', '未知')
                item_quantity = getattr(item, 'quantity', 1) if hasattr(item, 'quantity') else item.get('quantity', 1)
                print(f"     [{i}] {item_name} x{item_quantity} (类型: {item_type})")
        else:
            print(f"   背包为空")
        print(f"   背包是否已满: {len(self.inventory) >= self.max_size}")
        
        # 按类型统计物品数量
        type_count = {}
        for item in self.inventory:
            item_type = getattr(item, 'type', '未知类型') if hasattr(item, 'type') else item.get('type', '未知类型')
            type_count[item_type] = type_count.get(item_type, 0) + 1
        
        print(f"   按类型统计:")
        for type_name, count in type_count.items():
            print(f"     {type_name}: {count}个")
            
        # UI容器统计
        print(f"   UI容器统计:")
        print(f"     装备: {len(self.equipment_items)}个")
        print(f"     消耗品: {len(self.consumable_items)}个") 
        print(f"     技能书: {len(self.skill_book_items)}个")