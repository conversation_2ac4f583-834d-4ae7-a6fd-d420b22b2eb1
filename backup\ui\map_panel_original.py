import pygame
import os
import logging
from game.ui.ui_panel import UIPanel
from game.core.resource_manager import get_game_asset_path, ImageCacheManager
from game.ui.map_panel_config import MapPanelConfig
from game.ui.map_panel_state import MapPanelStateManager
from game.ui.map_panel_error_handler import MapPanelErrorHandler
from game.ui.map_panel_components import InfoAreaContainer

class MapPanel(UIPanel):
    """
    地图面板 - 重构版本
    集成了缓存管理、状态管理、错误处理和组件化架构
    """
    
    def __init__(self, screen, map_manager, player, position, size, battle_manager=None):
        """初始化地图面板，使用新的管理器系统"""
        super().__init__(screen, position, size)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 核心依赖
        self.map_manager = map_manager
        self.player = player
        self.battle_manager = battle_manager
        
        # 初始化核心管理器
        self._initialize_managers()
        
        # 初始化UI组件
        self._initialize_ui_components()
        
        # 初始化状态
        self._initialize_state()
        
        # 缓存地图相关的rect信息（渲染时更新）
        self.map_grid_rects = []
        
        self.logger.info("MapPanel 初始化完成")
    
    def _initialize_managers(self):
        """初始化所有管理器"""
        try:
            # 图片缓存管理器（单例）
            self.image_cache = ImageCacheManager()
            
            # 状态管理器
            self.state_manager = MapPanelStateManager()
            
            # 错误处理器
            self.error_handler = MapPanelErrorHandler()
            
            # 配置管理器
            self.config = MapPanelConfig()
            
            self.logger.info("所有管理器初始化成功")
            
        except Exception as e:
            self.logger.error(f"管理器初始化失败: {e}")
            self.error_handler.handle_error(
                "initialization", 
                e, 
                "使用默认配置"
            )
    
    def _initialize_ui_components(self):
        """初始化UI组件系统"""
        try:
            # 创建信息区域容器
            self.info_container = InfoAreaContainer(
                self.screen,
                pygame.Rect(
                    self.rect.left,
                    self.rect.bottom - self.config.INFO_AREA_HEIGHT,
                    self.rect.width,
                    self.config.INFO_AREA_HEIGHT
                )
            )
            
            self.logger.info("UI组件系统初始化成功")
            
        except Exception as e:
            self.logger.error(f"UI组件初始化失败: {e}")
            self.error_handler.handle_error(
                "ui_component", 
                e, 
                "使用简化UI"
            )
            self.info_container = None
    
    def _initialize_state(self):
        """初始化状态"""
        try:
            # 设置初始标签页
            if self.map_manager:
                categories = self.map_manager.get_categories()
                if categories:
                    self.state_manager.set_current_tab(categories[0])
            
            # 重置其他状态
            self.state_manager.reset_selection()
            self.state_manager.reset_pagination()
            
            self.logger.info("状态初始化成功")
            
        except Exception as e:
            self.logger.error(f"状态初始化失败: {e}")
            self.error_handler.handle_error(
                "state_management", 
                e, 
                "使用默认状态"
            )
    
    def get_maps_for_current_tab_and_page(self):
        """获取当前标签页和页码对应的地图数据"""
        try:
            if not self.map_manager:
                return [], 0
            
            current_tab = self.state_manager.get_current_tab()
            current_page = self.state_manager.get_current_page()
            maps_per_page = self.config.GRID_COLS * self.config.GRID_ROWS
            
            all_maps = self.map_manager.get_maps_by_category(current_tab, self.player)
            if not all_maps:
                return [], 0
            
            start_index = current_page * maps_per_page
            end_index = min(start_index + maps_per_page, len(all_maps))
            total_pages = (len(all_maps) + maps_per_page - 1) // maps_per_page
            
            return all_maps[start_index:end_index], total_pages
            
        except Exception as e:
            self.logger.error(f"获取地图数据失败: {e}")
            self.error_handler.handle_error(
                "data_retrieval", 
                e, 
                "返回空列表"
            )
            return [], 0
    
    def _load_map_image(self, map_data):
        """加载地图缩略图，使用缓存系统"""
        try:
            map_name = map_data.get("name", "未知地图")
            
            # 尝试多种图片格式
            image_paths = [
                f"images/maps/{map_name}.jpg",
                f"images/maps/{map_name}.png",
                f"images/maps/{map_data.get('thumbnail', '')}" if map_data.get('thumbnail') else None
            ]
            
            for path in image_paths:
                if path:
                    full_path = get_game_asset_path(path)
                    if os.path.exists(full_path):
                        # 使用缓存加载图片
                        image = self.image_cache.get_image(full_path)
                        if image:
                            # 缩放到合适大小
                            target_size = (self.config.GRID_CELL_WIDTH, self.config.GRID_CELL_HEIGHT)
                            return pygame.transform.scale(image, target_size)
            
            # 如果没有找到图片，返回占位符
            return self._create_placeholder_image()
            
        except Exception as e:
            self.logger.error(f"加载地图图片失败 {map_data.get('name', '未知')}: {e}")
            self.error_handler.handle_error(
                "resource_loading", 
                e, 
                "使用占位符图片"
            )
            return self._create_placeholder_image()
    
    def _create_placeholder_image(self):
        """创建占位符图片"""
        try:
            size = (self.config.GRID_CELL_WIDTH, self.config.GRID_CELL_HEIGHT)
            placeholder = pygame.Surface(size).convert_alpha()
            placeholder.fill(self.config.PLACEHOLDER_COLOR)
            
            # 添加占位文字
            if hasattr(self, 'normal_font'):
                text = self.normal_font.render("无图片", True, self.config.TEXT_COLOR)
                text_rect = text.get_rect(center=(size[0]//2, size[1]//2))
                placeholder.blit(text, text_rect)
            
            return placeholder
            
        except Exception as e:
            self.logger.error(f"创建占位符图片失败: {e}")
            # 返回最简单的Surface
            return pygame.Surface((self.config.GRID_CELL_WIDTH, self.config.GRID_CELL_HEIGHT))
    
    def render_tabs(self):
        """渲染标签页"""
        try:
            if not self.map_manager:
                return
            
            categories = self.map_manager.get_categories() or self.config.TAB_CATEGORIES
            current_tab = self.state_manager.get_current_tab()
            
            current_x = self.rect.left + self.config.TAB_MARGIN
            tab_y = self.rect.top + self.config.TAB_MARGIN
            
            # 清空之前的标签按钮rect
            self.state_manager.clear_tab_rects()
            
            for category in categories:
                # 计算标签尺寸
                text_surface = self.normal_font.render(category, True, self.config.TEXT_COLOR)
                tab_width = text_surface.get_width() + 2 * self.config.TAB_PADDING
                tab_rect = pygame.Rect(current_x, tab_y, tab_width, self.config.TAB_HEIGHT)
                
                # 保存标签rect用于点击检测
                self.state_manager.set_tab_rect(category, tab_rect)
                
                # 绘制标签背景
                is_current = (category == current_tab)
                bg_color = self.config.TAB_ACTIVE_COLOR if is_current else self.config.TAB_INACTIVE_COLOR
                text_color = self.config.TAB_ACTIVE_TEXT_COLOR if is_current else self.config.TAB_INACTIVE_TEXT_COLOR
                
                pygame.draw.rect(self.screen, bg_color, tab_rect)
                
                # 绘制活动标签的底部高亮
                if is_current:
                    highlight_rect = pygame.Rect(
                        tab_rect.left,
                        tab_rect.bottom - 2,
                        tab_rect.width,
                        2
                    )
                    pygame.draw.rect(self.screen, self.config.TAB_HIGHLIGHT_COLOR, highlight_rect)
                
                # 绘制标签文字
                text_rect = text_surface.get_rect(center=tab_rect.center)
                text_surface = self.normal_font.render(category, True, text_color)
                self.screen.blit(text_surface, text_rect)
                
                current_x += tab_width + self.config.TAB_SPACING
                
        except Exception as e:
            self.logger.error(f"渲染标签页失败: {e}")
            self.error_handler.handle_error(
                "rendering", 
                e, 
                "跳过标签页渲染"
            )
    
    def render_map_grid(self):
        """渲染地图网格"""
        try:
            maps_on_page, _ = self.get_maps_for_current_tab_and_page()
            if not maps_on_page:
                self._render_empty_grid()
                return
            
            # 清空并重建网格rect
            self.map_grid_rects = []
            grid_state = self.state_manager.get_grid_state()
            
            grid_origin_x = self.rect.left + self.config.GRID_MARGIN_HORIZONTAL
            grid_origin_y = self.rect.top + self.config.GRID_MARGIN_TOP
            
            for i, map_data in enumerate(maps_on_page):
                row = i // self.config.GRID_COLS
                col = i % self.config.GRID_COLS
                
                cell_x = grid_origin_x + col * (self.config.GRID_CELL_WIDTH + self.config.GRID_PADDING)
                cell_y = grid_origin_y + row * (self.config.GRID_CELL_HEIGHT + self.config.GRID_PADDING)
                cell_rect = pygame.Rect(cell_x, cell_y, self.config.GRID_CELL_WIDTH, self.config.GRID_CELL_HEIGHT)
                
                # 保存rect信息
                map_id = map_data.get("id", f"map_{i}")
                self.map_grid_rects.append({
                    'rect': cell_rect,
                    'map_data': map_data,
                    'map_id': map_id
                })
                
                # 更新网格状态
                grid_state.add_cell(map_id, cell_rect, map_data)
                
                # 渲染单个地图格子
                self._render_map_cell(cell_rect, map_data, map_id)
            
            self.logger.debug(f"渲染了 {len(maps_on_page)} 个地图格子")
            
        except Exception as e:
            self.logger.error(f"渲染地图网格失败: {e}")
            self.error_handler.handle_error(
                "rendering", 
                e, 
                "显示错误信息"
            )
            self._render_error_grid(str(e))
    
    def _render_map_cell(self, cell_rect, map_data, map_id):
        """渲染单个地图格子"""
        try:
            # 1. 加载并绘制地图图片
            map_image = self._load_map_image(map_data)
            if map_image:
                self.screen.blit(map_image, cell_rect.topleft)
                
                # 添加半透明遮罩提高文字可读性
                overlay = pygame.Surface((cell_rect.width, cell_rect.height))
                overlay.set_alpha(self.config.OVERLAY_ALPHA)
                overlay.fill(self.config.OVERLAY_COLOR)
                self.screen.blit(overlay, cell_rect.topleft)
            
            # 2. 绘制地图名称
            map_name = map_data.get("name", "未知地图")
            name_surface = self.normal_font.render(map_name, True, self.config.MAP_NAME_COLOR)
            name_rect = name_surface.get_rect(
                centerx=cell_rect.centerx,
                top=cell_rect.top + self.config.MAP_NAME_OFFSET_Y
            )
            self.screen.blit(name_surface, name_rect)
            
            # 3. 绘制状态信息
            self._render_map_status_info(cell_rect, map_data, name_rect.bottom)
            
            # 4. 绘制状态指示器
            self._render_map_status_indicator(cell_rect, map_data)
            
            # 5. 绘制选中边框
            if self.state_manager.get_selected_map() == map_id:
                pygame.draw.rect(
                    self.screen, 
                    self.config.SELECTED_BORDER_COLOR, 
                    cell_rect, 
                    self.config.SELECTED_BORDER_WIDTH
                )
                
        except Exception as e:
            self.logger.error(f"渲染地图格子失败 {map_data.get('name', '未知')}: {e}")
            # 绘制错误占位符
            pygame.draw.rect(self.screen, self.config.ERROR_COLOR, cell_rect)
            error_text = self.small_font.render("渲染错误", True, self.config.TEXT_COLOR)
            error_rect = error_text.get_rect(center=cell_rect.center)
            self.screen.blit(error_text, error_rect)
    
    def _render_map_status_info(self, cell_rect, map_data, start_y):
        """渲染地图状态信息（怪物数量、Boss等）"""
        try:
            info_y = start_y + self.config.STATUS_INFO_OFFSET_Y
            
            # 怪物信息
            monsters_info = map_data.get("monsters_info", "怪:未知")
            monsters_surface = self.small_font.render(monsters_info, True, self.config.STATUS_INFO_COLOR)
            monsters_rect = monsters_surface.get_rect(
                centerx=cell_rect.centerx,
                top=info_y
            )
            self.screen.blit(monsters_surface, monsters_rect)
            
            # Boss信息
            boss_info = map_data.get("boss_info", "Boss:未知")
            boss_surface = self.small_font.render(boss_info, True, self.config.STATUS_INFO_COLOR)
            boss_rect = boss_surface.get_rect(
                centerx=cell_rect.centerx,
                top=monsters_rect.bottom + self.config.STATUS_INFO_SPACING
            )
            self.screen.blit(boss_surface, boss_rect)
            
        except Exception as e:
            self.logger.error(f"渲染状态信息失败: {e}")
    
    def _render_map_status_indicator(self, cell_rect, map_data):
        """渲染地图状态指示器（解锁/锁定）"""
        try:
            is_unlocked = map_data.get("unlocked", True)
            
            # 创建状态指示器文字
            if is_unlocked:
                indicator_text = "●"
                indicator_color = self.config.UNLOCKED_INDICATOR_COLOR
            else:
                indicator_text = "×"
                indicator_color = self.config.LOCKED_INDICATOR_COLOR
            
            indicator_surface = self.small_font.render(indicator_text, True, indicator_color)
            indicator_rect = indicator_surface.get_rect(
                bottomright=(
                    cell_rect.right - self.config.STATUS_INDICATOR_MARGIN,
                    cell_rect.bottom - self.config.STATUS_INDICATOR_MARGIN
                )
            )
            self.screen.blit(indicator_surface, indicator_rect)
            
        except Exception as e:
            self.logger.error(f"渲染状态指示器失败: {e}")
    
    def _render_empty_grid(self):
        """渲染空网格"""
        try:
            empty_text = "此分类下暂无地图"
            text_surface = self.normal_font.render(empty_text, True, self.config.TEXT_COLOR)
            text_rect = text_surface.get_rect(center=self.rect.center)
            self.screen.blit(text_surface, text_rect)
            
        except Exception as e:
            self.logger.error(f"渲染空网格失败: {e}")
    
    def _render_error_grid(self, error_msg):
        """渲染错误网格"""
        try:
            error_text = f"渲染错误: {error_msg}"
            text_surface = self.normal_font.render(error_text, True, self.config.ERROR_COLOR)
            text_rect = text_surface.get_rect(center=self.rect.center)
            self.screen.blit(text_surface, text_rect)
            
        except Exception as e:
            self.logger.error(f"渲染错误网格失败: {e}")

    def render_mid_controls(self):
        """渲染中间控制区域（分页按钮等）"""
        try:
            _, total_pages = self.get_maps_for_current_tab_and_page()
            current_page = self.state_manager.get_current_page()
            
            # 计算控制区域位置
            y_pos = (self.rect.top + self.config.GRID_MARGIN_TOP + 
                    self.config.GRID_ROWS * (self.config.GRID_CELL_HEIGHT + self.config.GRID_PADDING) - 
                    self.config.GRID_PADDING + 5)
            
            # 翻页按钮
            prev_text = f"上一页 ({current_page + 1}/{total_pages})"
            next_text = f"下一页 ({current_page + 1}/{total_pages})"
            
            prev_text_surf = self.small_font.render(prev_text, True, self.config.TEXT_COLOR)
            next_text_surf = self.small_font.render(next_text, True, self.config.TEXT_COLOR)
            
            button_width = max(prev_text_surf.get_width(), next_text_surf.get_width()) + 20
            button_height = prev_text_surf.get_height() + 10
            
            # 上一页按钮
            prev_rect = pygame.Rect(
                self.rect.left + self.config.GRID_MARGIN_HORIZONTAL, 
                y_pos, 
                button_width, 
                button_height
            )
            self.state_manager.set_prev_button_rect(prev_rect)
            
            prev_color = self.config.BUTTON_DISABLED_COLOR if current_page == 0 else self.config.BUTTON_COLOR
            pygame.draw.rect(self.screen, prev_color, prev_rect)
            
            text_rect = prev_text_surf.get_rect(center=prev_rect.center)
            self.screen.blit(prev_text_surf, text_rect)
            
            # 下一页按钮
            next_rect = pygame.Rect(
                prev_rect.right + 10, 
                y_pos, 
                button_width, 
                button_height
            )
            self.state_manager.set_next_button_rect(next_rect)
            
            next_color = self.config.BUTTON_DISABLED_COLOR if current_page >= total_pages - 1 else self.config.BUTTON_COLOR
            pygame.draw.rect(self.screen, next_color, next_rect)
            
            text_rect = next_text_surf.get_rect(center=next_rect.center)
            self.screen.blit(next_text_surf, text_rect)
            
            # BOSS次数显示
            if self.map_manager and hasattr(self.map_manager, 'get_boss_attempts'):
                boss_count_text = f"BOSS次数: ({self.map_manager.get_boss_attempts()}) (新图重置)"
                boss_count_surf = self.normal_font.render(boss_count_text, True, self.config.BOSS_COUNT_COLOR)
                boss_x = next_rect.right + 20
                boss_y = y_pos + (button_height - boss_count_surf.get_height()) // 2
                self.screen.blit(boss_count_surf, (boss_x, boss_y))
            
        except Exception as e:
            self.logger.error(f"渲染中间控制区域失败: {e}")
            self.error_handler.handle_error(
                "rendering", 
                e, 
                "跳过控制区域渲染"
            )
    
    def render_wrapped_text(self, text, font, color, rect, line_spacing=2):
        """渲染自动换行文本 - 优化版本"""
        try:
            if not text:
                return
            
            words = text.split(' ')
            lines = []
            current_line = ""
            
            for word in words:
                test_line = current_line + (" " if current_line else "") + word
                test_surface = font.render(test_line, True, color)
                
                if test_surface.get_width() <= rect.width:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                        current_line = word
                    else:
                        # 单词太长，需要截断
                        lines.append(word[:rect.width // font.size("A")[0]])
                        current_line = ""
            
            if current_line:
                lines.append(current_line)
            
            # 渲染每一行
            y_offset = rect.top
            for line in lines:
                if y_offset + font.get_height() > rect.bottom:
                    break  # 超出区域，停止渲染
                
                line_surface = font.render(line, True, color)
                self.screen.blit(line_surface, (rect.left, y_offset))
                y_offset += font.get_height() + line_spacing
                
        except Exception as e:
            self.logger.error(f"渲染文本失败: {e}")
            # 渲染简化的错误信息
            error_surface = font.render("文本渲染错误", True, self.config.ERROR_COLOR)
            self.screen.blit(error_surface, rect.topleft)
    
    def render_bottom_info_area(self):
        """渲染底部信息区域 - 使用组件化架构"""
        try:
            if self.info_container:
                # 使用组件化系统渲染
                self._update_info_components()
                self.info_container.render()
            else:
                # 降级到简化渲染
                self._render_simplified_info_area()
                
        except Exception as e:
            self.logger.error(f"渲染底部信息区域失败: {e}")
            self.error_handler.handle_error(
                "rendering", 
                e, 
                "使用简化信息区域"
            )
            self._render_simplified_info_area()
    
    def _update_info_components(self):
        """更新信息组件数据"""
        try:
            selected_map_data = None
            if self.state_manager.get_selected_map():
                selected_map_data = self.map_manager.get_map_by_id(self.state_manager.get_selected_map())
            
            # 更新解锁进度组件
            unlock_progress_data = self._get_unlock_progress_data()
            self.info_container.update_component('unlock_progress', unlock_progress_data)
            
            # 更新Boss挑战组件
            boss_challenge_data = self._get_boss_challenge_data(selected_map_data)
            self.info_container.update_component('boss_challenge', boss_challenge_data)
            
            # 更新手动挑战组件
            manual_challenge_data = self._get_manual_challenge_data(selected_map_data)
            self.info_container.update_component('manual_challenge', manual_challenge_data)
            
        except Exception as e:
            self.logger.error(f"更新信息组件失败: {e}")
    
    def _get_unlock_progress_data(self):
        """获取解锁进度数据"""
        try:
            if not self.map_manager:
                return {"title": "开通新图", "content": "地图管理器未初始化"}
            
            next_map = self.map_manager.get_next_unlockable_map(self.player)
            if next_map:
                progress = self.map_manager.get_unlock_progress(next_map, self.player)
                return {
                    "title": "开通新图",
                    "content": f"{next_map.get('name', '未知地图')}\n进度: {progress}%"
                }
            else:
                return {
                    "title": "开通新图", 
                    "content": "所有地图已解锁"
                }
                
        except Exception as e:
            self.logger.error(f"获取解锁进度数据失败: {e}")
            return {"title": "开通新图", "content": "数据获取失败"}
    
    def _get_boss_challenge_data(self, selected_map_data):
        """获取Boss挑战数据"""
        try:
            if not selected_map_data:
                return {
                    "title": "自动挑战",
                    "content": "请先选择地图",
                    "button_text": "自动挑战",
                    "button_enabled": False
                }
            
            auto_status = self.map_manager.get_auto_challenge_status() if self.map_manager else False
            status_text = "开启" if auto_status else "关闭"
            
            return {
                "title": "自动挑战",
                "content": f"状态: {status_text}\n地图: {selected_map_data.get('name', '未知')}",
                "button_text": "切换状态",
                "button_enabled": True,
                "current_status": auto_status
            }
            
        except Exception as e:
            self.logger.error(f"获取Boss挑战数据失败: {e}")
            return {
                "title": "自动挑战",
                "content": "数据获取失败",
                "button_text": "自动挑战",
                "button_enabled": False
            }
    
    def _get_manual_challenge_data(self, selected_map_data):
        """获取手动挑战数据"""
        try:
            if not selected_map_data:
                return {
                    "title": "手动挑战",
                    "content": "请先选择地图",
                    "button_text": "开始挑战",
                    "button_enabled": False
                }
            
            can_challenge = True
            if self.map_manager and hasattr(self.map_manager, 'can_challenge_boss'):
                can_challenge = self.map_manager.can_challenge_boss(selected_map_data, self.player)
            
            content = f"地图: {selected_map_data.get('name', '未知')}"
            if not can_challenge:
                content += "\n条件不满足"
            
            return {
                "title": "手动挑战",
                "content": content,
                "button_text": "开始挑战",
                "button_enabled": can_challenge
            }
            
        except Exception as e:
            self.logger.error(f"获取手动挑战数据失败: {e}")
            return {
                "title": "手动挑战",
                "content": "数据获取失败",
                "button_text": "开始挑战",
                "button_enabled": False
            }
    
    def _render_simplified_info_area(self):
        """渲染简化的信息区域（降级方案）"""
        try:
            info_rect = pygame.Rect(
                self.rect.left,
                self.rect.bottom - self.config.INFO_AREA_HEIGHT,
                self.rect.width,
                self.config.INFO_AREA_HEIGHT
            )
            
            # 绘制背景
            pygame.draw.rect(self.screen, self.config.INFO_AREA_BG_COLOR, info_rect)
            pygame.draw.rect(self.screen, self.config.INFO_AREA_BORDER_COLOR, info_rect, 1)
            
            # 显示简化信息
            simple_text = "信息区域加载中..."
            if self.state_manager.get_selected_map():
                simple_text = f"已选择地图: {self.state_manager.get_selected_map()}"
            
            text_surface = self.normal_font.render(simple_text, True, self.config.TEXT_COLOR)
            text_rect = text_surface.get_rect(center=info_rect.center)
            self.screen.blit(text_surface, text_rect)
            
            # 渲染底部说明文字
            bottom_text = "取BOSS存储数->魔盒->转至低级地图 (1/1)"
            bottom_surface = self.small_font.render(bottom_text, True, self.config.SECONDARY_TEXT_COLOR)
            bottom_x = self.rect.left + self.config.GRID_MARGIN_HORIZONTAL
            bottom_y = self.rect.bottom - 20
            self.screen.blit(bottom_surface, (bottom_x, bottom_y))
            
        except Exception as e:
            self.logger.error(f"渲染简化信息区域失败: {e}")
    
    def render(self):
        """主渲染方法"""
        try:
            if not self.visible:
                return
            
            # 绘制面板背景
            super().render()
            
            # 渲染各个组件
            self.render_tabs()
            self.render_map_grid()
            self.render_mid_controls()
            self.render_bottom_info_area()
            
            self.logger.debug("MapPanel 渲染完成")
            
        except Exception as e:
            self.logger.error(f"MapPanel 渲染失败: {e}")
            self.error_handler.handle_error(
                "rendering", 
                e, 
                "显示错误面板"
            )
            self._render_error_panel(str(e))
    
    def _render_error_panel(self, error_msg):
        """渲染错误面板"""
        try:
            # 绘制错误背景
            pygame.draw.rect(self.screen, self.config.ERROR_BG_COLOR, self.rect)
            pygame.draw.rect(self.screen, self.config.ERROR_COLOR, self.rect, 2)
            
            # 显示错误信息
            error_text = f"MapPanel 渲染错误: {error_msg}"
            text_surface = self.normal_font.render(error_text, True, self.config.ERROR_COLOR)
            text_rect = text_surface.get_rect(center=self.rect.center)
            self.screen.blit(text_surface, text_rect)
            
        except Exception as e:
            self.logger.error(f"渲染错误面板失败: {e}")
    
    def handle_event(self, event):
        """事件处理方法 - 使用状态管理器"""
        try:
            if not self.visible:
                return False
            
            if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                return self._handle_mouse_click(event.pos)
            
            return False
            
        except Exception as e:
            self.logger.error(f"事件处理失败: {e}")
            self.error_handler.handle_error(
                "event_handling", 
                e, 
                "忽略事件"
            )
            return False
    
    def _handle_mouse_click(self, mouse_pos):
        """处理鼠标点击事件"""
        try:
            # 1. 检测标签页点击
            if self._handle_tab_click(mouse_pos):
                return True
            
            # 2. 检测地图格子点击
            if self._handle_map_grid_click(mouse_pos):
                return True
            
            # 3. 检测翻页按钮点击
            if self._handle_pagination_click(mouse_pos):
                return True
            
            # 4. 检测挑战按钮点击
            if self._handle_challenge_button_click(mouse_pos):
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理鼠标点击失败: {e}")
            return False
    
    def _handle_tab_click(self, mouse_pos):
        """处理标签页点击"""
        try:
            tab_rects = self.state_manager.get_tab_rects()
            for tab_name, rect in tab_rects.items():
                if rect.collidepoint(mouse_pos):
                    if self.state_manager.get_current_tab() != tab_name:
                        self.state_manager.set_current_tab(tab_name)
                        self.state_manager.reset_pagination()
                        self.state_manager.reset_selection()
                        self.logger.info(f"切换到标签页: {tab_name}")
                        self.map_grid_rects = []  # 重置网格rect
                    return True
            return False
            
        except Exception as e:
            self.logger.error(f"处理标签页点击失败: {e}")
            return False
    
    def _handle_map_grid_click(self, mouse_pos):
        """处理地图格子点击"""
        try:
            for grid_info in self.map_grid_rects:
                if grid_info['rect'].collidepoint(mouse_pos):
                    map_data = grid_info['map_data']
                    map_id = grid_info['map_id']
                    
                    if map_data.get("unlocked", True):
                        self.state_manager.set_selected_map(map_id)
                        map_name = map_data.get('name')
                        self.logger.info(f"选择了地图: {map_name} (ID: {map_id})")
                        
                        # 实际切换地图
                        if self.map_manager and hasattr(self.map_manager, 'switch_map'):
                            success = self.map_manager.switch_map(map_name)
                            if success:
                                self.logger.info(f"成功切换到地图: {map_name}")
                                if hasattr(self.player, 'set_current_map'):
                                    self.player.set_current_map(map_id)
                            else:
                                self.logger.warning(f"切换地图失败: {map_name}")
                    else:
                        self.logger.info(f"点击了未解锁地图: {map_data.get('name')}")
                        # 这里可以添加解锁逻辑
                    
                    return True
            return False
            
        except Exception as e:
            self.logger.error(f"处理地图格子点击失败: {e}")
            return False
    
    def _handle_pagination_click(self, mouse_pos):
        """处理分页按钮点击"""
        try:
            prev_rect = self.state_manager.get_prev_button_rect()
            next_rect = self.state_manager.get_next_button_rect()
            
            if prev_rect and prev_rect.collidepoint(mouse_pos):
                if self.state_manager.get_current_page() > 0:
                    self.state_manager.prev_page()
                    self.map_grid_rects = []  # 重置网格rect
                    self.logger.info("翻到上一页")
                return True
            
            if next_rect and next_rect.collidepoint(mouse_pos):
                _, total_pages = self.get_maps_for_current_tab_and_page()
                if self.state_manager.get_current_page() < total_pages - 1:
                    self.state_manager.next_page()
                    self.map_grid_rects = []  # 重置网格rect
                    self.logger.info("翻到下一页")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理分页点击失败: {e}")
            return False
    
    def _handle_challenge_button_click(self, mouse_pos):
        """处理挑战按钮点击"""
        try:
            selected_map_data = None
            if self.state_manager.get_selected_map():
                selected_map_data = self.map_manager.get_map_by_id(self.state_manager.get_selected_map())
            
            # 自动挑战按钮
            auto_rect = self.state_manager.get_auto_challenge_rect()
            if auto_rect and auto_rect.collidepoint(mouse_pos) and selected_map_data:
                new_status = self.map_manager.toggle_auto_challenge()
                self.logger.info(f"自动挑战Boss: {'开启' if new_status else '关闭'}")
                return True
            
            # 手动挑战按钮
            manual_rect = self.state_manager.get_manual_challenge_rect()
            if manual_rect and manual_rect.collidepoint(mouse_pos) and selected_map_data:
                if self.map_manager.start_boss_challenge(selected_map_data, is_manual=True):
                    self.logger.info(f"开始手动挑战Boss: {selected_map_data.get('name')}")
                else:
                    self.logger.warning("无法挑战Boss：条件不满足")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理挑战按钮点击失败: {e}")
            return False
    
    def update(self):
        """更新方法"""
        try:
            # 更新状态管理器
            self.state_manager.update()
            
            # 更新UI组件
            if self.info_container:
                self.info_container.update()
            
            # 更新缓存统计（如果需要）
            if hasattr(self.image_cache, 'update_stats'):
                self.image_cache.update_stats()
                
        except Exception as e:
            self.logger.error(f"更新失败: {e}")
            self.error_handler.handle_error(
                "update", 
                e, 
                "跳过更新"
            )
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理图片缓存
            if hasattr(self.image_cache, 'cleanup'):
                self.image_cache.cleanup()
            
            # 清理UI组件
            if self.info_container:
                self.info_container.cleanup()
            
            # 清理状态管理器
            self.state_manager.cleanup()
            
            self.logger.info("MapPanel 资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
    
    def get_cache_stats(self):
        """获取缓存统计信息"""
        try:
            if hasattr(self.image_cache, 'get_stats'):
                return self.image_cache.get_stats()
            return {}
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def get_error_stats(self):
        """获取错误统计信息"""
        try:
            return self.error_handler.get_error_stats()
        except Exception as e:
            self.logger.error(f"获取错误统计失败: {e}")
            return {}