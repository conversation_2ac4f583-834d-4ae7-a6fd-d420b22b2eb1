#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实掉落信息集成测试

测试公告面板是否能正确显示真实的游戏掉落信息，而不是示例数据
"""

import sys
import os
import time
import pygame
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from game.ui.announcement_panel import AnnouncementPanel
from game.core.battle_manager import BattleManager


def test_real_drop_integration():
    """测试真实掉落信息集成"""
    print("\n=== 测试真实掉落信息集成 ===")
    
    # 初始化pygame
    pygame.init()
    screen = pygame.display.set_mode((100, 100))
    
    # 创建战斗管理器
    battle_manager = BattleManager(debug_console_output=False)
    
    # 创建公告面板
    mock_user_message_manager = Mock()
    mock_user_message_manager.get_messages_by_type.return_value = []
    
    panel = AnnouncementPanel(
        screen=screen,
        battle_manager=battle_manager,
        position=(0, 0),
        size=(400, 300),
        player=Mock(),
        user_message_manager=mock_user_message_manager
    )
    
    # 连接战斗管理器和公告面板
    battle_manager.set_announcement_panel(panel)
    
    print("✅ 战斗管理器和公告面板已连接")
    
    # 检查初始状态（应该没有示例数据）
    panel.current_tab = 1  # 切换到掉落标签页
    initial_drops = panel.get_current_tab_messages()
    print(f"✅ 初始掉落记录: {len(initial_drops)} 条（应该为0）")
    
    panel.current_tab = 2  # 切换到重要信息标签页
    initial_important = panel.get_current_tab_messages()
    print(f"✅ 初始重要信息: {len(initial_important)} 条（应该只有连接消息）")
    
    panel.current_tab = 0  # 切换到系统公告标签页
    initial_announcements = panel.get_current_tab_messages()
    print(f"✅ 初始系统公告: {len(initial_announcements)} 条（应该只有欢迎消息）")
    
    # 模拟真实的掉落事件
    print("\n--- 模拟真实掉落事件 ---")
    
    # 直接调用公告面板的方法，模拟战斗管理器的调用
    panel.add_drop_info("真实铁剑", "武器", "普通", "真实骷髅", "真实比奇省")
    panel.add_drop_info("真实药水", "消耗品", "普通", "真实鹿", "真实银杏村")
    panel.add_drop_info("真实魔法戒指", "装备", "稀有", "真实半兽勇士", "真实比奇大陆")
    
    # 检查掉落信息
    panel.current_tab = 1
    real_drops = panel.get_current_tab_messages()
    print(f"✅ 添加真实掉落后: {len(real_drops)} 条记录")
    
    for i, drop in enumerate(real_drops[-3:]):  # 显示最后3条
        print(f"   {i+1}. {drop['message']}")
    
    # 模拟真实的重要事件
    print("\n--- 模拟真实重要事件 ---")
    
    panel.add_important_info("真实升级：10级 → 11级", "升级", "重要", (0, 255, 0))
    panel.add_important_info("真实成就：击败100个怪物", "成就", "重要", (255, 215, 0))
    
    # 检查重要信息
    panel.current_tab = 2
    real_important = panel.get_current_tab_messages()
    print(f"✅ 添加真实重要信息后: {len(real_important)} 条记录")
    
    for i, msg in enumerate(real_important[-3:]):  # 显示最后3条
        print(f"   {i+1}. {msg['message']}")
    
    # 模拟真实的系统公告
    print("\n--- 模拟真实系统公告 ---")
    
    panel.add_system_announcement("真实系统维护通知：服务器将在30分钟后重启", (255, 255, 0))
    panel.add_system_announcement("真实活动通知：双倍经验活动开始！", (0, 255, 0))
    
    # 检查系统公告
    panel.current_tab = 0
    real_announcements = panel.get_current_tab_messages()
    print(f"✅ 添加真实系统公告后: {len(real_announcements)} 条记录")
    
    for i, announcement in enumerate(real_announcements[-3:]):  # 显示最后3条
        print(f"   {i+1}. {announcement['message']}")
    
    # 测试去重功能
    print("\n--- 测试去重功能 ---")
    
    initial_count = len(panel.drop_manager.get_recent_drops())
    
    # 添加重复掉落
    panel.add_drop_info("重复武器", "武器", "普通", "重复怪物", "重复地图")
    panel.add_drop_info("重复武器", "武器", "普通", "重复怪物", "重复地图")  # 应该被去重
    
    final_count = len(panel.drop_manager.get_recent_drops())
    print(f"✅ 去重测试: 添加2次相同掉落，实际增加 {final_count - initial_count} 条")
    
    pygame.quit()
    
    print("\n🎉 真实掉落信息集成测试完成！")
    print("现在公告面板只会显示真实的游戏数据，不再有固定的示例数据。")


def test_empty_panel_display():
    """测试空面板显示"""
    print("\n=== 测试空面板显示 ===")
    
    pygame.init()
    screen = pygame.display.set_mode((100, 100))
    
    # 创建一个全新的公告面板（不添加任何数据）
    mock_user_message_manager = Mock()
    mock_user_message_manager.get_messages_by_type.return_value = []

    panel = AnnouncementPanel(
        screen=screen,
        battle_manager=Mock(),
        position=(0, 0),
        size=(400, 300),
        player=Mock(),
        user_message_manager=mock_user_message_manager
    )
    
    # 不连接战斗管理器，检查各标签页的初始状态
    for i, tab_name in enumerate(panel.tabs):
        panel.current_tab = i
        messages = panel.get_current_tab_messages()
        print(f"✅ {tab_name} (未连接): {len(messages)} 条消息")
        
        if messages:
            for msg in messages:
                print(f"   - {msg['message']}")
    
    pygame.quit()


if __name__ == "__main__":
    print("开始测试真实掉落信息集成...")
    print("=" * 60)
    
    try:
        test_empty_panel_display()
        test_real_drop_integration()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！公告面板现在只显示真实的游戏数据。")
        print("\n📝 说明:")
        print("- 系统公告：只在战斗管理器连接时添加欢迎消息")
        print("- 玩家掉落：只显示真实的战斗掉落")
        print("- 重要信息：只显示真实的游戏事件（升级、成就、死亡等）")
        print("- 所有示例数据已移除")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
