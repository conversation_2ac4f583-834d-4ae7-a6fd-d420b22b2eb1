import pygame
from game.ui.ui_panel import UIPanel

class RankPanel(UIPanel):
    """
    排行榜面板，显示玩家排名
    """
    def __init__(self, screen, rank_manager, position, size):
        super().__init__(screen, position, size)
        
        # 排行榜管理器引用
        self.rank_manager = rank_manager
        
        # 设置面板标题
        self.title = "排名区"
        
        # 设置面板背景色
        self.background_color = (35, 35, 35)
        
        # 排行榜显示区域
        self.rank_rect = pygame.Rect(
            self.rect.left + 10,
            self.rect.top + 30,
            self.size[0] - 20,
            self.size[1] - 80
        )
        
        # 每行排名高度
        self.rank_line_height = 30
        
        # 可显示的排名行数
        self.visible_ranks = self.rank_rect.height // self.rank_line_height
        
        # 创建排行榜切换按钮
        self.create_rank_type_buttons()
        
        # 创建翻页按钮
        self.create_page_buttons()
    
    def create_rank_type_buttons(self):
        """
        创建排行榜类型切换按钮
        """
        # 战力排行按钮
        self.add_button(
            "战力",
            (10, self.size[1] - 30, 60, 20),
            lambda: self.switch_rank_type("battle_power")
        )
        
        # 等级排行按钮
        self.add_button(
            "等级",
            (80, self.size[1] - 30, 60, 20),
            lambda: self.switch_rank_type("level")
        )
    
    def create_page_buttons(self):
        """
        创建翻页按钮
        """
        # 上一页按钮
        self.add_button(
            "<<",
            (10, self.size[1] - 60, 30, 20),
            self.prev_page
        )
        
        # 下一页按钮
        self.add_button(
            ">>",
            (self.size[0] - 40, self.size[1] - 60, 30, 20),
            self.next_page
        )
    
    def render(self):
        """
        渲染排行榜面板
        """
        super().render()
        
        # 渲染排行榜标题
        self.render_rank_title()
        
        # 渲染排行榜数据
        self.render_rank_data()
    
    def render_rank_title(self):
        """
        渲染排行榜标题
        """
        title_text = "战力风云榜" if self.rank_manager.current_rank_type == "battle_power" else "等级排行榜"
        title_surface = self.title_font.render(title_text, True, (255, 215, 0))
        title_rect = title_surface.get_rect(midtop=(self.rect.centerx, self.rect.top + 30))
        self.screen.blit(title_surface, title_rect)
    
    def render_rank_data(self):
        """
        渲染排行榜数据
        """
        # 获取当前排行榜数据
        ranks = self.rank_manager.get_current_ranks()
        
        # 渲染每一行排名
        for i, rank_data in enumerate(ranks):
            rank_y = self.rank_rect.top + i * self.rank_line_height + 30
            
            # 渲染排名
            rank_text = f"{rank_data['rank']}"
            rank_surface = self.normal_font.render(rank_text, True, self.normal_color)
            self.screen.blit(rank_surface, (self.rank_rect.left + 10, rank_y))
            
            # 渲染玩家名称
            name_text = rank_data['name']
            name_surface = self.normal_font.render(name_text, True, self.normal_color)
            self.screen.blit(name_surface, (self.rank_rect.left + 40, rank_y))
            
            # 渲染玩家战力或等级
            if self.rank_manager.current_rank_type == "battle_power":
                value_text = f"战力:{rank_data['battle_power']}"
            else:
                value_text = f"Lv.{rank_data['level']}"
            
            value_color = (0, 255, 0) if i < 3 else self.normal_color  # 前三名使用绿色
            value_surface = self.normal_font.render(value_text, True, value_color)
            self.screen.blit(value_surface, (self.rank_rect.left + 120, rank_y))
            
            # 绘制分隔线
            pygame.draw.line(
                self.screen,
                (50, 50, 50),
                (self.rank_rect.left, rank_y + self.rank_line_height - 1),
                (self.rank_rect.right, rank_y + self.rank_line_height - 1),
                1
            )
    
    def update(self):
        """
        更新排行榜面板
        """
        pass
    
    def switch_rank_type(self, rank_type):
        """
        切换排行榜类型
        """
        if self.rank_manager.set_current_rank_type(rank_type):
            print(f"切换排行榜类型: {rank_type}")
    
    def prev_page(self):
        """
        上一页
        """
        if self.rank_manager.prev_page():
            print("排行榜上一页")
    
    def next_page(self):
        """
        下一页
        """
        if self.rank_manager.next_page():
            print("排行榜下一页") 