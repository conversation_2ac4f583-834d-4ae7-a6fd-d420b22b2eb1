# -*- coding: utf-8 -*-
"""
药水管理面板
经典表格式药水列表界面，支持HP/MP药水管理和自动使用配置
"""
import pygame
import json
import os
from game.ui.ui_panel import UIPanel
from game.core.resource_manager import resource_path, get_game_data_path
from game.data.inventory import Item

# 药水类型常量
HP_POTION = "hp_potion"
MP_POTION = "mp_potion"
SPECIAL_POTION = "special_potion"

class Potion(Item):
    """
    药水类物品，扩展基础Item类
    """
    def __init__(self, id, name, icon_path=None, description="", potion_type=HP_POTION, 
                 hp_restore=0, mp_restore=0, cooldown=1000, price=0, currency="金币"):
        """
        初始化药水
        
        Args:
            id: 药水ID
            name: 药水名称
            icon_path: 药水图标路径
            description: 药水描述
            potion_type: 药水类型 (HP_POTION, MP_POTION, SPECIAL_POTION)
            hp_restore: HP回复量
            mp_restore: MP回复量
            cooldown: 冷却时间(毫秒)
            price: 价格
            currency: 货币类型
        """
        super().__init__(id, name, icon_path, "consumable", description, stackable=True, max_stack_size=999)
        self.potion_type = potion_type
        self.hp_restore = hp_restore
        self.mp_restore = mp_restore
        self.cooldown = cooldown
        self.price = price
        self.currency = currency
        self.enabled = False  # 是否启用自动使用

class PotionConfig:
    """
    药水配置管理类
    """
    def __init__(self):
        self.hp_threshold = 50  # HP自动使用阈值(百分比)
        self.mp_threshold = 30  # MP自动使用阈值(百分比)
        self.auto_potion_enabled = False  # 自动药水总开关
        self.enabled_potions = set()  # 启用的药水ID集合
        
    def save_config(self):
        """保存配置到文件"""
        try:
            import json
            import os
            
            config_data = {
                "hp_threshold": self.hp_threshold,
                "mp_threshold": self.mp_threshold,
                "auto_potion_enabled": self.auto_potion_enabled,
                "enabled_potions": list(self.enabled_potions)  # set转换为list用于JSON序列化
            }
            
            # 确保配置目录存在
            config_dir = "game/data"
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            config_path = os.path.join(config_dir, "potion_config.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 药水配置已保存到: {config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 保存药水配置失败: {e}")
            return False
        
    def load_config(self):
        """从文件加载配置"""
        try:
            import json
            import os
            
            config_path = get_game_data_path("potion_config.json")
            if not os.path.exists(config_path):
                print("📁 药水配置文件不存在，使用默认配置")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.hp_threshold = config_data.get("hp_threshold", 50)
            self.mp_threshold = config_data.get("mp_threshold", 30)
            self.auto_potion_enabled = config_data.get("auto_potion_enabled", False)
            self.enabled_potions = set(config_data.get("enabled_potions", []))  # list转换为set
            
            print(f"✅ 药水配置已加载: HP阈值{self.hp_threshold}%, MP阈值{self.mp_threshold}%, 启用{len(self.enabled_potions)}个药水")
            return True
            
        except Exception as e:
            print(f"❌ 加载药水配置失败: {e}")
            return False

class MedicinePanel(UIPanel):
    """
    药水管理面板类，经典表格式界面
    """
    
    def __init__(self, screen, position=(650, 100), size=(500, 600), battle_panel=None, player=None, potion_effects_manager=None):
        """
        初始化药水管理面板
        
        Args:
            screen: 屏幕对象
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
            battle_panel: 战斗面板引用，用于同步自动吃药配置
            player: 玩家对象引用，用于药水效果应用
            potion_effects_manager: 药水效果管理器引用
        """
        super().__init__(screen, position, size)
        self.title = "药水管理"
        
        # 战斗面板引用
        self.battle_panel = battle_panel
        
        # 玩家对象引用 - 修复缺失的引用
        self.player = player
        if not self.player:
            print("⚠️ 药水面板初始化时未提供player对象，某些功能可能无法正常工作")
        
        # 药水效果管理器引用
        self.potion_effects_manager = potion_effects_manager
        if not self.potion_effects_manager:
            print("⚠️ 药水面板初始化时未提供药水效果管理器，将使用传统方式")
        
        # 当前选中的标签页
        self.current_tab = HP_POTION
        
        # 药水配置
        self.potion_config = PotionConfig()
        
        # 🔧 新增：加载保存的配置
        self.potion_config.load_config()
        
        # 从配置文件加载药水数据
        self.potions = {HP_POTION: [], MP_POTION: [], SPECIAL_POTION: []}
        self.potion_counts = {}
        self._load_potions_from_config()
        
        # 如果没有加载到数据，使用默认测试数据
        if not any(self.potions.values()):
            self._load_default_potions()
        
        # 🔧 新增：应用加载的配置到药水启用状态
        self._apply_loaded_config_to_potions()
        
        # 选中的药水行
        self.selected_row = -1
        
        # 滑块拖拽状态
        self.dragging_hp_slider = False
        self.dragging_mp_slider = False
        
        # 初始化UI组件
        self._init_ui_components()
        
    def _load_potions_from_config(self):
        """从items_config.json加载药水数据"""
        try:
            config_path = get_game_data_path("items_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    items_config = json.load(f)
                
                # items_config.json直接包含所有物品，没有"consumables"分组
                print(f"🔍 找到 {len(items_config)} 个物品")
                potion_id = 1
                
                for item_name, item_data in items_config.items():
                    print(f"🔍 检查物品: {item_name}, 类别: {item_data.get('category')}")
                    
                    # 只处理药水类别的消耗品
                    if item_data.get("category") != "potion":
                        continue
                        
                    # 根据效果类型分类药水
                    effects = item_data.get("effects", {})
                    hp_restore = effects.get("hp_restore", 0)
                    mp_restore = effects.get("mp_restore", 0)
                    print(f"🔍 药水 {item_name}: HP+{hp_restore}, MP+{mp_restore}")
                    
                    # 确定药水类型
                    if hp_restore > 0 and mp_restore > 0:
                        potion_type = SPECIAL_POTION
                    elif hp_restore > 0:
                        potion_type = HP_POTION
                    elif mp_restore > 0:
                        potion_type = MP_POTION
                    else:
                        continue  # 跳过没有恢复效果的物品
                    
                    # 创建药水对象
                    potion = Potion(
                        id=potion_id,
                        name=item_name,
                        icon_path=item_data.get("icon"),
                        description=item_data.get("description", ""),
                        potion_type=potion_type,
                        hp_restore=hp_restore,
                        mp_restore=mp_restore,
                        cooldown=effects.get("cooldown", 1000),
                        price=item_data.get("sell_price", 0),  # 使用sell_price字段
                        currency=item_data.get("currency", "金币")
                    )
                    
                    self.potions[potion_type].append(potion)
                    # 模拟背包中的数量（实际应该从背包系统获取）
                    self.potion_counts[potion_id] = 10  # 默认数量
                    print(f"✅ 添加药水: {item_name} ({potion_type})")
                    potion_id += 1
                
                print(f"✅ 成功从配置文件加载 {sum(len(potions) for potions in self.potions.values())} 种药水")
                
        except Exception as e:
            print(f"❌ 加载药水配置失败: {e}")
            import traceback
            traceback.print_exc()
            
    def _load_default_potions(self):
        """加载默认测试药水数据"""
        self.potions = {
            HP_POTION: [
                Potion(1, "小型生命药水", None, "回复100点HP", HP_POTION, 100, 0, 1000, 50, "金币"),
                Potion(2, "中型生命药水", None, "回复300点HP", HP_POTION, 300, 0, 1000, 150, "金币"),
                Potion(3, "大型生命药水", None, "回复800点HP", HP_POTION, 800, 0, 1000, 400, "金币"),
                Potion(4, "超级生命药水", None, "回复2000点HP", HP_POTION, 2000, 0, 1000, 1000, "金币"),
            ],
            MP_POTION: [
                Potion(11, "小型魔法药水", None, "回复50点MP", MP_POTION, 0, 50, 1000, 30, "金币"),
                Potion(12, "中型魔法药水", None, "回复150点MP", MP_POTION, 0, 150, 1000, 90, "金币"),
                Potion(13, "大型魔法药水", None, "回复400点MP", MP_POTION, 0, 400, 1000, 240, "金币"),
                Potion(14, "超级魔法药水", None, "回复1000点MP", MP_POTION, 0, 1000, 1000, 600, "金币"),
            ],
            SPECIAL_POTION: [
                Potion(21, "全能药水", None, "同时回复HP和MP", SPECIAL_POTION, 500, 250, 2000, 800, "金币"),
                Potion(22, "瞬间回复药水", None, "瞬间回复大量HP", SPECIAL_POTION, 1500, 0, 500, 2000, "元宝"),
            ]
        }
        
        # 药水数量数据(模拟背包数据)
        self.potion_counts = {
            1: 25, 2: 15, 3: 8, 4: 3,
            11: 30, 12: 20, 13: 10, 14: 5,
            21: 2, 22: 1
        }
        print("⚠️ 使用默认测试药水数据")
        
    def _apply_loaded_config_to_potions(self):
        """应用加载的配置到药水启用状态"""
        for potion_type, potion_list in self.potions.items():
            for potion in potion_list:
                if potion.id in self.potion_config.enabled_potions:
                    potion.enabled = True
                else:
                    potion.enabled = False
        
    def _init_ui_components(self):
        """初始化UI组件"""
        # 标签页区域
        self.tab_height = 30
        self.tab_area = pygame.Rect(0, self.title_height + 5, self.size[0], self.tab_height)
        
        # 标签页按钮
        tab_width = 120
        self.hp_tab_rect = pygame.Rect(10, self.title_height + 10, tab_width, 20)
        self.mp_tab_rect = pygame.Rect(140, self.title_height + 10, tab_width, 20)
        self.special_tab_rect = pygame.Rect(270, self.title_height + 10, tab_width, 20)
        
        # 表格区域
        table_top = self.title_height + self.tab_height + 15
        self.table_area = pygame.Rect(10, table_top, self.size[0] - 20, 300)
        
        # 表格列定义 [序号, 名称, 单次回复, 总数, 间隔, 价格, 货币]
        self.column_widths = [40, 120, 80, 60, 60, 80, 60]
        self.column_names = ["NO.", "名称", "单次回复", "总数", "间隔", "价格", "货币"]
        
        # 保护设置区域
        protection_top = table_top + 320
        self.protection_area = pygame.Rect(10, protection_top, self.size[0] - 20, 120)
        
        # 🔧 改进：HP阈值滑块 - 使用加载的配置值
        self.hp_slider_rect = pygame.Rect(120, protection_top + 20, 200, 20)
        hp_ratio = self.potion_config.hp_threshold / 100.0
        self.hp_slider_handle = pygame.Rect(120 + int(200 * hp_ratio) - 5, protection_top + 20, 10, 20)
        
        # 🔧 改进：MP阈值滑块 - 使用加载的配置值
        self.mp_slider_rect = pygame.Rect(120, protection_top + 60, 200, 20)
        mp_ratio = self.potion_config.mp_threshold / 100.0
        self.mp_slider_handle = pygame.Rect(120 + int(200 * mp_ratio) - 5, protection_top + 60, 10, 20)
        
        # 控制按钮区域
        button_top = protection_top + 140
        self.add_button("保存配置", (20, button_top, 80, 30), self._save_config)
        self.add_button("重置设置", (120, button_top, 80, 30), self._reset_config)
        self.add_button("应用更改", (220, button_top, 80, 30), self._apply_config)
        auto_text = "禁用自动药水" if self.potion_config.auto_potion_enabled else "启用自动药水"
        self.add_button(auto_text, (320, button_top, 100, 30), self._toggle_auto_potion)
        self.add_button("关闭", (430, button_top, 60, 30), self._close_panel)
        
    def handle_event(self, event):
        """处理事件 - 修复版，完全消费鼠标事件防止穿透"""
        if not self.visible:
            return False
            
        # 处理鼠标点击事件
        if event.type == pygame.MOUSEBUTTONDOWN:
            # 先检查是否点击在面板区域内
            if self.rect.collidepoint(event.pos):
                # 在面板内部的点击
                if event.button == 1:  # 左键
                    # 调用父类事件处理（处理按钮点击）
                    if super().handle_event(event):
                        return True
                        
                    mouse_pos = event.pos
                    
                    # 检查标签页点击
                    if self._check_tab_click(mouse_pos):
                        return True
                        
                    # 检查表格行点击
                    if self._check_table_click(mouse_pos):
                        return True
                        
                    # 检查滑块点击
                    if self._check_slider_click(mouse_pos):
                        return True
                
                # 在面板内的任何点击都应该被消费，防止穿透
                return True
            else:
                # 点击在面板外部，不处理但也不关闭面板（由UIManager处理）
                return False
                
        elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:
            # 在面板内才处理
            if self.rect.collidepoint(event.pos):
                self.dragging_hp_slider = False
                self.dragging_mp_slider = False
                return True
            
        elif event.type == pygame.MOUSEMOTION:
            # 只有在拖拽状态下才处理
            if self.dragging_hp_slider or self.dragging_mp_slider:
                self._handle_slider_drag(event.pos)
                return True
                
        return False
        
    def _check_tab_click(self, mouse_pos):
        """检查标签页点击"""
        local_pos = (mouse_pos[0] - self.rect.left, mouse_pos[1] - self.rect.top)
        
        if self.hp_tab_rect.collidepoint(local_pos):
            self.current_tab = HP_POTION
            self.selected_row = -1
            return True
        elif self.mp_tab_rect.collidepoint(local_pos):
            self.current_tab = MP_POTION
            self.selected_row = -1
            return True
        elif self.special_tab_rect.collidepoint(local_pos):
            self.current_tab = SPECIAL_POTION
            self.selected_row = -1
            return True
            
        return False
        
    def _check_table_click(self, mouse_pos):
        """检查表格行点击"""
        local_pos = (mouse_pos[0] - self.rect.left, mouse_pos[1] - self.rect.top)
        
        if self.table_area.collidepoint(local_pos):
            # 计算点击的行
            header_height = 25
            row_height = 22
            
            if local_pos[1] > self.table_area.top + header_height:
                row_index = int((local_pos[1] - self.table_area.top - header_height) / row_height)
                current_potions = self.potions.get(self.current_tab, [])
                
                if 0 <= row_index < len(current_potions):
                    # 检查是否点击了复选框区域
                    checkbox_x = self.table_area.left + 5
                    if checkbox_x <= local_pos[0] <= checkbox_x + 15:
                        # 切换药水启用状态
                        potion = current_potions[row_index]
                        potion.enabled = not potion.enabled
                        if potion.enabled:
                            self.potion_config.enabled_potions.add(potion.id)
                        else:
                            self.potion_config.enabled_potions.discard(potion.id)
                        
                        # 🔧 新增：自动保存配置
                        self._auto_save_config()
                    else:
                        # 点击药水行 - 直接购买并使用药水
                        potion = current_potions[row_index]
                        self._buy_and_use_potion(potion)
                        self.selected_row = row_index
                    return True
                    
        return False
        
    def _check_slider_click(self, mouse_pos):
        """检查滑块点击"""
        local_pos = (mouse_pos[0] - self.rect.left, mouse_pos[1] - self.rect.top)
        
        if self.hp_slider_rect.collidepoint(local_pos):
            self.dragging_hp_slider = True
            self._update_hp_slider(local_pos[0])
            return True
        elif self.mp_slider_rect.collidepoint(local_pos):
            self.dragging_mp_slider = True
            self._update_mp_slider(local_pos[0])
            return True
            
        return False
        
    def _handle_slider_drag(self, mouse_pos):
        """处理滑块拖拽"""
        local_x = mouse_pos[0] - self.rect.left
        
        if self.dragging_hp_slider:
            self._update_hp_slider(local_x)
        elif self.dragging_mp_slider:
            self._update_mp_slider(local_x)
            
    def _update_hp_slider(self, x):
        """更新HP滑块位置"""
        slider_x = max(self.hp_slider_rect.left, min(x, self.hp_slider_rect.right))
        progress = (slider_x - self.hp_slider_rect.left) / self.hp_slider_rect.width
        self.potion_config.hp_threshold = int(progress * 100)
        self.hp_slider_handle.centerx = slider_x
        
        # 同步到战斗面板
        if self.battle_panel:
            self.battle_panel.hp_threshold = self.potion_config.hp_threshold
        
        # 🔧 新增：自动保存配置
        self._auto_save_config()
        
    def _update_mp_slider(self, x):
        """更新MP滑块位置"""
        slider_x = max(self.mp_slider_rect.left, min(x, self.mp_slider_rect.right))
        progress = (slider_x - self.mp_slider_rect.left) / self.mp_slider_rect.width
        self.potion_config.mp_threshold = int(progress * 100)
        self.mp_slider_handle.centerx = slider_x
        
        # 同步到战斗面板
        if self.battle_panel:
            self.battle_panel.mp_threshold = self.potion_config.mp_threshold
        
        # 🔧 新增：自动保存配置
        self._auto_save_config()
        
    def _save_config(self):
        """保存配置"""
        self.potion_config.save_config()
        print("药水配置已保存")
    
    def _auto_save_config(self):
        """
        自动保存配置（静默保存，无提示）
        在配置修改时自动调用，避免用户丢失设置
        """
        try:
            # 静默保存，不显示提示
            self.potion_config.save_config()
            
            # 同步到战斗面板的配置保存
            if self.battle_panel and hasattr(self.battle_panel, '_save_potion_config_to_file'):
                self.battle_panel.auto_potion_enabled = self.potion_config.auto_potion_enabled
                self.battle_panel.hp_threshold = self.potion_config.hp_threshold
                self.battle_panel.mp_threshold = self.potion_config.mp_threshold
                self.battle_panel._save_potion_config_to_file()
                
        except Exception as e:
            print(f"❌ 自动保存药水配置失败: {e}")
        
    def _reset_config(self):
        """重置配置"""
        self.potion_config.hp_threshold = 50
        self.potion_config.mp_threshold = 30
        self.potion_config.auto_potion_enabled = False
        self.potion_config.enabled_potions.clear()
        
        # 重置所有药水启用状态
        for potion_list in self.potions.values():
            for potion in potion_list:
                potion.enabled = False
                
        # 更新滑块位置
        self.hp_slider_handle.centerx = self.hp_slider_rect.left + int(self.hp_slider_rect.width * 0.5)
        self.mp_slider_handle.centerx = self.mp_slider_rect.left + int(self.mp_slider_rect.width * 0.3)
        
        print("药水配置已重置")
        
    def _apply_config(self):
        """应用配置 - 改进版，自动启用药水"""
        self.potion_config.auto_potion_enabled = True
        
        # 🔧 自动启用第一个HP和MP药水
        hp_potions = self.potions.get(HP_POTION, [])
        mp_potions = self.potions.get(MP_POTION, [])
        
        # 如果没有启用任何药水，自动启用第一个可用药水
        if not self.potion_config.enabled_potions:
            if hp_potions:
                first_hp_potion = hp_potions[0]
                first_hp_potion.enabled = True
                self.potion_config.enabled_potions.add(first_hp_potion.id)
                print(f"🔧 自动启用HP药水: {first_hp_potion.name}")
            
            if mp_potions:
                first_mp_potion = mp_potions[0]
                first_mp_potion.enabled = True
                self.potion_config.enabled_potions.add(first_mp_potion.id)
                print(f"🔧 自动启用MP药水: {first_mp_potion.name}")
        
        print(f"药水配置已应用 - HP阈值: {self.potion_config.hp_threshold}%, MP阈值: {self.potion_config.mp_threshold}%")
        print(f"已启用药水数量: {len(self.potion_config.enabled_potions)}")
        
        # 同步到战斗面板
        if self.battle_panel:
            self.battle_panel.auto_potion_enabled = self.potion_config.auto_potion_enabled
            self.battle_panel.hp_threshold = self.potion_config.hp_threshold
            self.battle_panel.mp_threshold = self.potion_config.mp_threshold
        
        # 🔧 新增：自动保存配置
        self._auto_save_config()
        
    def _toggle_auto_potion(self):
        """切换自动药水功能"""
        self.potion_config.auto_potion_enabled = not self.potion_config.auto_potion_enabled
        status = "启用" if self.potion_config.auto_potion_enabled else "禁用"
        print(f"自动药水功能已{status}")
        
        # 同步到战斗面板
        if self.battle_panel:
            self.battle_panel.auto_potion_enabled = self.potion_config.auto_potion_enabled
            self.battle_panel.hp_threshold = self.potion_config.hp_threshold
            self.battle_panel.mp_threshold = self.potion_config.mp_threshold
        
        # 更新按钮文本
        for button in self.buttons:
            if button["text"] in ["启用自动药水", "禁用自动药水"]:
                button["text"] = "禁用自动药水" if self.potion_config.auto_potion_enabled else "启用自动药水"
                break
        
        # 🔧 新增：自动保存配置
        self._auto_save_config()
                
    def _buy_and_use_potion(self, potion):
        """购买并使用药水"""
        try:
            # 检查玩家是否有足够的金币
            if not self._check_player_gold(potion.price):
                print(f"金币不足，无法购买{potion.name}")
                return False
            
            # 扣除金币
            if not self._deduct_player_gold(potion.price):
                print(f"扣除金币失败")
                return False
            
            # 使用药水效果
            if self._apply_potion_effect(potion):
                print(f"成功使用{potion.name}，花费{potion.price}金币")
                return True
            else:
                # 如果使用失败，退还金币
                self._add_player_gold(potion.price)
                print(f"使用{potion.name}失败，已退还金币")
                return False
                
        except Exception as e:
            print(f"购买并使用药水时出错: {e}")
            return False
    
    def _check_player_gold(self, amount):
        """检查玩家是否有足够的金币"""
        try:
            if hasattr(self, 'player') and self.player:
                currencies = getattr(self.player, 'currencies', {})
                current_gold = currencies.get('gold', 0)
                return current_gold >= amount
            return False
        except Exception as e:
            print(f"检查金币时出错: {e}")
            return False
    
    def _deduct_player_gold(self, amount):
        """扣除玩家金币"""
        try:
            if hasattr(self, 'player') and self.player:
                currencies = getattr(self.player, 'currencies', {})
                current_gold = currencies.get('gold', 0)
                if current_gold >= amount:
                    currencies['gold'] = current_gold - amount
                    # 保存玩家数据
                    self._save_player_data()
                    return True
            return False
        except Exception as e:
            print(f"扣除金币时出错: {e}")
            return False
    
    def _add_player_gold(self, amount):
        """增加玩家金币（用于退还）"""
        try:
            if hasattr(self, 'player') and self.player:
                currencies = getattr(self.player, 'currencies', {})
                currencies['gold'] = currencies.get('gold', 0) + amount
                self._save_player_data()
                return True
            return False
        except Exception as e:
            print(f"增加金币时出错: {e}")
            return False
    
    def _apply_potion_effect(self, potion):
        """应用药水效果 - 新版：使用药水效果管理器"""
        try:
            if not hasattr(self, 'player') or not self.player:
                print(f"❌ 药水面板缺少player对象引用")
                return False
            
            print(f"🔍 应用药水效果: {potion.name}, 类型: {potion.potion_type}")
            
            # 使用新的药水效果管理器
            if hasattr(self, 'potion_effects_manager') and self.potion_effects_manager:
                # 构建药水效果配置
                effects_config = {
                    'hp_restore': getattr(potion, 'hp_restore', 0),
                    'mp_restore': getattr(potion, 'mp_restore', 0),
                    'recovery_type': self._get_recovery_type(potion.name),
                    'cooldown': getattr(potion, 'cooldown', 3000)  # 毫秒
                }
                
                # 使用药水效果管理器应用效果
                return self.potion_effects_manager.use_potion(potion.name, effects_config)
            else:
                # 回退到传统方式
                print("⚠️ 使用传统药水效果应用方式")
                return self._apply_potion_effect_legacy(potion)
            
        except Exception as e:
            print(f"❌ 应用药水效果时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _get_recovery_type(self, potion_name: str) -> str:
        """获取药水的恢复类型"""
        try:
            # 从物品配置中读取恢复类型
            import json
            import os
            from game.core.resource_manager import get_game_data_path
            
            config_path = get_game_data_path("items_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    items_config = json.load(f)
                
                item_config = items_config.get(potion_name, {})
                effects = item_config.get('effects', {})
                return effects.get('recovery_type', 'instant')
            
            return 'instant'  # 默认瞬间恢复
            
        except Exception as e:
            print(f"❌ 获取恢复类型时出错: {e}")
            return 'instant'
    
    def _apply_potion_effect_legacy(self, potion):
        """传统药水效果应用方式（兼容性）"""
        try:
            # 根据药水类型恢复HP或MP - 修正使用常量而非字符串
            if potion.potion_type == HP_POTION:
                hp_amount = getattr(potion, 'hp_restore', 0)
                print(f"🩸 HP药水，恢复量: {hp_amount}")
                return self._restore_player_hp(hp_amount)
            elif potion.potion_type == MP_POTION:
                mp_amount = getattr(potion, 'mp_restore', 0)
                print(f"💙 MP药水，恢复量: {mp_amount}")
                return self._restore_player_mp(mp_amount)
            elif potion.potion_type == SPECIAL_POTION:
                # 特殊药水可能同时恢复HP和MP
                hp_amount = getattr(potion, 'hp_restore', 0)
                mp_amount = getattr(potion, 'mp_restore', 0)
                print(f"⭐ 特殊药水，HP恢复: {hp_amount}, MP恢复: {mp_amount}")
                hp_success = self._restore_player_hp(hp_amount) if hp_amount > 0 else True
                mp_success = self._restore_player_mp(mp_amount) if mp_amount > 0 else True
                return hp_success and mp_success
            else:
                print(f"❌ 未知药水类型: {potion.potion_type}")
                return False
            
        except Exception as e:
            print(f"❌ 传统药水效果应用时出错: {e}")
            return False
    
    def _restore_player_hp(self, amount):
        """恢复玩家HP"""
        try:
            if hasattr(self, 'player') and self.player and hasattr(self.player, 'max_hp'):
                # 优先使用current_hp，如果没有则使用hp
                if hasattr(self.player, 'current_hp'):
                    old_hp = self.player.current_hp
                    self.player.current_hp = min(self.player.current_hp + amount, self.player.max_hp)
                    restored = self.player.current_hp - old_hp
                    print(f"恢复HP: +{restored} (当前HP: {self.player.current_hp}/{self.player.max_hp})")
                else:
                    old_hp = self.player.hp
                    self.player.hp = min(self.player.hp + amount, self.player.max_hp)
                    restored = self.player.hp - old_hp
                    print(f"恢复HP: +{restored} (当前HP: {self.player.hp}/{self.player.max_hp})")
                self._save_player_data()
                return True
            return False
        except Exception as e:
            print(f"恢复HP时出错: {e}")
            return False
    
    def _restore_player_mp(self, amount):
        """恢复玩家MP"""
        try:
            if hasattr(self, 'player') and self.player and hasattr(self.player, 'mp') and hasattr(self.player, 'max_mp'):
                old_mp = self.player.mp
                self.player.mp = min(self.player.mp + amount, self.player.max_mp)
                restored = self.player.mp - old_mp
                print(f"恢复MP: +{restored} (当前MP: {self.player.mp}/{self.player.max_mp})")
                self._save_player_data()
                return True
            return False
        except Exception as e:
            print(f"恢复MP时出错: {e}")
            return False
    
    def _save_player_data(self):
        """保存玩家数据到文件 - 修复版，统一使用SaveLoadManager"""
        try:
            if hasattr(self, 'player') and self.player:
                # 🔧 修复：使用SaveLoadManager统一保存，避免数据分离
                from game.managers.save_load_manager import SaveLoadManager
                save_manager = SaveLoadManager()
                
                # 保存到autosave，与游戏启动时加载的文件保持一致
                if save_manager.save_player_data(self.player, "autosave"):
                    print("✅ 玩家数据已保存到统一存档")
                    return True
                else:
                    print("❌ 保存失败")
                    return False
        except Exception as e:
            print(f"保存玩家数据时出错: {e}")
            return False
                
    def _close_panel(self):
        """关闭面板 - 改进版，自动保存配置和玩家数据"""
        # 🔧 自动保存药水配置
        self.potion_config.save_config()
        
        # 🔧 自动保存玩家数据
        self._save_player_data()
        
        self.visible = False
        print("药水面板已关闭并自动保存配置")
        
    def render(self):
        """渲染面板"""
        if not self.visible:
            return
            
        # 调用父类渲染
        super().render()
        
        # 渲染标签页
        self._render_tabs()
        
        # 渲染药水表格
        self._render_potion_table()
        
        # 渲染保护设置
        self._render_protection_settings()
        
    def _render_tabs(self):
        """渲染标签页"""
        tabs = [
            (self.hp_tab_rect, "HP药水", HP_POTION),
            (self.mp_tab_rect, "MP药水", MP_POTION),
            (self.special_tab_rect, "特殊药水", SPECIAL_POTION)
        ]
        
        for tab_rect, tab_name, tab_type in tabs:
            # 调整为绝对坐标
            abs_rect = pygame.Rect(
                self.rect.left + tab_rect.x,
                self.rect.top + tab_rect.y,
                tab_rect.width,
                tab_rect.height
            )
            
            # 选中标签页高亮
            if tab_type == self.current_tab:
                pygame.draw.rect(self.screen, (80, 80, 120), abs_rect)
            else:
                pygame.draw.rect(self.screen, (60, 60, 60), abs_rect)
                
            pygame.draw.rect(self.screen, self.border_color, abs_rect, 1)
            
            # 渲染标签页文本
            text_surface = self.small_font.render(tab_name, True, self.normal_color)
            text_rect = text_surface.get_rect(center=abs_rect.center)
            self.screen.blit(text_surface, text_rect)
            
    def _render_potion_table(self):
        """渲染药水表格"""
        # 表格背景
        abs_table_rect = pygame.Rect(
            self.rect.left + self.table_area.x,
            self.rect.top + self.table_area.y,
            self.table_area.width,
            self.table_area.height
        )
        pygame.draw.rect(self.screen, (40, 40, 40), abs_table_rect)
        pygame.draw.rect(self.screen, self.border_color, abs_table_rect, 1)
        
        # 渲染表头
        self._render_table_header()
        
        # 渲染药水数据行
        self._render_potion_rows()
        
    def _render_table_header(self):
        """渲染表格头部"""
        header_y = self.rect.top + self.table_area.y + 5
        x_offset = self.rect.left + self.table_area.x + 20  # 留出复选框空间
        
        for i, (width, name) in enumerate(zip(self.column_widths, self.column_names)):
            # 渲染列标题
            text_surface = self.small_font.render(name, True, self.highlight_color)
            self.screen.blit(text_surface, (x_offset + 5, header_y))
            
            # 绘制列分隔线
            if i < len(self.column_widths) - 1:
                line_x = x_offset + width
                pygame.draw.line(self.screen, self.border_color, 
                               (line_x, header_y), (line_x, header_y + 20), 1)
            
            x_offset += width
            
        # 绘制表头底部分隔线
        pygame.draw.line(self.screen, self.border_color,
                        (self.rect.left + self.table_area.x, header_y + 22),
                        (self.rect.left + self.table_area.x + self.table_area.width, header_y + 22), 1)
        
    def _render_potion_rows(self):
        """渲染药水数据行"""
        current_potions = self.potions.get(self.current_tab, [])
        row_height = 22
        start_y = self.rect.top + self.table_area.y + 30
        
        for row_index, potion in enumerate(current_potions):
            row_y = start_y + row_index * row_height
            
            # 选中行高亮
            if row_index == self.selected_row:
                highlight_rect = pygame.Rect(
                    self.rect.left + self.table_area.x + 1,
                    row_y - 2,
                    self.table_area.width - 2,
                    row_height
                )
                pygame.draw.rect(self.screen, (60, 60, 80), highlight_rect)
                
            # 渲染复选框
            checkbox_rect = pygame.Rect(self.rect.left + self.table_area.x + 5, row_y + 3, 15, 15)
            pygame.draw.rect(self.screen, (200, 200, 200), checkbox_rect, 1)
            if potion.enabled:
                pygame.draw.rect(self.screen, (100, 200, 100), 
                               pygame.Rect(checkbox_rect.x + 2, checkbox_rect.y + 2, 11, 11))
                
            # 渲染数据列
            x_offset = self.rect.left + self.table_area.x + 20
            
            # 获取药水数量
            count = self.potion_counts.get(potion.id, 0)
            
            # 根据药水类型确定回复值显示
            if potion.potion_type == HP_POTION:
                restore_text = f"+{potion.hp_restore}HP"
            elif potion.potion_type == MP_POTION:
                restore_text = f"+{potion.mp_restore}MP"
            else:
                restore_text = f"+{potion.hp_restore}HP +{potion.mp_restore}MP"
                
            row_data = [
                str(row_index + 1),
                potion.name,
                restore_text,
                str(count),
                f"{potion.cooldown}ms",
                str(potion.price),
                potion.currency
            ]
            
            for i, (width, data) in enumerate(zip(self.column_widths, row_data)):
                # 渲染单元格数据
                text_surface = self.small_font.render(data, True, self.normal_color)
                self.screen.blit(text_surface, (x_offset + 5, row_y))
                
                # 绘制列分隔线
                if i < len(self.column_widths) - 1:
                    line_x = x_offset + width
                    pygame.draw.line(self.screen, (80, 80, 80), 
                                   (line_x, row_y - 2), (line_x, row_y + row_height - 2), 1)
                
                x_offset += width
                
    def _render_protection_settings(self):
        """渲染保护设置区域"""
        # 保护设置背景
        abs_protection_rect = pygame.Rect(
            self.rect.left + self.protection_area.x,
            self.rect.top + self.protection_area.y,
            self.protection_area.width,
            self.protection_area.height
        )
        pygame.draw.rect(self.screen, (45, 45, 45), abs_protection_rect)
        pygame.draw.rect(self.screen, self.border_color, abs_protection_rect, 1)
        
        # 标题
        title_surface = self.normal_font.render("保护设置", True, self.highlight_color)
        self.screen.blit(title_surface, (abs_protection_rect.x + 10, abs_protection_rect.y + 5))
        
        # HP阈值设置
        self._render_threshold_slider("HP阈值:", self.hp_slider_rect, self.hp_slider_handle, 
                                    self.potion_config.hp_threshold, abs_protection_rect.y + 30)
        
        # MP阈值设置
        self._render_threshold_slider("MP阈值:", self.mp_slider_rect, self.mp_slider_handle, 
                                    self.potion_config.mp_threshold, abs_protection_rect.y + 70)
        
        # 状态显示
        status_text = "启用" if self.potion_config.auto_potion_enabled else "禁用"
        status_color = (100, 200, 100) if self.potion_config.auto_potion_enabled else (200, 100, 100)
        status_surface = self.small_font.render(f"自动药水: {status_text}", True, status_color)
        self.screen.blit(status_surface, (abs_protection_rect.x + 350, abs_protection_rect.y + 50))
        
    def _render_threshold_slider(self, label, slider_rect, handle_rect, value, y_pos):
        """渲染阈值滑块"""
        # 标签
        label_surface = self.small_font.render(label, True, self.normal_color)
        self.screen.blit(label_surface, (self.rect.left + 20, y_pos))
        
        # 滑块轨道
        abs_slider_rect = pygame.Rect(
            self.rect.left + slider_rect.x,
            self.rect.top + slider_rect.y,
            slider_rect.width,
            slider_rect.height
        )
        pygame.draw.rect(self.screen, (80, 80, 80), abs_slider_rect)
        pygame.draw.rect(self.screen, self.border_color, abs_slider_rect, 1)
        
        # 滑块手柄
        abs_handle_rect = pygame.Rect(
            self.rect.left + handle_rect.x,
            self.rect.top + handle_rect.y,
            handle_rect.width,
            handle_rect.height
        )
        pygame.draw.rect(self.screen, (150, 150, 150), abs_handle_rect)
        pygame.draw.rect(self.screen, (200, 200, 200), abs_handle_rect, 1)
        
        # 数值显示
        value_surface = self.small_font.render(f"{value}%", True, self.normal_color)
        self.screen.blit(value_surface, (abs_slider_rect.right + 10, y_pos))