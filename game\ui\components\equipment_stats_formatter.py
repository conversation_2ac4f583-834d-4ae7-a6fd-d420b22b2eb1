"""
装备属性格式化组件
统一处理装备属性的显示格式化逻辑，减少代码重复
"""
from typing import Dict, Any, List, Tuple, Optional


class EquipmentStatsFormatter:
    """装备属性格式化器"""
    
    def __init__(self):
        """初始化格式化器"""
        # 范围属性配置
        self.range_attributes = [
            ('攻击下限', '攻击上限', '攻击力'),
            ('防御下限', '防御上限', '防御力'),
            ('魔法攻击下限', '魔法攻击上限', '魔法攻击'),
            ('道术攻击下限', '道术攻击上限', '道术攻击')
        ]
        
        # 百分比属性
        self.percentage_attributes = {
            '暴击率', '闪避率', '命中率', '格挡率', '反击率',
            'critical_rate', 'dodge_rate', 'hit_rate', 'block_rate'
        }
        
        # 速度属性
        self.speed_attributes = {
            '攻击速度', '移动速度', '施法速度',
            'attack_speed', 'move_speed', 'cast_speed'
        }
        
        # 属性显示优先级（用于排序）
        self.attribute_priority = {
            '攻击力': 1, '魔法攻击': 2, '道术攻击': 3,
            '防御力': 4, '魔法防御': 5,
            '生命值': 6, '魔法值': 7,
            '攻击速度': 8, '暴击率': 9,
            '准确': 10, '敏捷': 11, '幸运': 12
        }
    
    def format_combined_stats(self, base_stats: Dict[str, Any], 
                            equipment_stats: Dict[str, Any]) -> List[str]:
        """
        格式化合并的属性显示
        
        Args:
            base_stats: 基础属性
            equipment_stats: 装备属性
            
        Returns:
            格式化的属性显示列表
        """
        formatted_stats = []
        
        # 处理范围属性
        for min_key, max_key, display_name in self.range_attributes:
            base_min = base_stats.get(min_key, 0)
            base_max = base_stats.get(max_key, 0)
            eq_min = equipment_stats.get(min_key, 0)
            eq_max = equipment_stats.get(max_key, 0)
            
            if base_min > 0 or base_max > 0 or eq_min > 0 or eq_max > 0:
                total_min = base_min + eq_min
                total_max = base_max + eq_max
                
                if eq_min > 0 or eq_max > 0:
                    text = f"{display_name}: {base_min}-{base_max}(+{eq_min}-{eq_max}) = {total_min}-{total_max}"
                else:
                    text = f"{display_name}: {total_min}-{total_max}"
                
                formatted_stats.append(text)
        
        # 处理单值属性
        single_attributes = self._get_single_attributes(base_stats, equipment_stats)
        
        for attr_name in sorted(single_attributes.keys(), 
                               key=lambda x: self.attribute_priority.get(x, 999)):
            base_val, eq_val = single_attributes[attr_name]
            total_val = base_val + eq_val
            
            if eq_val > 0:
                text = f"{attr_name}: {self._format_value(attr_name, base_val)}(+{self._format_value(attr_name, eq_val)}) = {self._format_value(attr_name, total_val)}"
            else:
                text = f"{attr_name}: {self._format_value(attr_name, total_val)}"
            
            formatted_stats.append(text)
        
        return formatted_stats
    
    def format_equipment_summary(self, equipment_stats: Dict[str, Any]) -> List[str]:
        """
        格式化装备属性摘要（简化版）
        
        Args:
            equipment_stats: 装备属性
            
        Returns:
            简化的属性显示列表
        """
        summary = []
        
        # 只显示有加成的主要属性
        main_attributes = [
            ('攻击下限', '攻击上限', '攻击'),
            ('防御下限', '防御上限', '防御'),
            ('魔法攻击下限', '魔法攻击上限', '魔攻')
        ]
        
        for min_key, max_key, display_name in main_attributes:
            min_val = equipment_stats.get(min_key, 0)
            max_val = equipment_stats.get(max_key, 0)
            
            if min_val > 0 or max_val > 0:
                summary.append(f"{display_name}: +{min_val}-{max_val}")
        
        # 添加其他重要的单值属性
        important_single = ['生命值', '魔法值', '攻击速度', '暴击率']
        for attr in important_single:
            value = equipment_stats.get(attr, 0)
            if value > 0:
                summary.append(f"{attr}: +{self._format_value(attr, value)}")
        
        return summary[:3]  # 最多显示3个属性
    
    def calculate_equipment_score(self, equipment_stats: Dict[str, Any]) -> int:
        """
        计算装备评分
        
        Args:
            equipment_stats: 装备属性
            
        Returns:
            装备评分
        """
        score = 0
        
        # 攻击力评分权重
        attack_weights = {
            ('攻击下限', '攻击上限'): 10,
            ('魔法攻击下限', '魔法攻击上限'): 10,
            ('道术攻击下限', '道术攻击上限'): 10
        }
        
        for (min_key, max_key), weight in attack_weights.items():
            min_val = equipment_stats.get(min_key, 0)
            max_val = equipment_stats.get(max_key, 0)
            score += (min_val + max_val) * weight
        
        # 防御力评分权重
        defense_weights = {
            ('防御下限', '防御上限'): 8,
            '魔抗': 6
        }
        
        def_min = equipment_stats.get('防御下限', 0)
        def_max = equipment_stats.get('防御上限', 0)
        score += (def_min + def_max) * 8
        
        magic_def = equipment_stats.get('魔抗', 0)
        score += magic_def * 6
        
        # 其他属性评分权重
        other_weights = {
            '生命值': 2,
            '魔法值': 2,
            '准确': 15,
            '敏捷': 12,
            '幸运': 20,
            '攻击速度': 50,  # 攻击速度很重要
            '暴击率': 100   # 暴击率很重要
        }
        
        for attr, weight in other_weights.items():
            value = equipment_stats.get(attr, 0)
            if attr in self.percentage_attributes:
                # 百分比属性按百分比计算
                score += value * 100 * weight
            else:
                score += value * weight
        
        return int(score)
    
    def _get_single_attributes(self, base_stats: Dict[str, Any], 
                             equipment_stats: Dict[str, Any]) -> Dict[str, Tuple[float, float]]:
        """获取单值属性映射"""
        # 跳过范围属性的组成部分
        skip_attrs = set()
        for min_key, max_key, _ in self.range_attributes:
            skip_attrs.add(min_key)
            skip_attrs.add(max_key)
        
        # 收集所有单值属性
        all_attrs = set(base_stats.keys()) | set(equipment_stats.keys())
        single_attrs = all_attrs - skip_attrs
        
        result = {}
        for attr in single_attrs:
            base_val = base_stats.get(attr, 0)
            eq_val = equipment_stats.get(attr, 0)
            
            # 只包含有值的属性
            if base_val != 0 or eq_val != 0:
                result[attr] = (base_val, eq_val)
        
        return result
    
    def _format_value(self, attr_name: str, value: Any) -> str:
        """格式化属性值"""
        if not isinstance(value, (int, float)):
            return str(value)
        
        # 百分比属性
        if any(percent_attr in attr_name.lower() for percent_attr in self.percentage_attributes):
            if isinstance(value, float) and value < 1:
                return f"{value * 100:.1f}%"
            else:
                return f"{value:.1f}%"
        
        # 速度属性
        if any(speed_attr in attr_name.lower() for speed_attr in self.speed_attributes):
            return f"{value:.1f}"
        
        # 普通数值属性
        if isinstance(value, float) and value.is_integer():
            return str(int(value))
        else:
            return str(value)
    
    def get_attribute_display_order(self) -> List[str]:
        """获取属性显示顺序"""
        return sorted(self.attribute_priority.keys(), 
                     key=lambda x: self.attribute_priority[x])


# 创建全局格式化器实例
equipment_stats_formatter = EquipmentStatsFormatter() 