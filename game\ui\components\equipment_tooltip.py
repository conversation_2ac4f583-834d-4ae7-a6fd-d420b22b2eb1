"""
装备提示框组件 - 改进版本
负责显示装备的详细信息悬浮提示
"""
import pygame
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum
from game.ui.components.equipment_stats_formatter import EquipmentStatsFormatter
from game.data.equipment_converter import EquipmentConverter


class TextAlignment(Enum):
    """文本对齐方式"""
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"


@dataclass
class TooltipLine:
    """提示框行数据"""
    text: str
    color: Tuple[int, int, int]
    font: pygame.font.Font
    alignment: TextAlignment = TextAlignment.LEFT
    indent: int = 0  # 缩进级别


class EquipmentTooltip:
    """装备提示框组件 - 改进版本"""
    
    # 类级别的字体缓存
    _font_cache = {}
    
    # 品质映射表
    RARITY_TRANSLATION = {
        'common': '普通', 'uncommon': '精良', 'rare': '稀有',
        'epic': '史诗', 'legendary': '传说', 'normal': '普通',
        'fine': '精良', 'magic': '魔法', 'unique': '史诗'
    }
    
    RARITY_COLORS = {
        '普通': (200, 200, 200), '精良': (120, 200, 120),
        '稀有': (120, 120, 200), '史诗': (200, 120, 200),
        '传说': (255, 165, 0)
    }
    
    # 属性映射表
    ATTR_MAPPING = {
        # 英文属性名到中文显示名
        'attack': '攻击力',
        'defense': '防御力',
        'magic': '魔法攻击',
        'magic_defense': '魔抗',
        'taoism': '道术攻击',
        'accuracy': '准确',
        'agility': '敏捷',
        'luck': '幸运',
        'attack_speed': '攻速',
        'critical_rate': '暴击率',
        'hp': '生命值',
        'mp': '魔法值',
        'drop_rate_bonus': '掉落率加成',
        'exp_bonus': '经验加成',
        'gold_bonus': '金币加成',

        # 🔧 修复：添加中文属性名的映射（中文到中文，保持原样）
        '攻击下限': '攻击下限',
        '攻击上限': '攻击上限',
        '防御下限': '防御下限',
        '防御上限': '防御上限',
        '魔法攻击下限': '魔法攻击下限',
        '魔法攻击上限': '魔法攻击上限',
        '道术攻击下限': '道术攻击下限',
        '道术攻击上限': '道术攻击上限',
        '生命值': '生命值',
        '魔法值': '魔法值',
        '魔抗': '魔抗',
        '攻速': '攻速',
        '暴击率': '暴击率',
        '准确': '准确',
        '准确度': '准确度',
        '敏捷': '敏捷',
        '幸运': '幸运',
        '掉落率加成': '掉落率加成',
        '经验加成': '经验加成',
        '金币加成': '金币加成',
    }
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化装备提示框
        
        Args:
            config: 提示框配置
        """
        self.config = config
        self.visible = False
        self.equipment_data = None
        self.position = (0, 0)
        self.compare_equipment = None  # 用于对比的装备
        
        # 初始化字体
        self._init_fonts()
        
        # 缓存
        self._cached_surface = None
        self._cached_equipment_id = None
        self._cached_position = None
        
        # 动画相关
        self._fade_alpha = 0
        self._target_alpha = 255
        self._animation_speed = config.get('animation_speed', 15)
    
    def _init_fonts(self):
        """初始化字体（使用缓存）"""
        font_config = self.config.get('fonts', {})
        title_size = font_config.get('title_size', 16)
        normal_size = font_config.get('normal_size', 14)
        font_name = font_config.get('name', 'SimHei')
        
        # 使用缓存避免重复创建字体对象
        title_key = f"{font_name}_bold_{title_size}"
        normal_key = f"{font_name}_{normal_size}"
        
        if title_key not in self._font_cache:
            self._font_cache[title_key] = pygame.font.SysFont(font_name, title_size, bold=True)
        if normal_key not in self._font_cache:
            self._font_cache[normal_key] = pygame.font.SysFont(font_name, normal_size)
        
        self.title_font = self._font_cache[title_key]
        self.normal_font = self._font_cache[normal_key]
    
    def show(self, equipment_data: Dict[str, Any], position: Tuple[int, int], 
             compare_equipment: Optional[Dict[str, Any]] = None):
        """
        显示提示框
        
        Args:
            equipment_data: 装备数据
            position: 显示位置
            compare_equipment: 要对比的装备（可选）
        """
        self.equipment_data = equipment_data
        self.position = position
        self.compare_equipment = compare_equipment
        self.visible = True
        
        # 重置动画
        self._fade_alpha = 0
        self._target_alpha = 255
        
        # 检查是否需要重新生成缓存
        equipment_id = self._generate_equipment_id(equipment_data)
        if self._cached_equipment_id != equipment_id:
            self._cached_surface = None
            self._cached_equipment_id = equipment_id
    
    def hide(self):
        """隐藏提示框（带淡出动画）"""
        self._target_alpha = 0
    
    def update(self, dt: float):
        """
        更新提示框状态（用于动画）
        
        Args:
            dt: 时间增量（秒）
        """
        if self._fade_alpha != self._target_alpha:
            # 渐变动画
            alpha_diff = self._target_alpha - self._fade_alpha
            alpha_change = self._animation_speed * dt * 60  # 假设60FPS为基准
            
            if abs(alpha_diff) < alpha_change:
                self._fade_alpha = self._target_alpha
            else:
                self._fade_alpha += alpha_change if alpha_diff > 0 else -alpha_change
            
            # 完全透明时隐藏
            if self._fade_alpha == 0:
                self.visible = False
                self.equipment_data = None
                self.compare_equipment = None
    
    def render(self, screen: pygame.Surface, player):
        """
        渲染提示框
        
        Args:
            screen: 屏幕表面
            player: 玩家对象
        """
        if not self.visible or not self.equipment_data or self._fade_alpha == 0:
            return
        
        try:
            # 使用缓存的表面（如果可用）
            if self._cached_surface is None:
                self._cached_surface = self._create_tooltip_surface(player)
            
            if self._cached_surface:
                # 应用透明度
                if self._fade_alpha < 255:
                    temp_surface = self._cached_surface.copy()
                    temp_surface.set_alpha(int(self._fade_alpha))
                    surface_to_render = temp_surface
                else:
                    surface_to_render = self._cached_surface
                
                # 智能定位
                final_position = self._calculate_position(screen, surface_to_render.get_size())
                screen.blit(surface_to_render, final_position)
        except Exception as e:
            print(f"渲染装备提示框时出错: {e}")
    
    def _create_tooltip_surface(self, player) -> Optional[pygame.Surface]:
        """创建提示框表面"""
        if not self.equipment_data:
            return None
        
        lines = self._create_tooltip_lines(player)
        if not lines:
            return None
        
        # 计算所需尺寸
        max_width = 0
        total_height = self.config['padding'] * 2
        line_heights = []
        
        for line in lines:
            if line.text:
                text_surface = line.font.render(line.text, True, line.color)
                width = text_surface.get_width() + line.indent * 20
                max_width = max(max_width, width)
                line_heights.append(text_surface.get_height())
            else:
                line_heights.append(self.config['line_spacing'] // 2)
            total_height += line_heights[-1] + self.config.get('line_gap', 2)
        
        tooltip_width = min(max_width + self.config['padding'] * 2, self.config['max_width'])
        tooltip_height = total_height
        
        # 创建表面
        surface = pygame.Surface((tooltip_width, tooltip_height), pygame.SRCALPHA)
        
        # 绘制背景
        self._draw_background(surface, tooltip_width, tooltip_height)
        
        # 绘制文本
        current_y = self.config['padding']
        for i, line in enumerate(lines):
            if line.text:
                text_surface = line.font.render(line.text, True, line.color)
                x = self.config['padding'] + line.indent * 20
                
                # 处理对齐
                if line.alignment == TextAlignment.CENTER:
                    x = (tooltip_width - text_surface.get_width()) // 2
                elif line.alignment == TextAlignment.RIGHT:
                    x = tooltip_width - text_surface.get_width() - self.config['padding']
                
                surface.blit(text_surface, (x, current_y))
            
            current_y += line_heights[i] + self.config.get('line_gap', 2)
        
        return surface
    
    def _draw_background(self, surface: pygame.Surface, width: int, height: int):
        """绘制背景和边框（支持圆角和渐变）"""
        background_rect = pygame.Rect(0, 0, width, height)
        
        # 检查是否使用圆角
        corner_radius = self.config.get('corner_radius', 0)
        
        if corner_radius > 0:
            # 绘制圆角背景
            pygame.draw.rect(surface, self.config['background_color'], 
                           background_rect, border_radius=corner_radius)
            pygame.draw.rect(surface, self.config['border_color'], 
                           background_rect, self.config['border_width'], 
                           border_radius=corner_radius)
        else:
            # 普通矩形
            pygame.draw.rect(surface, self.config['background_color'], background_rect)
            pygame.draw.rect(surface, self.config['border_color'], 
                           background_rect, self.config['border_width'])
        
        # 可选：添加渐变效果
        if self.config.get('gradient_enabled', False):
            self._add_gradient(surface, background_rect)
    
    def _add_gradient(self, surface: pygame.Surface, rect: pygame.Rect):
        """添加渐变效果"""
        gradient_surface = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
        for y in range(rect.height):
            alpha = int(255 * (1 - y / rect.height) * 0.3)  # 30%的渐变强度
            pygame.draw.line(gradient_surface, (255, 255, 255, alpha), 
                           (0, y), (rect.width, y))
        surface.blit(gradient_surface, (rect.x, rect.y))
    
    def _create_tooltip_lines(self, player) -> List[TooltipLine]:
        """创建提示框行数据"""
        if not self.equipment_data:
            return []
        
        lines = []
        
        # 装备名称
        self._add_equipment_name(lines)
        
        # 品质和类型
        self._add_equipment_type(lines)
        
        # 空行
        lines.append(TooltipLine('', (0, 0, 0), self.normal_font))
        
        # 装备属性
        self._add_equipment_stats(lines)
        
        # 对比信息
        if self.compare_equipment:
            self._add_comparison_info(lines)
        
        # 特殊效果
        self._add_special_effects(lines)
        
        # 套装信息
        self._add_set_info(lines)
        
        # 装备需求
        self._add_equipment_requirements(lines, player)
        
        # 装备描述
        self._add_equipment_description(lines)
        
        # 价值信息
        self._add_value_info(lines)
        
        return lines
    
    def _add_equipment_name(self, lines: List[TooltipLine]):
        """添加装备名称"""
        name = self.equipment_data.get('name', '未知装备')
        enhancement = self.equipment_data.get('enhancement', 0)
        
        # 如果有强化等级，添加到名称中
        if enhancement > 0:
            name = f"{name} +{enhancement}"
        
        lines.append(TooltipLine(
            name, 
            self.config['title_color'], 
            self.title_font,
            TextAlignment.CENTER
        ))
    
    def _add_equipment_type(self, lines: List[TooltipLine]):
        """添加装备类型信息"""
        rarity = self._translate_rarity(self.equipment_data.get('rarity', '普通'))
        slot = self.equipment_data.get('slot', '未知')
        
        lines.append(TooltipLine(
            f"{rarity} {slot}",
            self._get_rarity_color(rarity),
            self.normal_font,
            TextAlignment.CENTER
        ))
    
    def _add_equipment_stats(self, lines: List[TooltipLine]):
        """添加装备属性行"""
        stats = self._get_equipment_stats()
        
        if not stats:
            return
        
        lines.append(TooltipLine(
            '装备属性:',
            self.config['normal_text_color'],
            self.normal_font
        ))
        
        # 按照优先级排序属性
        sorted_stats = self._sort_stats(stats)
        
        for stat_name, value in sorted_stats:
            text = self._format_stat_text(stat_name, value)
            # 🔧 修复：跳过值为0或None的属性
            if text is None:
                continue

            color = self._get_stat_color(stat_name, value)

            lines.append(TooltipLine(
                text,
                color,
                self.normal_font,
                indent=1
            ))
    
    def _add_comparison_info(self, lines: List[TooltipLine]):
        """添加装备对比信息"""
        if not self.compare_equipment:
            return
        
        current_stats = self._get_equipment_stats()
        compare_stats = self._get_equipment_stats(self.compare_equipment)
        
        # 计算差异
        stat_differences = {}
        all_stats = set(current_stats.keys()) | set(compare_stats.keys())
        
        for stat in all_stats:
            current_val = current_stats.get(stat, 0)
            compare_val = compare_stats.get(stat, 0)
            diff = current_val - compare_val
            
            if diff != 0:
                stat_differences[stat] = diff
        
        if stat_differences:
            lines.append(TooltipLine('', (0, 0, 0), self.normal_font))
            lines.append(TooltipLine(
                '对比当前装备:',
                self.config['normal_text_color'],
                self.normal_font
            ))
            
            for stat_name, diff in stat_differences.items():
                if diff > 0:
                    text = f"  ↑ {self._format_stat_name(stat_name)}: +{diff}"
                    color = (120, 200, 120)  # 绿色
                else:
                    text = f"  ↓ {self._format_stat_name(stat_name)}: {diff}"
                    color = (200, 120, 120)  # 红色
                
                lines.append(TooltipLine(text, color, self.normal_font))
    
    def _add_special_effects(self, lines: List[TooltipLine]):
        """添加特殊效果"""
        special_effects = self.equipment_data.get('special_effects', [])
        
        if special_effects:
            lines.append(TooltipLine('', (0, 0, 0), self.normal_font))
            lines.append(TooltipLine(
                '特殊效果:',
                (255, 215, 0),  # 金色
                self.normal_font
            ))
            
            for effect in special_effects:
                lines.append(TooltipLine(
                    f"  • {effect}",
                    (255, 215, 0),
                    self.normal_font
                ))
    
    def _add_set_info(self, lines: List[TooltipLine]):
        """添加套装信息"""
        set_info = self.equipment_data.get('set_info')
        
        if set_info:
            lines.append(TooltipLine('', (0, 0, 0), self.normal_font))
            lines.append(TooltipLine(
                f"套装: {set_info['name']}",
                (147, 112, 219),  # 紫色
                self.normal_font
            ))
            
            # 显示套装件数
            owned_pieces = set_info.get('owned_pieces', 0)
            total_pieces = set_info.get('total_pieces', 0)
            
            lines.append(TooltipLine(
                f"  ({owned_pieces}/{total_pieces})",
                (147, 112, 219),
                self.normal_font
            ))
            
            # 显示套装效果
            for pieces_required, effects in set_info.get('bonuses', {}).items():
                color = (147, 112, 219) if owned_pieces >= pieces_required else (128, 128, 128)
                lines.append(TooltipLine(
                    f"  {pieces_required}件:",
                    color,
                    self.normal_font
                ))
                
                for effect in effects:
                    lines.append(TooltipLine(
                        f"    • {effect}",
                        color,
                        self.normal_font
                    ))
    
    def _add_equipment_requirements(self, lines: List[TooltipLine], player):
        """添加装备需求"""
        required_level = self.equipment_data.get('required_level', 1)
        required_class = self.equipment_data.get('required_class', [])
        required_stats = self.equipment_data.get('required_stats', {})
        
        has_requirements = (required_level > 1 or required_class or required_stats)
        
        if has_requirements:
            lines.append(TooltipLine('', (0, 0, 0), self.normal_font))
            lines.append(TooltipLine(
                '装备需求:',
                self.config['normal_text_color'],
                self.normal_font
            ))
            
            # 等级需求
            if required_level > 1:
                meets_req = player.level >= required_level
                color = self.config['stat_color'] if meets_req else self.config['requirement_color']
                lines.append(TooltipLine(
                    f"  等级: {required_level}",
                    color,
                    self.normal_font
                ))
            
            # 职业需求
            if required_class:
                class_text = self._format_class_requirement(required_class)
                meets_req = self._check_class_requirement(player, required_class)
                color = self.config['stat_color'] if meets_req else self.config['requirement_color']
                
                lines.append(TooltipLine(
                    f"  职业: {class_text}",
                    color,
                    self.normal_font
                ))
            
            # 属性需求
            for stat, value in required_stats.items():
                player_stat = getattr(player, stat, 0)
                meets_req = player_stat >= value
                color = self.config['stat_color'] if meets_req else self.config['requirement_color']
                
                stat_name = self.ATTR_MAPPING.get(stat, stat)
                lines.append(TooltipLine(
                    f"  {stat_name}: {value}",
                    color,
                    self.normal_font
                ))
    
    def _add_equipment_description(self, lines: List[TooltipLine]):
        """添加装备描述"""
        description = self.equipment_data.get('description', '')
        
        if description:
            lines.append(TooltipLine('', (0, 0, 0), self.normal_font))
            
            # 支持多行描述
            desc_lines = description.split('\n')
            for desc_line in desc_lines:
                lines.append(TooltipLine(
                    desc_line,
                    (180, 180, 180),
                    self.normal_font
                ))
    
    def _add_value_info(self, lines: List[TooltipLine]):
        """添加价值信息"""
        if self.config.get('show_value', True):
            value = self.equipment_data.get('value', 0)
            if value > 0:
                lines.append(TooltipLine('', (0, 0, 0), self.normal_font))
                lines.append(TooltipLine(
                    f"售价: {value} 金币",
                    (255, 215, 0),
                    self.normal_font
                ))
    
    def _get_equipment_stats(self, equipment_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """获取装备属性（统一的属性获取方法）"""
        if equipment_data is None:
            equipment_data = self.equipment_data
        
        if not equipment_data:
            return {}
        
        # 标准化装备数据
        standardized = EquipmentConverter.standardize_item_dict(equipment_data)
        return standardized.get('attributes', {})
    
    def _sort_stats(self, stats: Dict[str, Any]) -> List[Tuple[str, Any]]:
        """按优先级排序属性"""
        # 定义属性显示优先级
        priority_order = [
            'attack', 'defense', 'magic', 'taoism',
            'hp', 'mp', 'magic_defense',
            'accuracy', 'agility', 'luck',
            'attack_speed', 'critical_rate',
            'exp_bonus', 'gold_bonus', 'drop_rate_bonus'
        ]
        
        sorted_stats = []
        
        # 先添加优先级列表中的属性
        for attr in priority_order:
            if attr in stats and stats[attr] != 0:
                sorted_stats.append((attr, stats[attr]))
        
        # 添加其他属性
        for attr, value in stats.items():
            if attr not in priority_order and value != 0:
                sorted_stats.append((attr, value))
        
        return sorted_stats
    
    def _format_stat_text(self, stat_name: str, value: Any) -> str:
        """格式化属性文本"""
        display_name = self.ATTR_MAPPING.get(stat_name, stat_name)

        # 🔧 修复：跳过值为0的属性
        if value == 0:
            return None

        # 处理范围值
        if isinstance(value, list) and len(value) == 2:
            return f"{display_name}: +{value[0]}-{value[1]}"

        # 处理百分比值
        if any(keyword in stat_name for keyword in ['rate', 'bonus', '率', '加成']):
            if isinstance(value, float) and value < 1:
                return f"{display_name}: +{value * 100:.1f}%"
            else:
                return f"{display_name}: +{value:.1f}%"

        # 处理浮点数
        if isinstance(value, float):
            if value.is_integer():
                return f"{display_name}: +{int(value)}"
            else:
                return f"{display_name}: +{value:.1f}"

        # 默认处理
        return f"{display_name}: +{value}"
    
    def _format_stat_name(self, stat_name: str) -> str:
        """格式化属性名称"""
        return self.ATTR_MAPPING.get(stat_name, stat_name)
    
    def _get_stat_color(self, stat_name: str, value: Any) -> Tuple[int, int, int]:
        """获取属性颜色"""
        # 可以根据属性类型返回不同颜色
        if any(keyword in stat_name for keyword in ['attack', 'magic', 'taoism', '攻击']):
            return (255, 150, 150)  # 浅红色
        elif any(keyword in stat_name for keyword in ['defense', '防御']):
            return (150, 150, 255)  # 浅蓝色
        elif any(keyword in stat_name for keyword in ['hp', 'mp', '生命', '魔法值']):
            return (150, 255, 150)  # 浅绿色
        else:
            return self.config['stat_color']
    
    def _format_class_requirement(self, required_class: Any) -> str:
        """格式化职业需求"""
        if isinstance(required_class, list):
            return '/'.join(required_class)
        return str(required_class)
    
    def _check_class_requirement(self, player, required_class: Any) -> bool:
        """检查职业需求"""
        if not required_class:
            return True
        
        if isinstance(required_class, list):
            return player.character_class in required_class
        return player.character_class == required_class
    
    def _calculate_position(self, screen: pygame.Surface, tooltip_size: Tuple[int, int]) -> Tuple[int, int]:
        """计算提示框的最佳显示位置（改进的智能定位）"""
        tooltip_width, tooltip_height = tooltip_size
        screen_width, screen_height = screen.get_size()
        
        # 获取鼠标偏移配置
        offset_x = self.config.get('mouse_offset_x', 15)
        offset_y = self.config.get('mouse_offset_y', -10)
        
        x, y = self.position
        x += offset_x
        y += offset_y
        
        # 首选右下角显示
        if x + tooltip_width <= screen_width and y + tooltip_height <= screen_height:
            return (x, y)
        
        # 如果右边空间不够，尝试左边
        if x + tooltip_width > screen_width:
            x = self.position[0] - tooltip_width - offset_x
        
        # 如果下边空间不够，尝试上边
        if y + tooltip_height > screen_height:
            y = self.position[1] - tooltip_height - offset_y
        
        # 确保不超出屏幕边界
        margin = self.config.get('screen_margin', 10)
        x = max(margin, min(x, screen_width - tooltip_width - margin))
        y = max(margin, min(y, screen_height - tooltip_height - margin))
        
        return (x, y)
    
    def _translate_rarity(self, rarity: str) -> str:
        """翻译品质名称"""
        return self.RARITY_TRANSLATION.get(rarity.lower(), rarity)
    
    def _get_rarity_color(self, rarity: str) -> Tuple[int, int, int]:
        """获取品质颜色"""
        return self.RARITY_COLORS.get(rarity, (200, 200, 200))
    
    def _generate_equipment_id(self, equipment_data: Dict[str, Any]) -> str:
        """
        生成装备唯一ID用于缓存
    
        Args:
            equipment_data: 装备数据字典
        
        Returns:
            装备的唯一ID字符串
        """
        if not equipment_data:
            return "empty_equipment"
    
        # 安全地获取并转换关键属性
        equipment_id = equipment_data.get('id', '')
        equipment_name = equipment_data.get('name', '')
        enhancement = equipment_data.get('enhancement', 0)
    
        # 处理stats - 可能是字典或其他类型
        stats = equipment_data.get('stats', {})
        if isinstance(stats, dict):
            # 将字典转换为排序后的字符串，确保相同内容生成相同ID
            stats_items = sorted(stats.items())
            stats_str = ','.join(f"{k}:{v}" for k, v in stats_items)
        else:
            stats_str = str(stats)
    
        # 构建唯一ID
        key_parts = [
            f"id_{equipment_id}",
            f"name_{equipment_name}",
            f"enhance_{enhancement}",
            f"stats_{stats_str}",
            f"compare_{self.compare_equipment is not None}"
        ]
    
        # 确保所有部分都是字符串，并清理特殊字符
        clean_parts = []
        for part in key_parts:
            # 转换为字符串并替换可能导致问题的字符
            clean_part = str(part).replace(' ', '_').replace('/', '_')
            clean_parts.append(clean_part)
    
        return '_'.join(clean_parts)