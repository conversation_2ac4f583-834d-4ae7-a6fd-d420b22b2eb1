#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Boss刷新状态面板
显示所有地图Boss的刷新状态和倒计时
"""

import pygame
import time
from game.ui.ui_panel import UIPanel

class BossRefreshPanel(UIPanel):
    """
    Boss刷新状态面板
    显示所有地图Boss的刷新状态，包括刷新倒计时
    """
    
    def __init__(self, screen, map_manager, position, size):
        super().__init__(screen, position, size)
        
        self.map_manager = map_manager
        self.title = "Boss刷新状态"
        self.background_color = (25, 25, 35, 240)  # 半透明深色背景
        self.border_color = (100, 100, 120)
        self.visible = False  # 默认不可见
        
        # 字体设置
        self.title_font = pygame.font.SysFont("SimHei", 18, bold=True)
        self.content_font = pygame.font.SysFont("SimHei", 14)
        self.small_font = pygame.font.SysFont("SimH<PERSON>", 12)
        
        # 颜色定义
        self.title_color = (255, 255, 255)
        self.available_color = (0, 255, 0)  # 绿色 - 已刷新
        self.unavailable_color = (255, 100, 100)  # 红色 - 未刷新
        self.text_color = (220, 220, 220)
        self.time_color = (255, 255, 100)  # 黄色 - 时间显示
        
        # 滚动相关
        self.scroll_y = 0
        self.max_scroll = 0
        self.scroll_speed = 20
        
        # 关闭按钮
        self.close_button_rect = pygame.Rect(
            self.rect.right - 30, self.rect.top + 5, 25, 25
        )
        
        # 获取Boss数据
        self.boss_data = self._load_boss_data()
        
    def _load_boss_data(self):
        """
        从地图配置中加载所有Boss数据
        
        Returns:
            list: Boss数据列表
        """
        boss_list = []
        
        # 从地图配置文件中读取Boss信息
        try:
            import json
            import os
            
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                "game", "data", "maps_config.json"
            )
            
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                maps = config_data.get("maps", {})
                for map_name, map_data in maps.items():
                    # 检查地图中的Boss
                    monsters = map_data.get("monsters", [])
                    for monster in monsters:
                        if monster.get("is_boss", False):
                            boss_info = {
                                "map_name": map_name,
                                "boss_name": monster["name"],
                                "respawn_time": map_data.get("respawn_time", 300),  # 默认5分钟
                                "is_world_boss": map_data.get("is_world_boss", False)
                            }
                            boss_list.append(boss_info)
                    
                    # 检查世界Boss
                    if map_data.get("is_world_boss", False):
                        boss_monster = map_data.get("boss_monster")
                        if boss_monster:
                            boss_info = {
                                "map_name": map_name,
                                "boss_name": boss_monster["name"],
                                "respawn_time": map_data.get("respawn_time", 7200),  # 默认2小时
                                "is_world_boss": True
                            }
                            boss_list.append(boss_info)
                            
        except Exception as e:
            print(f"加载Boss数据时出错: {e}")
            # 如果加载失败，提供一些默认的Boss数据
            boss_list = [
                {"map_name": "沃玛寺庙", "boss_name": "沃玛教主", "respawn_time": 1800, "is_world_boss": False},
                {"map_name": "祖玛寺庙", "boss_name": "祖玛教主", "respawn_time": 7200, "is_world_boss": True},
                {"map_name": "封魔谷", "boss_name": "虹魔教主", "respawn_time": 14400, "is_world_boss": True},
                {"map_name": "赤月峡谷", "boss_name": "赤月恶魔", "respawn_time": 10800, "is_world_boss": True},
            ]
            
        return boss_list
    
    def _format_time(self, seconds):
        """
        将秒数格式化为 HH:MM:SS 格式
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if seconds <= 0:
            return "00:00:00"
            
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def _get_boss_status(self, boss_info):
        """
        获取Boss的刷新状态
        
        Args:
            boss_info: Boss信息字典
            
        Returns:
            tuple: (是否已刷新, 剩余时间秒数)
        """
        map_name = boss_info["map_name"]
        respawn_time = boss_info["respawn_time"]
        
        # 获取Boss最后击杀时间
        current_time = time.time()
        last_kill_time = 0
        
        # 从map_manager获取Boss刷新时间
        if hasattr(self.map_manager, 'boss_respawn_times'):
            last_kill_time = self.map_manager.boss_respawn_times.get(map_name, 0)
        
        # 计算是否已刷新
        time_since_kill = current_time - last_kill_time
        is_available = time_since_kill >= respawn_time
        time_until_respawn = max(0, respawn_time - time_since_kill)
        
        return is_available, time_until_respawn
    
    def draw(self):
        """
        绘制Boss刷新状态面板
        """
        if not self.visible:
            return
            
        # 创建半透明表面
        panel_surface = pygame.Surface((self.rect.width, self.rect.height), pygame.SRCALPHA)
        panel_surface.fill(self.background_color)
        
        # 绘制边框
        pygame.draw.rect(panel_surface, self.border_color, 
                        (0, 0, self.rect.width, self.rect.height), 2)
        
        # 绘制标题
        title_surface = self.title_font.render(self.title, True, self.title_color)
        title_x = (self.rect.width - title_surface.get_width()) // 2
        panel_surface.blit(title_surface, (title_x, 10))
        
        # 绘制关闭按钮
        close_button_local = pygame.Rect(
            self.rect.width - 30, 5, 25, 25
        )
        pygame.draw.rect(panel_surface, (200, 50, 50), close_button_local)
        pygame.draw.rect(panel_surface, (255, 255, 255), close_button_local, 1)
        
        # 绘制关闭按钮的X
        close_text = self.small_font.render("×", True, (255, 255, 255))
        close_text_rect = close_text.get_rect(center=close_button_local.center)
        panel_surface.blit(close_text, close_text_rect)
        
        # 绘制Boss列表
        y_offset = 45 - self.scroll_y
        line_height = 35
        
        for i, boss_info in enumerate(self.boss_data):
            if y_offset > self.rect.height or y_offset + line_height < 0:
                y_offset += line_height
                continue
                
            # 获取Boss状态
            is_available, time_until_respawn = self._get_boss_status(boss_info)
            
            # 绘制Boss名称
            boss_name = boss_info["boss_name"]
            name_surface = self.content_font.render(boss_name, True, self.text_color)
            panel_surface.blit(name_surface, (10, y_offset))
            
            # 绘制时间和状态
            if is_available:
                time_text = "00:00:00"
                status_text = "已刷新"
                status_color = self.available_color
            else:
                time_text = self._format_time(time_until_respawn)
                status_text = "未刷新"
                status_color = self.unavailable_color
            
            # 绘制时间
            time_surface = self.content_font.render(time_text, True, self.time_color)
            panel_surface.blit(time_surface, (150, y_offset))
            
            # 绘制状态
            status_surface = self.content_font.render(status_text, True, status_color)
            panel_surface.blit(status_surface, (250, y_offset))
            
            # 绘制地图名称（小字）
            map_surface = self.small_font.render(f"({boss_info['map_name']})", True, (150, 150, 150))
            panel_surface.blit(map_surface, (10, y_offset + 18))
            
            y_offset += line_height
        
        # 计算最大滚动距离
        total_height = len(self.boss_data) * line_height
        visible_height = self.rect.height - 50
        self.max_scroll = max(0, total_height - visible_height)
        
        # 绘制滚动条（如果需要）
        if self.max_scroll > 0:
            scrollbar_height = max(20, int(visible_height * visible_height / total_height))
            scrollbar_y = int(50 + (visible_height - scrollbar_height) * self.scroll_y / self.max_scroll)
            
            pygame.draw.rect(panel_surface, (100, 100, 100), 
                           (self.rect.width - 15, 50, 10, visible_height))
            pygame.draw.rect(panel_surface, (200, 200, 200), 
                           (self.rect.width - 15, scrollbar_y, 10, scrollbar_height))
        
        # 将面板绘制到屏幕上
        self.screen.blit(panel_surface, self.rect.topleft)
    
    def handle_event(self, event):
        """
        处理事件
        
        Args:
            event: pygame事件
            
        Returns:
            bool: 是否处理了事件
        """
        if not self.visible:
            return False
            
        if event.type == pygame.MOUSEBUTTONDOWN:
            mouse_pos = pygame.mouse.get_pos()
            
            # 检查是否点击了关闭按钮
            if self.close_button_rect.collidepoint(mouse_pos):
                self.visible = False
                return True
                
            # 检查是否在面板内
            if self.rect.collidepoint(mouse_pos):
                return True  # 阻止事件传递
                
        elif event.type == pygame.MOUSEWHEEL:
            mouse_pos = pygame.mouse.get_pos()
            if self.rect.collidepoint(mouse_pos):
                # 滚动
                self.scroll_y = max(0, min(self.max_scroll, 
                                         self.scroll_y - event.y * self.scroll_speed))
                return True
                
        return False
    
    def show(self):
        """
        显示面板
        """
        self.visible = True
        self.scroll_y = 0  # 重置滚动位置
        
    def hide(self):
        """
        隐藏面板
        """
        self.visible = False
    
    def toggle(self):
        """
        切换面板显示状态
        """
        self.visible = not self.visible
        if self.visible:
            self.scroll_y = 0  # 重置滚动位置