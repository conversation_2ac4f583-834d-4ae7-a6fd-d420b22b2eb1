import json
import os
from game.core.resource_manager import get_game_data_path, get_resource_path

class DataManager:
    """
    数据管理器，负责游戏数据的读取和保存
    """
    def __init__(self):
        # 数据文件路径 - 使用资源管理器
        self.data_dir = get_resource_path("game/data")
        self.player_data_file = get_game_data_path("player.json")
        self.map_data_file = get_game_data_path("maps_config.json")
        self.rank_data_file = get_game_data_path("ranks.json")
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 游戏数据
        self.player_data = None
        self.map_data = None
        self.rank_data = None
        self.monster_skills_data = None
    
    def load_game_data(self):
        """
        加载所有游戏数据
        """
        self.load_player_data()
        self.load_map_data()
        self.load_rank_data()
        self.load_monster_skills_data()
    
    def load_player_data(self):
        """
        加载玩家数据
        """
        try:
            if os.path.exists(self.player_data_file):
                with open(self.player_data_file, 'r', encoding='utf-8') as f:
                    self.player_data = json.load(f)
            else:
                # 如果文件不存在，创建默认玩家数据
                self.player_data = self._create_default_player_data()
                self.save_player_data(self.player_data)
        except Exception as e:
            print(f"加载玩家数据失败: {e}")
            self.player_data = self._create_default_player_data()
    
    def load_map_data(self):
        """
        加载地图数据
        """
        try:
            if os.path.exists(self.map_data_file):
                with open(self.map_data_file, 'r', encoding='utf-8') as f:
                    self.map_data = json.load(f)
            else:
                # 如果文件不存在，创建默认地图数据
                self.map_data = self._create_default_map_data()
                self.save_map_data(self.map_data)
        except Exception as e:
            print(f"加载地图数据失败: {e}")
            self.map_data = self._create_default_map_data()
    
    def load_rank_data(self):
        """
        加载排行榜数据
        """
        try:
            if os.path.exists(self.rank_data_file):
                with open(self.rank_data_file, 'r', encoding='utf-8') as f:
                    self.rank_data = json.load(f)
            else:
                # 如果文件不存在，创建默认排行榜数据
                self.rank_data = self._create_default_rank_data()
                self.save_rank_data(self.rank_data)
        except Exception as e:
            print(f"加载排行榜数据失败: {e}")
            self.rank_data = self._create_default_rank_data()
    
    def save_player_data(self, player_data):
        """
        保存玩家数据
        """
        try:
            self.player_data = player_data
            with open(self.player_data_file, 'w', encoding='utf-8') as f:
                json.dump(player_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存玩家数据失败: {e}")
    
    def save_map_data(self, map_data):
        """
        保存地图数据
        """
        try:
            self.map_data = map_data
            with open(self.map_data_file, 'w', encoding='utf-8') as f:
                json.dump(map_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存地图数据失败: {e}")
    
    def save_rank_data(self, rank_data):
        """
        保存排行榜数据
        """
        try:
            self.rank_data = rank_data
            with open(self.rank_data_file, 'w', encoding='utf-8') as f:
                json.dump(rank_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存排行榜数据失败: {e}")
    
    def get_player_data(self):
        """
        获取玩家数据
        """
        return self.player_data
    
    def get_map_data(self):
        """
        获取地图数据
        """
        return self.map_data
    
    def get_rank_data(self):
        """
        获取排行榜数据
        """
        return self.rank_data
    
    def get_monster_data(self, monster_name):
        """
        获取怪物数据 - 使用统一的MonsterDataLoader
        
        参数:
            monster_name: 怪物名称
            
        返回:
            怪物数据字典或None
        """
        try:
            from game.models.monster_data_loader import MonsterDataLoader
            loader = MonsterDataLoader.get_instance()
            return loader.get_full_monster_data(monster_name)
        except Exception as e:
            print(f"获取怪物数据失败: {e}")
            return None

    # 已移除 _convert_monster_list_to_dict 方法
    # 现在统一使用 MonsterDataLoader 处理怪物数据转换

    def load_monster_skills_data(self):
        """
        加载怪物技能数据 - 使用统一的MonsterDataLoader
        """
        try:
            from game.models.monster_data_loader import MonsterDataLoader
            loader = MonsterDataLoader.get_instance()
            loader.load_data()  # 确保数据已加载
            self.monster_skills_data = loader.skills_data
        except Exception as e:
            print(f"加载怪物技能数据失败: {e}")
            self.monster_skills_data = {}

    def get_monster_skills_data(self, monster_name):
        """
        获取指定怪物的技能列表 - 使用统一的MonsterDataLoader

        参数:
            monster_name: 怪物名称

        返回:
            技能列表或None
        """
        try:
            from game.models.monster_data_loader import MonsterDataLoader
            loader = MonsterDataLoader.get_instance()
            return loader.get_monster_skills(monster_name)
        except Exception as e:
            print(f"获取怪物技能数据失败: {e}")
            return None
    
    def _create_default_player_data(self):
        """
        创建默认玩家数据
        """
        return {
            "name": "玩家1",
            "level": 1,
            "vip_level": 0,
            "vip_exp": 0,
            "hp": 100,
            "max_hp": 100,
            "mp": 50,
            "max_mp": 50,
            "exp": 0,
            "max_exp": 100,
            "currencies": {
                "gold": 0,
                "coin": 0,
                "yuanbao": 777689
            },
            "equipment": {},
            "inventory": [],
            "skills": [],
            "stats": {
                "strength": 10,
                "agility": 10,
                "intelligence": 10,
                "vitality": 10,
                "luck": 10
            },
            "battle_power": 100
        }
    
    def _create_default_map_data(self):
        """
        创建默认地图数据
        """
        return {
            "available_maps": [
                {
                    "id": 1,
                    "name": "新手村",
                    "unlocked": True,
                    "thumbnail": "newbie_village.png",
                    "enemies": [
                        {"id": 1, "name": "小史莱姆", "level": 1, "hp": 20, "exp": 5, "gold": 1},
                        {"id": 2, "name": "小蝙蝠", "level": 2, "hp": 30, "exp": 8, "gold": 2}
                    ],
                    "boss": {"id": 101, "name": "史莱姆王", "level": 5, "hp": 100, "exp": 50, "gold": 20}
                },
                {
                    "id": 2,
                    "name": "银杏村",
                    "unlocked": False,
                    "thumbnail": "ginkgo_village.png",
                    "unlock_requirement": {"player_level": 5},
                    "enemies": [
                        {"id": 3, "name": "森林狼", "level": 6, "hp": 60, "exp": 15, "gold": 5},
                        {"id": 4, "name": "树妖", "level": 7, "hp": 70, "exp": 18, "gold": 6}
                    ],
                    "boss": {"id": 102, "name": "森林守护者", "level": 10, "hp": 200, "exp": 100, "gold": 40}
                },
                {
                    "id": 3,
                    "name": "比奇大陆",
                    "unlocked": False,
                    "thumbnail": "bichi_continent.png",
                    "unlock_requirement": {"player_level": 10},
                    "enemies": [
                        {"id": 5, "name": "沙漠蝎子", "level": 11, "hp": 110, "exp": 25, "gold": 10},
                        {"id": 6, "name": "沙漠强盗", "level": 12, "hp": 120, "exp": 28, "gold": 12}
                    ],
                    "boss": {"id": 103, "name": "沙漠暴君", "level": 15, "hp": 300, "exp": 150, "gold": 60}
                }
            ],
            "boss_challenge": {
                "max_daily": 15,
                "current": 15
            }
        }
    
    def _create_default_rank_data(self):
        """
        创建默认排行榜数据
        """
        return {
            "battle_power": [
                {"rank": 1, "name": "剑风破浪", "battle_power": 15341, "level": 30},
                {"rank": 2, "name": "天涯明月", "battle_power": 14765, "level": 28},
                {"rank": 3, "name": "刀光剑影", "battle_power": 13998, "level": 27},
                {"rank": 4, "name": "风云再起", "battle_power": 12876, "level": 25},
                {"rank": 5, "name": "十方俱灭", "battle_power": 11543, "level": 23},
                {"rank": 6, "name": "夜雨幽魂", "battle_power": 10982, "level": 22},
                {"rank": 7, "name": "Cww", "battle_power": 9875, "level": 20},
                {"rank": 8, "name": "青衫落拓", "battle_power": 8754, "level": 18},
                {"rank": 9, "name": "三生石", "battle_power": 7632, "level": 16},
                {"rank": 10, "name": "小楼独饮", "battle_power": 6541, "level": 14}
            ],
            "level": [
                {"rank": 1, "name": "剑风破浪", "level": 30, "battle_power": 15341},
                {"rank": 2, "name": "天涯明月", "level": 28, "battle_power": 14765},
                {"rank": 3, "name": "刀光剑影", "level": 27, "battle_power": 13998},
                {"rank": 4, "name": "风云再起", "level": 25, "battle_power": 12876},
                {"rank": 5, "name": "十方俱灭", "level": 23, "battle_power": 11543},
                {"rank": 6, "name": "夜雨幽魂", "level": 22, "battle_power": 10982},
                {"rank": 7, "name": "Cww", "level": 20, "battle_power": 9875},
                {"rank": 8, "name": "青衫落拓", "level": 18, "battle_power": 8754},
                {"rank": 9, "name": "三生石", "level": 16, "battle_power": 7632},
                {"rank": 10, "name": "小楼独饮", "level": 14, "battle_power": 6541}
            ]
        }