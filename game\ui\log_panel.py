import pygame
import pyperclip  # 用于剪贴板操作
from game.ui.ui_panel import UIPanel

class LogPanel(UIPanel):
    """
    日志面板，显示游戏日志和公告
    """
    def __init__(self, screen, battle_manager, position, size):
        super().__init__(screen, position, size)
        
        # 战斗管理器引用
        self.battle_manager = battle_manager
        
        # 设置面板标题
        self.title = "公告区"
        
        # 设置面板背景色
        self.background_color = (25, 25, 25)
        
        # 日志内容
        self.logs = []
        
        # 系统消息
        self.system_messages = [
            "欢迎回家, 服务器已运行 198 天",
            "主线任务: 右键锁定装备一次: 奖励->BOSS卷: 1"
        ]
        
        # 日志显示区域
        self.log_rect = pygame.Rect(
            self.rect.left + 10,
            self.rect.top + 30,
            self.size[0] - 20,
            self.size[1] - 40
        )
        
        # 每行日志高度
        self.log_line_height = 18
        
        # 可显示的日志行数
        self.visible_logs = self.log_rect.height // self.log_line_height
        
        # 日志滚动位置
        self.log_scroll_position = 0
        
        # 文本选择相关属性
        self.text_selection = {
            'active': False,  # 是否正在选择文本
            'start_pos': None,  # 选择开始位置 (log_index, char_index)
            'end_pos': None,   # 选择结束位置 (log_index, char_index)
            'start_mouse_pos': None,  # 鼠标开始位置
            'dragging': False  # 是否正在拖拽
        }
        
        # 初始化日志
        self.initialize_logs()
    
    def initialize_logs(self):
        """
        初始化日志内容
        """
        # 添加系统消息到日志
        for message in self.system_messages:
            self.add_log(message)
    
    def add_log(self, message, color=(200, 200, 200)):
        """
        添加日志
        """
        self.logs.append({
            "message": message,
            "color": color,
            "time": pygame.time.get_ticks()  # 记录日志时间
        })
        
        # 如果日志超过100条，移除最早的日志
        if len(self.logs) > 100:
            self.logs.pop(0)
        
        # 自动滚动到最新日志
        self.log_scroll_position = max(0, len(self.logs) - self.visible_logs)
    
    def render(self):
        """
        渲染日志面板
        """
        super().render()
        
        # 渲染日志背景
        pygame.draw.rect(self.screen, (15, 15, 15), self.log_rect)
        
        # 计算可见日志的起始和结束索引
        start_index = self.log_scroll_position
        end_index = min(start_index + self.visible_logs, len(self.logs))
        
        # 渲染可见日志
        for i in range(start_index, end_index):
            log = self.logs[i]
            log_y = self.log_rect.top + (i - start_index) * self.log_line_height
            
            # 确保消息是字符串类型
            message = str(log["message"]) if log["message"] is not None else ""
            
            # 检查是否需要渲染选择高亮
            if self.text_selection['active'] and self.text_selection['start_pos'] and self.text_selection['end_pos']:
                self._render_text_with_selection(message, log["color"], i, log_y)
            else:
                log_surface = self.small_font.render(message, True, log["color"])
                self.screen.blit(log_surface, (self.log_rect.left + 5, log_y))
    
    def update(self):
        """
        更新日志面板
        """
        # 获取战斗日志
        battle_logs = self.battle_manager.get_battle_log()
        
        # 如果有新的战斗日志，添加到日志面板
        if battle_logs and (not self.logs or self.logs[-1]["message"] != battle_logs[-1]):
            for log in battle_logs:
                if not any(l["message"] == log for l in self.logs[-len(battle_logs):]):
                    self.add_log(log, (255, 200, 200))
    
    def handle_event(self, event):
        """
        处理事件
        """
        if not self.visible:
            return False
        
        # 检查是否点击了按钮
        if super().handle_event(event):
            return True
        
        # 处理文本选择相关事件
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self.log_rect.collidepoint(event.pos):
                if event.button == 1:  # 左键点击
                    # 开始文本选择
                    text_pos = self.get_text_position_from_mouse(event.pos)
                    if text_pos:
                        self.text_selection['active'] = True
                        self.text_selection['start_pos'] = text_pos
                        self.text_selection['end_pos'] = text_pos
                        self.text_selection['start_mouse_pos'] = event.pos
                        self.text_selection['dragging'] = True
                    return True
                elif event.button == 4:  # 滚轮向上
                    self.scroll_up()
                    return True
                elif event.button == 5:  # 滚轮向下
                    self.scroll_down()
                    return True
            else:
                # 点击日志区域外，清除选择
                self.clear_selection()
        
        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1 and self.text_selection['dragging']:  # 左键释放
                self.text_selection['dragging'] = False
                return True
        
        elif event.type == pygame.MOUSEMOTION:
            if self.text_selection['dragging'] and self.log_rect.collidepoint(event.pos):
                # 更新选择结束位置
                text_pos = self.get_text_position_from_mouse(event.pos)
                if text_pos:
                    self.text_selection['end_pos'] = text_pos
                return True
        
        elif event.type == pygame.KEYDOWN:
            # 处理键盘事件
            if event.key == pygame.K_c and (event.mod & pygame.KMOD_CTRL):  # Ctrl+C
                if self.text_selection['active']:
                    self.copy_selected_text()
                    return True
            elif event.key == pygame.K_a and (event.mod & pygame.KMOD_CTRL):  # Ctrl+A
                # 全选所有文本
                if self.logs:
                    self.text_selection['active'] = True
                    self.text_selection['start_pos'] = (0, 0)
                    last_log_index = len(self.logs) - 1
                    last_message = str(self.logs[last_log_index]["message"]) if self.logs[last_log_index]["message"] is not None else ""
                    self.text_selection['end_pos'] = (last_log_index, len(last_message))
                    return True
            elif event.key == pygame.K_ESCAPE:  # ESC键清除选择
                self.clear_selection()
                return True
        
        return False
    
    def scroll_up(self):
        """
        向上滚动日志
        """
        self.log_scroll_position = max(0, self.log_scroll_position - 1)
    
    def scroll_down(self):
        """
        向下滚动日志
        """
        max_scroll = max(0, len(self.logs) - self.visible_logs)
        self.log_scroll_position = min(max_scroll, self.log_scroll_position + 1)
    
    def get_text_position_from_mouse(self, mouse_pos):
        """
        根据鼠标位置获取文本位置
        返回 (log_index, char_index) 或 None
        """
        if not self.log_rect.collidepoint(mouse_pos):
            return None
        
        # 计算相对于日志区域的位置
        relative_y = mouse_pos[1] - self.log_rect.top
        relative_x = mouse_pos[0] - self.log_rect.left - 5  # 减去左边距
        
        # 计算日志行索引
        line_index = relative_y // self.log_line_height
        log_index = self.log_scroll_position + line_index
        
        # 检查是否在有效范围内
        if log_index < 0 or log_index >= len(self.logs):
            return None
        
        # 获取该行的文本
        message = str(self.logs[log_index]["message"]) if self.logs[log_index]["message"] is not None else ""
        
        # 计算字符索引（简化版本，基于字符宽度估算）
        char_width = 8  # 假设每个字符宽度为8像素
        char_index = max(0, min(len(message), relative_x // char_width))
        
        return (log_index, char_index)
    
    def get_selected_text(self):
        """
        获取选中的文本
        """
        if not self.text_selection['active'] or not self.text_selection['start_pos'] or not self.text_selection['end_pos']:
            return ""
        
        start_log, start_char = self.text_selection['start_pos']
        end_log, end_char = self.text_selection['end_pos']
        
        # 确保开始位置在结束位置之前
        if start_log > end_log or (start_log == end_log and start_char > end_char):
            start_log, start_char, end_log, end_char = end_log, end_char, start_log, start_char
        
        selected_text = []
        
        for log_index in range(start_log, end_log + 1):
            if log_index >= len(self.logs):
                break
            
            message = str(self.logs[log_index]["message"]) if self.logs[log_index]["message"] is not None else ""
            
            if log_index == start_log and log_index == end_log:
                # 同一行选择
                selected_text.append(message[start_char:end_char])
            elif log_index == start_log:
                # 开始行
                selected_text.append(message[start_char:])
            elif log_index == end_log:
                # 结束行
                selected_text.append(message[:end_char])
            else:
                # 中间行
                selected_text.append(message)
        
        return "\n".join(selected_text)
    
    def copy_selected_text(self):
        """
        复制选中的文本到剪贴板
        """
        selected_text = self.get_selected_text()
        if selected_text:
            try:
                pyperclip.copy(selected_text)
                print(f"已复制文本: {selected_text[:50]}...")  # 显示前50个字符
            except Exception as e:
                print(f"复制文本失败: {e}")
    
    def clear_selection(self):
        """
        清除文本选择
        """
        self.text_selection['active'] = False
        self.text_selection['start_pos'] = None
        self.text_selection['end_pos'] = None
        self.text_selection['dragging'] = False
    
    def _render_text_with_selection(self, message, text_color, log_index, log_y):
        """
        渲染带有选择高亮的文本
        """
        start_log, start_char = self.text_selection['start_pos']
        end_log, end_char = self.text_selection['end_pos']
        
        # 确保开始位置在结束位置之前
        if start_log > end_log or (start_log == end_log and start_char > end_char):
            start_log, start_char, end_log, end_char = end_log, end_char, start_log, start_char
        
        # 检查当前行是否在选择范围内
        if log_index < start_log or log_index > end_log:
            # 不在选择范围内，正常渲染
            log_surface = self.small_font.render(message, True, text_color)
            self.screen.blit(log_surface, (self.log_rect.left + 5, log_y))
            return
        
        # 计算选择范围
        if log_index == start_log and log_index == end_log:
            # 同一行选择
            sel_start = start_char
            sel_end = end_char
        elif log_index == start_log:
            # 开始行
            sel_start = start_char
            sel_end = len(message)
        elif log_index == end_log:
            # 结束行
            sel_start = 0
            sel_end = end_char
        else:
            # 中间行，全选
            sel_start = 0
            sel_end = len(message)
        
        # 分段渲染文本
        x_offset = self.log_rect.left + 5
        char_width = 8  # 假设每个字符宽度为8像素
        
        # 渲染选择前的文本
        if sel_start > 0:
            before_text = message[:sel_start]
            before_surface = self.small_font.render(before_text, True, text_color)
            self.screen.blit(before_surface, (x_offset, log_y))
            x_offset += len(before_text) * char_width
        
        # 渲染选择的文本（带高亮背景）
        if sel_end > sel_start:
            selected_text = message[sel_start:sel_end]
            if selected_text:
                # 绘制选择背景
                selection_width = len(selected_text) * char_width
                selection_rect = pygame.Rect(x_offset, log_y, selection_width, self.log_line_height)
                pygame.draw.rect(self.screen, (100, 150, 255), selection_rect)  # 蓝色高亮背景
                
                # 渲染选择的文本
                selected_surface = self.small_font.render(selected_text, True, (255, 255, 255))  # 白色文字
                self.screen.blit(selected_surface, (x_offset, log_y))
                x_offset += selection_width
        
        # 渲染选择后的文本
        if sel_end < len(message):
            after_text = message[sel_end:]
            after_surface = self.small_font.render(after_text, True, text_color)
            self.screen.blit(after_surface, (x_offset, log_y))