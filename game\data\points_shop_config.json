{"categories": {"道具": {"name": "道具", "description": "各种消耗品和增强道具", "icon": "potion", "items": [{"item_id": "祝福油", "name": "祝福油", "description": "增加装备幸运值的珍贵道具", "points_cost": 100, "stock": -1, "daily_limit": 3, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "太阳水", "name": "太阳水", "description": "立刻恢复少量生命值和魔法值", "points_cost": 50, "stock": -1, "daily_limit": 10, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "强效太阳水", "name": "强效太阳水", "description": "立刻恢复中量生命值和魔法值", "points_cost": 80, "stock": -1, "daily_limit": 5, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "万年雪霜", "name": "万年雪霜", "description": "立刻恢复大量生命值和魔法值", "points_cost": 120, "stock": -1, "daily_limit": 3, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "随机传送卷", "name": "随机传送卷", "description": "随机传送到当前地图的安全位置", "points_cost": 30, "stock": -1, "daily_limit": 20, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "地牢逃脱卷", "name": "地牢逃脱卷", "description": "立即传送到比奇省安全区", "points_cost": 40, "stock": -1, "daily_limit": 15, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}]}, "材料": {"name": "材料", "description": "制作和强化所需的各种材料", "icon": "material", "items": [{"item_id": "黑铁矿石", "name": "黑铁矿石", "description": "提升武器持久度的基础材料", "points_cost": 20, "stock": -1, "daily_limit": 30, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "金矿", "name": "金矿", "description": "珍贵的金属矿石，用于高级装备制作", "points_cost": 35, "stock": -1, "daily_limit": 20, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "铁矿", "name": "铁矿", "description": "常见的金属矿石，用于装备制作", "points_cost": 15, "stock": -1, "daily_limit": 50, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "铜矿", "name": "铜矿", "description": "基础金属矿石，用于初级装备制作", "points_cost": 10, "stock": -1, "daily_limit": 100, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}]}, "特殊": {"name": "特殊", "description": "稀有和特殊功能物品", "icon": "special", "items": [{"item_id": "修复油", "name": "修复油", "description": "修复装备耐久度的特殊道具", "points_cost": 60, "stock": -1, "daily_limit": 5, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "新手勋章", "name": "新手勋章", "description": "为新手玩家提供额外属性加成的特殊勋章", "points_cost": 200, "stock": 10, "daily_limit": 1, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "彩票", "name": "幸运彩票", "description": "可能获得珍贵奖励的神秘彩票", "points_cost": 150, "stock": -1, "daily_limit": 3, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}]}, "神水": {"name": "神水", "description": "提升角色属性的特殊药水", "icon": "enhance", "items": [{"item_id": "攻击神水", "name": "攻击神水", "description": "临时提升攻击力的神奇药水", "points_cost": 80, "stock": -1, "daily_limit": 5, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "魔力神水", "name": "魔力神水", "description": "临时提升魔法攻击力的神奇药水", "points_cost": 80, "stock": -1, "daily_limit": 5, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "疾风神水", "name": "疾风神水", "description": "临时提升移动速度的神奇药水", "points_cost": 70, "stock": -1, "daily_limit": 5, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "体力强效神水", "name": "体力强效神水", "description": "大幅提升生命值上限的强力药水", "points_cost": 120, "stock": -1, "daily_limit": 3, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}, {"item_id": "魔力强效神水", "name": "魔力强效神水", "description": "大幅提升魔法值上限的强力药水", "points_cost": 120, "stock": -1, "daily_limit": 3, "vip_discount": {"vip_1": 0.95, "vip_2": 0.9, "vip_3": 0.85}}]}}, "shop_settings": {"refresh_time": "00:00", "currency_type": "points", "max_daily_purchases": 30, "vip_bonus_enabled": true}}