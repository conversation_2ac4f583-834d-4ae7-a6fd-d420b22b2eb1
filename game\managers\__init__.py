# -*- coding: utf-8 -*-
"""
管理器模块
包含各种专门的管理器类，用于分离Player类的职责
"""

from .inventory_manager import InventoryManager
from .equipment_manager import EquipmentManager
from .skill_manager import SkillManager
from .quest_manager import QuestManager, QuestStatus, QuestType
from .save_load_manager import SaveLoadManager

# 新移动的管理器
from .data_manager import DataManager
from .rank_manager import RankManager
from .enemy_image_manager import enemy_image_manager
from .user_message_manager import UserMessageManager, UserMessageType, UserMessagePriority
from .monster_distribution_manager import MonsterDistributionManager
from .potion_effects_manager import PotionEffectsManager

__all__ = [
    'InventoryManager',
    'EquipmentManager', 
    'SkillManager',
    'QuestManager',
    'QuestStatus',
    'QuestType',
    'SaveLoadManager',
    # 新移动的管理器
    'DataManager',
    'RankManager', 
    'enemy_image_manager',
    'UserMessageManager',
    'UserMessageType',
    'UserMessagePriority',
    'MonsterDistributionManager',
    'PotionEffectsManager'
]