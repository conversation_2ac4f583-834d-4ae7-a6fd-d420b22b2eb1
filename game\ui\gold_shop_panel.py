#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pygame
import sys
import os
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from game.managers.gold_shop_manager import GoldShopManager
except ImportError:
    # 如果无法导入，创建一个简单的占位符
    class GoldShopManager:
        def __init__(self):
            pass
        def get_categories(self):
            return {}
        def get_category_items(self, category):
            return []
        def can_purchase_item(self, player, item_id, category):
            return {'can_purchase': False, 'reason': '系统错误'}
        def purchase_item(self, player, item_id, category, quantity=1):
            return {'success': False, 'message': '系统错误'}

class GoldShopPanel:
    """
    金币商城面板UI组件
    显示商城分类、商品列表和购买功能
    """
    
    def __init__(self, screen, font_manager, ui_manager, player=None):
        """
        初始化金币商城面板
        
        Args:
            screen: pygame屏幕对象
            font_manager: 字体管理器
            ui_manager: UI管理器
            player: 玩家对象
        """
        self.screen = screen
        self.font_manager = font_manager
        self.ui_manager = ui_manager
        self.player = player
        self.shop_manager = GoldShopManager()
        
        # 面板尺寸和位置
        self.width = 800
        self.height = 600
        self.x = (screen.get_width() - self.width) // 2
        self.y = (screen.get_height() - self.height) // 2
        
        # 深色主题配色（与游戏风格一致）
        self.colors = {
            'background': (15, 15, 15),           # 深黑背景
            'panel_bg': (25, 25, 25),             # 面板背景
            'border': (60, 60, 60),               # 边框颜色
            'category_bg': (35, 35, 35),          # 分类背景
            'category_selected': (80, 50, 20),    # 选中分类（金色主题）
            'category_hover': (60, 40, 20),       # 分类悬停
            'item_bg': (30, 30, 30),              # 商品背景
            'item_hover': (45, 45, 45),           # 商品悬停
            'button_normal': (70, 70, 70),        # 按钮正常
            'button_hover': (90, 90, 90),         # 按钮悬停
            'button_disabled': (40, 40, 40),      # 按钮禁用
            'text_primary': (220, 220, 220),      # 主要文字
            'text_secondary': (180, 180, 180),    # 次要文字
            'text_disabled': (120, 120, 120),     # 禁用文字
            'gold_color': (255, 215, 0),          # 金币颜色（金色）
            'success': (100, 200, 100),           # 成功颜色
            'error': (200, 100, 100),             # 错误颜色
            'warning': (200, 200, 100)            # 警告颜色
        }
        
        # 状态变量
        self.visible = False
        self.current_category = '武器'  # 默认选中武器分类
        self.selected_item = None
        self.message = ''
        self.message_color = self.colors['text_primary']
        self.message_timer = 0
        
        # 按钮状态
        self.button_states = {
            'close_button_rect': None,
            'close_hover': False,
            'category_buttons': {},
            'item_buttons': {},
            'purchase_button_rect': None,
            'purchase_hover': False
        }
        
        # 滚动相关
        self.scroll_offset = 0
        self.max_scroll = 0
        
        # 面板区域矩形（用于外部点击检测）
        self.rect = pygame.Rect(self.x, self.y, self.width, self.height)
        
    def show(self):
        """
        显示金币商城面板
        """
        self.visible = True
        self.current_category = '武器'  # 重置到默认分类
        self.selected_item = None
        self.scroll_offset = 0
        self._update_scroll_limits()
        
    def hide(self):
        """
        隐藏金币商城面板
        """
        self.visible = False
        
    def handle_event(self, event):
        """
        处理事件
        
        Args:
            event: pygame事件对象
        """
        if not self.visible:
            return
            
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # 左键点击
                mouse_pos = pygame.mouse.get_pos()
                
                # 检查关闭按钮
                if (self.button_states['close_button_rect'] and 
                    self.button_states['close_button_rect'].collidepoint(mouse_pos)):
                    self.hide()
                    return
                
                # 检查分类按钮
                for category, rect in self.button_states['category_buttons'].items():
                    if rect.collidepoint(mouse_pos):
                        self.current_category = category
                        self.selected_item = None
                        self.scroll_offset = 0
                        self._update_scroll_limits()
                        return
                
                # 检查商品按钮
                for item_id, rect in self.button_states['item_buttons'].items():
                    if rect.collidepoint(mouse_pos):
                        self.selected_item = item_id
                        return
                
                # 检查购买按钮
                if (self.button_states['purchase_button_rect'] and 
                    self.button_states['purchase_button_rect'].collidepoint(mouse_pos) and
                    self.selected_item):
                    self._purchase_selected_item()
                    return
            
            elif event.button == 4:  # 鼠标滚轮向上
                self.scroll_offset = max(0, self.scroll_offset - 30)
            elif event.button == 5:  # 鼠标滚轮向下
                self.scroll_offset = min(self.max_scroll, self.scroll_offset + 30)
        
        elif event.type == pygame.MOUSEMOTION:
            mouse_pos = pygame.mouse.get_pos()
            
            # 更新悬停状态
            self.button_states['close_hover'] = (
                self.button_states['close_button_rect'] and 
                self.button_states['close_button_rect'].collidepoint(mouse_pos)
            )
            
            self.button_states['purchase_hover'] = (
                self.button_states['purchase_button_rect'] and 
                self.button_states['purchase_button_rect'].collidepoint(mouse_pos)
            )
    
    def update(self, dt):
        """
        更新面板状态
        
        Args:
            dt: 时间增量
        """
        if not self.visible:
            return
            
        # 更新消息显示计时器
        if self.message_timer > 0:
            self.message_timer -= dt
            if self.message_timer <= 0:
                self.message = ''
    
    def render(self):
        """
        渲染金币商城面板
        """
        if not self.visible:
            return
            
        # 绘制半透明背景
        overlay = pygame.Surface((self.screen.get_width(), self.screen.get_height()))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))
        
        # 绘制主面板
        pygame.draw.rect(self.screen, self.colors['panel_bg'], 
                        (self.x, self.y, self.width, self.height))
        pygame.draw.rect(self.screen, self.colors['border'], 
                        (self.x, self.y, self.width, self.height), 2)
        
        # 渲染各个组件
        self._render_title()
        self._render_player_gold()
        self._render_categories()
        self._render_items()
        self._render_item_details()
        self._render_purchase_button()
        self._render_close_button()
        self._render_message()
    
    def _render_title(self):
        """
        渲染标题
        """
        title_font = self.font_manager.get_font('large')
        title_text = title_font.render('金币商城', True, self.colors['gold_color'])
        title_rect = title_text.get_rect(centerx=self.x + self.width // 2, y=self.y + 15)
        self.screen.blit(title_text, title_rect)
    
    def _render_player_gold(self):
        """
        渲染玩家金币信息
        """
        font = self.font_manager.get_font('medium')
        gold_text = f'金币: {self.get_player_gold()}'
        gold_surface = font.render(gold_text, True, self.colors['gold_color'])
        gold_rect = gold_surface.get_rect(right=self.x + self.width - 20, y=self.y + 15)
        self.screen.blit(gold_surface, gold_rect)
    
    def _render_categories(self):
        """
        渲染分类列表
        """
        categories = self.shop_manager.get_categories()
        font = self.font_manager.get_font('medium')
        
        start_y = self.y + 60
        button_height = 35
        button_width = 120
        
        self.button_states['category_buttons'] = {}
        
        for i, (category_name, category_info) in enumerate(categories.items()):
            button_y = start_y + i * (button_height + 5)
            button_rect = pygame.Rect(self.x + 10, button_y, button_width, button_height)
            
            # 选择按钮颜色
            if category_name == self.current_category:
                button_color = self.colors['category_selected']
            else:
                button_color = self.colors['category_bg']
            
            # 绘制按钮
            pygame.draw.rect(self.screen, button_color, button_rect)
            pygame.draw.rect(self.screen, self.colors['border'], button_rect, 1)
            
            # 绘制分类名称
            text_surface = font.render(category_name, True, self.colors['text_primary'])
            text_rect = text_surface.get_rect(center=button_rect.center)
            self.screen.blit(text_surface, text_rect)
            
            # 存储按钮区域用于点击检测
            self.button_states['category_buttons'][category_name] = button_rect
    
    def _render_items(self):
        """
        渲染商品列表
        """
        items = self.shop_manager.get_category_items(self.current_category)
        if not items:
            return
            
        font = self.font_manager.get_font('small')
        start_x = self.x + 140
        start_y = self.y + 60
        item_width = 280
        item_height = 60
        
        self.button_states['item_buttons'] = {}
        
        # 创建裁剪区域
        clip_rect = pygame.Rect(start_x, start_y, item_width, self.height - 120)
        original_clip = self.screen.get_clip()
        self.screen.set_clip(clip_rect)
        
        for i, item in enumerate(items):
            item_y = start_y + i * (item_height + 5) - self.scroll_offset
            
            # 只渲染可见的商品
            if item_y < start_y - item_height or item_y > start_y + self.height - 120:
                continue
                
            item_rect = pygame.Rect(start_x, item_y, item_width, item_height)
            
            # 选择商品颜色
            if self.selected_item == item['item_id']:
                item_color = self.colors['category_selected']
            else:
                item_color = self.colors['item_bg']
            
            # 绘制商品背景
            pygame.draw.rect(self.screen, item_color, item_rect)
            pygame.draw.rect(self.screen, self.colors['border'], item_rect, 1)
            
            # 绘制商品名称
            name_surface = font.render(item['name'], True, self.colors['text_primary'])
            name_rect = name_surface.get_rect(left=item_rect.left + 10, top=item_rect.top + 5)
            self.screen.blit(name_surface, name_rect)
            
            # 绘制价格
            cost = self._calculate_display_cost(item)
            price_text = f'{cost} 金币'
            price_surface = font.render(price_text, True, self.colors['gold_color'])
            price_rect = price_surface.get_rect(left=item_rect.left + 10, top=item_rect.top + 25)
            self.screen.blit(price_surface, price_rect)
            
            # 绘制等级要求
            if 'level_requirement' in item:
                level_text = f'等级: {item["level_requirement"]}'
                level_surface = font.render(level_text, True, self.colors['text_secondary'])
                level_rect = level_surface.get_rect(right=item_rect.right - 10, top=item_rect.top + 5)
                self.screen.blit(level_surface, level_rect)
            
            # 检查购买条件
            can_purchase = self.shop_manager.can_purchase_item(self.player, item['item_id'], self.current_category)
            if not can_purchase['can_purchase']:
                # 绘制不可购买的覆盖层
                overlay = pygame.Surface((item_width, item_height))
                overlay.set_alpha(128)
                overlay.fill((50, 50, 50))
                self.screen.blit(overlay, item_rect)
            
            # 存储按钮区域用于点击检测
            self.button_states['item_buttons'][item['item_id']] = item_rect
        
        # 恢复原始裁剪区域
        self.screen.set_clip(original_clip)
        
        # 更新滚动限制
        self._update_scroll_limits()
    
    def _render_item_details(self):
        """
        渲染选中商品的详情
        """
        if not self.selected_item:
            return
            
        # 获取选中商品信息
        items = self.shop_manager.get_category_items(self.current_category)
        selected_item_info = None
        for item in items:
            if item['item_id'] == self.selected_item:
                selected_item_info = item
                break
        
        if not selected_item_info:
            return
            
        font = self.font_manager.get_font('small')
        detail_x = self.x + 430
        detail_y = self.y + 60
        detail_width = 350
        
        # 绘制详情背景
        detail_rect = pygame.Rect(detail_x, detail_y, detail_width, 300)
        pygame.draw.rect(self.screen, self.colors['item_bg'], detail_rect)
        pygame.draw.rect(self.screen, self.colors['border'], detail_rect, 1)
        
        current_y = detail_y + 10
        
        # 商品名称
        name_font = self.font_manager.get_font('medium')
        name_surface = name_font.render(selected_item_info['name'], True, self.colors['text_primary'])
        self.screen.blit(name_surface, (detail_x + 10, current_y))
        current_y += 30
        
        # 商品描述
        description = selected_item_info.get('description', '暂无描述')
        desc_surface = font.render(description, True, self.colors['text_secondary'])
        self.screen.blit(desc_surface, (detail_x + 10, current_y))
        current_y += 25
        
        # 价格
        cost = self._calculate_display_cost(selected_item_info)
        price_text = f'价格: {cost} 金币'
        price_surface = font.render(price_text, True, self.colors['gold_color'])
        self.screen.blit(price_surface, (detail_x + 10, current_y))
        current_y += 25
        
        # 等级要求
        if 'level_requirement' in selected_item_info:
            level_text = f'等级要求: {selected_item_info["level_requirement"]}'
            level_surface = font.render(level_text, True, self.colors['text_secondary'])
            self.screen.blit(level_surface, (detail_x + 10, current_y))
            current_y += 25
        
        # 职业要求
        if 'class_requirement' in selected_item_info and selected_item_info['class_requirement']:
            class_text = f'职业要求: {"/".join(selected_item_info["class_requirement"])}'
            class_surface = font.render(class_text, True, self.colors['text_secondary'])
            self.screen.blit(class_surface, (detail_x + 10, current_y))
            current_y += 25
        
        # 检查购买条件并显示状态
        can_purchase = self.shop_manager.can_purchase_item(self.player, self.selected_item, self.current_category)
        if not can_purchase['can_purchase']:
            reason_surface = font.render(can_purchase['reason'], True, self.colors['error'])
            self.screen.blit(reason_surface, (detail_x + 10, current_y))
    
    def _render_purchase_button(self):
        """
        渲染购买按钮
        """
        if not self.selected_item:
            return
            
        button_width = 100
        button_height = 35
        button_x = self.x + self.width - button_width - 20
        button_y = self.y + self.height - button_height - 20
        
        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        self.button_states['purchase_button_rect'] = button_rect
        
        # 检查是否可以购买
        can_purchase = self.shop_manager.can_purchase_item(self.player, self.selected_item, self.current_category)
        
        if can_purchase['can_purchase']:
            if self.button_states['purchase_hover']:
                button_color = self.colors['button_hover']
            else:
                button_color = self.colors['button_normal']
            text_color = self.colors['text_primary']
        else:
            button_color = self.colors['button_disabled']
            text_color = self.colors['text_disabled']
        
        # 绘制按钮
        pygame.draw.rect(self.screen, button_color, button_rect)
        pygame.draw.rect(self.screen, self.colors['border'], button_rect, 1)
        
        # 绘制按钮文字
        font = self.font_manager.get_font('medium')
        text_surface = font.render('购买', True, text_color)
        text_rect = text_surface.get_rect(center=button_rect.center)
        self.screen.blit(text_surface, text_rect)
    
    def _render_close_button(self):
        """
        渲染关闭按钮
        """
        button_size = 30
        button_x = self.x + self.width - button_size - 10
        button_y = self.y + 10
        
        button_rect = pygame.Rect(button_x, button_y, button_size, button_size)
        self.button_states['close_button_rect'] = button_rect
        
        # 选择按钮颜色
        if self.button_states['close_hover']:
            button_color = self.colors['error']
        else:
            button_color = self.colors['button_normal']
        
        # 绘制按钮
        pygame.draw.rect(self.screen, button_color, button_rect)
        pygame.draw.rect(self.screen, self.colors['border'], button_rect, 1)
        
        # 绘制X符号
        font = self.font_manager.get_font('medium')
        close_text = font.render('×', True, self.colors['text_primary'])
        close_rect = close_text.get_rect(center=button_rect.center)
        self.screen.blit(close_text, close_rect)
    
    def _render_message(self):
        """
        渲染消息
        """
        if not self.message:
            return
            
        font = self.font_manager.get_font('medium')
        message_surface = font.render(self.message, True, self.message_color)
        message_rect = message_surface.get_rect(centerx=self.x + self.width // 2, y=self.y + self.height - 50)
        
        # 绘制消息背景
        bg_rect = message_rect.inflate(20, 10)
        pygame.draw.rect(self.screen, self.colors['panel_bg'], bg_rect)
        pygame.draw.rect(self.screen, self.colors['border'], bg_rect, 1)
        
        self.screen.blit(message_surface, message_rect)
    
    def _update_scroll_limits(self):
        """
        更新滚动限制
        """
        items = self.shop_manager.get_category_items(self.current_category)
        item_height = 65  # 60 + 5 spacing
        total_height = len(items) * item_height
        visible_height = self.height - 120
        
        self.max_scroll = max(0, total_height - visible_height)
    
    def _purchase_selected_item(self):
        """
        购买选中的商品
        """
        if not self.selected_item or not self.player:
            return
            
        result = self.shop_manager.purchase_item(self.player, self.selected_item, self.current_category)
        
        if result['success']:
            self.message = result['message']
            self.message_color = self.colors['success']
        else:
            self.message = result['message']
            self.message_color = self.colors['error']
        
        self.message_timer = 3.0  # 显示3秒
    
    def _calculate_display_cost(self, item_info: Dict[str, Any]) -> int:
        """
        计算显示的商品价格（考虑VIP折扣）
        
        Args:
            item_info: 商品信息
            
        Returns:
            int: 显示价格
        """
        if self.player:
            return self.shop_manager._calculate_item_cost(self.player, item_info)
        return item_info.get('gold_cost', 0)
    
    def get_player_gold(self) -> int:
        """
        获取玩家金币数量
        
        Returns:
            int: 玩家金币数量
        """
        if self.player and hasattr(self.player, 'gold'):
            return self.player.gold
        return 0 