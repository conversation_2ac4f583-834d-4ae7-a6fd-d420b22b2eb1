"""
幸运值系统 - 重新设计版
只影响伤害的上下限波动，使幸运值的作用更加直观和专注
"""
import random

class LuckSystem:
    """
    重新设计的幸运值系统类
    专注于影响伤害的上下限波动
    """
    
    # 不同幸运值对应的最大伤害概率
    MAX_DAMAGE_PROBABILITIES = {
        0: 0.1,     # 10%
        1: 1/9,     # 11.1%
        2: 1/8,     # 12.5%
        3: 1/7,     # 14.3%
        4: 1/6,     # 16.7%
        5: 0.2,     # 20%
        6: 0.25,    # 25%
        7: 1/3,     # 33%
        8: 0.5,     # 50%
    }
    
    @staticmethod
    def calculate_damage_with_luck(min_damage, max_damage, luck_value):
        """
        根据幸运值计算伤害
        
        Args:
            min_damage: 最小伤害
            max_damage: 最大伤害
            luck_value: 幸运值
            
        Returns:
            计算后的伤害值
        """
        # 确保伤害范围有效
        if min_damage >= max_damage:
            return max_damage
        
        # 获取玩家幸运值，确保在有效范围内
        player_luck = max(0, min(9, luck_value))
        
        # 如果幸运值≥9，必定造成最大伤害
        if player_luck >= 9:
            damage = max_damage
            return damage
        
        # 获取对应幸运值的最大伤害概率
        max_damage_prob = LuckSystem.MAX_DAMAGE_PROBABILITIES.get(player_luck, 0.1)
        
        # 随机决定是否造成最大伤害
        if random.random() < max_damage_prob:
            damage = max_damage
        else:
            # 如果不是最大伤害，在最小值和最大值之间随机
            damage = random.randint(min_damage, max_damage)
        
        return damage
    
    @staticmethod
    def get_max_damage_probability(luck_value):
        """
        获取指定幸运值的最大伤害概率
        
        Args:
            luck_value: 幸运值
            
        Returns:
            最大伤害概率
        """
        player_luck = max(0, min(9, luck_value))
        if player_luck >= 9:
            return 1.0  # 100%
        return LuckSystem.MAX_DAMAGE_PROBABILITIES.get(player_luck, 0.1)
    
    @staticmethod
    def get_luck_description(luck_value):
        """
        获取幸运值的描述信息
        
        Args:
            luck_value: 幸运值
            
        Returns:
            描述字符串
        """
        player_luck = max(0, min(9, luck_value))
        if player_luck >= 9:
            return f"幸运值{player_luck}: 必定造成最大伤害"
        
        prob = LuckSystem.MAX_DAMAGE_PROBABILITIES.get(player_luck, 0.1)
        return f"幸运值{player_luck}: {prob*100:.1f}%概率造成最大伤害" 