#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理器
解决PyInstaller打包后的资源路径问题
"""

import os
import sys
from pathlib import Path
import pygame

def resource_path(relative_path):
    """
    获取资源文件的正确路径 - 核心函数
    
    这个函数自动处理开发环境和PyInstaller打包环境的路径差异
    
    Args:
        relative_path: 相对于项目根目录的路径，如 "game/assets/images/background.png"
    
    Returns:
        资源文件的绝对路径
    """
    try:
        # 检查是否在PyInstaller环境中运行
        if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
            # PyInstaller --onefile模式：文件被解压到临时目录
            base_path = sys._MEIPASS
        else:
            # 开发环境或PyInstaller --onedir模式
            # 获取项目根目录（main.py所在的目录）
            base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # 构建完整路径
        full_path = os.path.join(base_path, relative_path)
        
        # 标准化路径（处理不同操作系统的路径分隔符）
        full_path = os.path.normpath(full_path)
        
        return full_path
    
    except Exception as e:
        print(f"资源路径解析错误: {e}")
        # 发生错误时的降级处理
        return os.path.join(os.getcwd(), relative_path)

def get_resource_path(relative_path):
    """
    获取资源文件的正确路径（resource_path的别名，保持向后兼容）
    """
    return resource_path(relative_path)

def get_game_asset_path(asset_path):
    """
    获取游戏资源文件路径的便捷函数
    
    Args:
        asset_path: 相对于game/assets的路径，如 "images/background.png"
    
    Returns:
        资源文件的绝对路径
    """
    return resource_path(f"game/assets/{asset_path}")

def get_game_data_path(data_file):
    """
    获取游戏数据文件路径的便捷函数
    
    Args:
        data_file: 相对于game/data的文件名，如 "monsters.json"
    
    Returns:
        数据文件的绝对路径
    """
    return resource_path(f"game/data/{data_file}")

def get_ui_asset_path(ui_file):
    """
    获取UI资源文件路径的便捷函数
    
    Args:
        ui_file: 相对于ui的文件名
    
    Returns:
        UI文件的绝对路径
    """
    return resource_path(f"ui/{ui_file}")

def get_saves_path(save_file=""):
    """
    获取存档文件路径 - 支持用户文档目录
    
    Args:
        save_file: 存档文件名（可选）
    
    Returns:
        存档文件路径
    """
    try:
        if getattr(sys, 'frozen', False):
            # 打包后的环境，使用用户文档目录
            import tempfile
            saves_dir = os.path.join(tempfile.gettempdir(), "萝卜传奇", "saves")
        else:
            # 开发环境，使用项目目录
            saves_dir = resource_path("saves")
        
        # 确保目录存在
        os.makedirs(saves_dir, exist_ok=True)
        
        if save_file:
            return os.path.join(saves_dir, save_file)
        else:
            return saves_dir
    except Exception as e:
        print(f"存档路径解析错误: {e}")
        # 降级处理
        return resource_path(f"saves/{save_file}" if save_file else "saves")

def get_logs_path(log_file=""):
    """
    获取日志文件路径
    
    Args:
        log_file: 日志文件名（可选）
    
    Returns:
        日志文件路径
    """
    try:
        if getattr(sys, 'frozen', False):
            # 打包后的环境，使用用户文档目录
            import tempfile
            logs_dir = os.path.join(tempfile.gettempdir(), "萝卜传奇", "logs")
        else:
            # 开发环境，使用项目目录
            logs_dir = resource_path("logs")
        
        # 确保目录存在
        os.makedirs(logs_dir, exist_ok=True)
        
        if log_file:
            return os.path.join(logs_dir, log_file)
        else:
            return logs_dir
    except Exception as e:
        print(f"日志路径解析错误: {e}")
        # 降级处理
        return resource_path(f"logs/{log_file}" if log_file else "logs")

def list_available_images(image_dir):
    """
    列出指定目录下的所有图片文件
    
    Args:
        image_dir: 相对于game/assets/images的目录，如 "characters"
    
    Returns:
        图片文件名列表
    """
    full_dir = get_game_asset_path(f"images/{image_dir}")
    
    if not os.path.exists(full_dir):
        return []
    
    image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp')
    images = []
    
    try:
        for file in os.listdir(full_dir):
            if file.lower().endswith(image_extensions):
                images.append(file)
    except OSError:
        pass
    
    return sorted(images)

def resource_exists(relative_path):
    """
    检查资源文件是否存在
    
    Args:
        relative_path: 相对路径
    
    Returns:
        True if exists, False otherwise
    """
    return os.path.exists(resource_path(relative_path))

def debug_resource_paths():
    """
    调试函数，打印资源路径信息
    """
    print("🔍 资源路径调试信息:")
    print(f"sys.frozen: {getattr(sys, 'frozen', False)}")
    print(f"sys._MEIPASS: {getattr(sys, '_MEIPASS', 'N/A')}")
    print(f"__file__: {__file__}")
    print(f"项目根目录: {resource_path('')}")
    print(f"游戏资源目录: {get_game_asset_path('')}")
    print(f"存档目录: {get_saves_path()}")
    print(f"日志目录: {get_logs_path()}")

def init_resource_manager():
    """
    初始化资源管理器，确保必要的目录存在
    """
    try:
        # 确保saves和logs目录存在
        get_saves_path()
        get_logs_path()
        
        print("✅ 资源管理器初始化完成")
        return True
    except Exception as e:
        print(f"❌ 资源管理器初始化失败: {e}")
        return False 