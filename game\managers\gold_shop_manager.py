#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime, date
from typing import Dict, Any, List, Optional

class GoldShopManager:
    """
    金币商城管理器
    负责处理金币商城的所有业务逻辑，包括商品购买、金币扣除、库存管理等
    """
    
    def __init__(self):
        """
        初始化金币商城管理器
        """
        self.config = self._load_config()
        self.purchase_records = {}  # 存储玩家购买记录
        
    def _load_config(self) -> Dict[str, Any]:
        """
        加载金币商城配置文件
        
        Returns:
            Dict[str, Any]: 商城配置数据
        """
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'gold_shop_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载金币商城配置失败: {e}")
            # 返回默认配置
            return {
                "categories": {},
                "shop_settings": {
                    "refresh_time": "00:00",
                    "currency_type": "gold",
                    "max_daily_purchases": 100,
                    "vip_bonus_enabled": True
                }
            }
    
    def get_categories(self) -> Dict[str, Any]:
        """
        获取所有商城分类
        
        Returns:
            Dict[str, Any]: 分类信息
        """
        return self.config.get('categories', {})
    
    def get_category_items(self, category_name: str) -> List[Dict[str, Any]]:
        """
        获取指定分类的商品列表
        
        Args:
            category_name: 分类名称
            
        Returns:
            List[Dict[str, Any]]: 商品列表
        """
        categories = self.get_categories()
        if category_name in categories:
            return categories[category_name].get('items', [])
        return []
    
    def can_purchase_item(self, player, item_id: str, category_name: str) -> Dict[str, Any]:
        """
        检查玩家是否可以购买指定商品
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            category_name: 分类名称
            
        Returns:
            Dict[str, Any]: 检查结果
        """
        try:
            # 获取商品信息
            items = self.get_category_items(category_name)
            item_info = None
            for item in items:
                if item['item_id'] == item_id:
                    item_info = item
                    break
            
            if not item_info:
                return {'can_purchase': False, 'reason': '商品不存在'}
            
            # 检查玩家金币
            player_gold = self._get_player_gold(player)
            item_cost = self._calculate_item_cost(player, item_info)
            
            if player_gold < item_cost:
                return {
                    'can_purchase': False, 
                    'reason': f'金币不足，需要{item_cost}金币，当前{player_gold}金币'
                }
            
            # 检查每日购买限制
            daily_purchases = self._get_daily_purchases(player, item_id)
            daily_limit = item_info.get('daily_limit', -1)
            
            if daily_limit > 0 and daily_purchases >= daily_limit:
                return {
                    'can_purchase': False, 
                    'reason': f'今日购买次数已达上限({daily_limit}次)'
                }
            
            # 检查库存
            stock = item_info.get('stock', -1)
            if stock == 0:
                return {'can_purchase': False, 'reason': '商品已售罄'}
            
            # 检查等级要求
            level_requirement = item_info.get('level_requirement', 1)
            if hasattr(player, 'level') and player.level < level_requirement:
                return {
                    'can_purchase': False, 
                    'reason': f'等级不足，需要{level_requirement}级'
                }
            
            # 检查职业要求
            class_requirement = item_info.get('class_requirement', [])
            if class_requirement and hasattr(player, 'character_class'):
                if player.character_class not in class_requirement:
                    return {
                        'can_purchase': False, 
                        'reason': f'职业不符，需要{"/".join(class_requirement)}'
                    }
            
            return {
                'can_purchase': True, 
                'cost': item_cost,
                'item_info': item_info
            }
            
        except Exception as e:
            print(f"检查购买条件时出错: {e}")
            return {'can_purchase': False, 'reason': '系统错误'}
    
    def purchase_item(self, player, item_id: str, category_name: str, quantity: int = 1) -> Dict[str, Any]:
        """
        购买商品
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            category_name: 分类名称
            quantity: 购买数量
            
        Returns:
            Dict[str, Any]: 购买结果
        """
        try:
            # 检查是否可以购买
            check_result = self.can_purchase_item(player, item_id, category_name)
            if not check_result['can_purchase']:
                return {
                    'success': False,
                    'message': check_result['reason']
                }
            
            item_info = check_result['item_info']
            total_cost = check_result['cost'] * quantity
            
            # 扣除金币
            if not self._deduct_player_gold(player, total_cost):
                return {
                    'success': False,
                    'message': '金币扣除失败'
                }
            
            # 添加物品到玩家背包
            if not self._add_item_to_player(player, item_id, quantity):
                # 如果添加物品失败，退还金币
                self._add_player_gold(player, total_cost)
                return {
                    'success': False,
                    'message': '背包空间不足'
                }
            
            # 记录购买
            self._record_purchase(player, item_id, quantity, total_cost)
            
            return {
                'success': True,
                'message': f'成功购买{item_info["name"]} x{quantity}',
                'item_name': item_info['name'],
                'quantity': quantity,
                'cost': total_cost
            }
            
        except Exception as e:
            print(f"购买商品时出错: {e}")
            return {
                'success': False,
                'message': '购买失败，请稍后重试'
            }
    
    def _get_player_gold(self, player) -> int:
        """
        获取玩家金币
        
        Args:
            player: 玩家对象
            
        Returns:
            int: 玩家金币
        """
        if hasattr(player, 'gold'):
            return player.gold
        return 0
    
    def _deduct_player_gold(self, player, gold: int) -> bool:
        """
        扣除玩家金币
        
        Args:
            player: 玩家对象
            gold: 扣除的金币数量
            
        Returns:
            bool: 是否成功扣除
        """
        try:
            if hasattr(player, 'gold') and player.gold >= gold:
                player.gold -= gold
                return True
            return False
        except Exception as e:
            print(f"扣除金币失败: {e}")
            return False
    
    def _add_player_gold(self, player, gold: int) -> bool:
        """
        给玩家添加金币（用于退款）
        
        Args:
            player: 玩家对象
            gold: 添加的金币数量
            
        Returns:
            bool: 是否成功添加
        """
        try:
            if hasattr(player, 'gold'):
                player.gold += gold
                return True
            return False
        except Exception as e:
            print(f"添加金币失败: {e}")
            return False
    
    def _calculate_item_cost(self, player, item_info: Dict[str, Any]) -> int:
        """
        计算商品实际价格（考虑VIP折扣等）
        
        Args:
            player: 玩家对象
            item_info: 商品信息
            
        Returns:
            int: 实际价格
        """
        base_cost = item_info.get('gold_cost', 0)
        
        # VIP折扣
        if hasattr(player, 'vip_level') and player.vip_level > 0:
            discount = min(0.1 * player.vip_level, 0.5)  # 每级VIP 10%折扣，最多50%
            base_cost = int(base_cost * (1 - discount))
        
        return max(1, base_cost)  # 最少1金币
    
    def _add_item_to_player(self, player, item_id: str, quantity: int) -> bool:
        """
        添加物品到玩家背包
        
        Args:
            player: 玩家对象
            item_id: 物品ID
            quantity: 数量
            
        Returns:
            bool: 是否成功添加
        """
        try:
            # 检查玩家是否有背包管理器
            if hasattr(player, 'inventory_manager'):
                # 尝试添加物品到背包
                return player.inventory_manager.add_item(item_id, quantity)
            elif hasattr(player, 'inventory'):
                # 如果没有背包管理器，直接操作背包
                if hasattr(player.inventory, 'add_item'):
                    return player.inventory.add_item(item_id, quantity)
                else:
                    # 简单的背包添加逻辑
                    if item_id not in player.inventory:
                        player.inventory[item_id] = 0
                    player.inventory[item_id] += quantity
                    return True
            return False
        except Exception as e:
            print(f"添加物品到背包失败: {e}")
            return False
    
    def _get_daily_purchases(self, player, item_id: str) -> int:
        """
        获取玩家今日购买次数
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            
        Returns:
            int: 今日购买次数
        """
        try:
            today = date.today().isoformat()
            player_id = getattr(player, 'name', 'unknown')
            
            if player_id not in self.purchase_records:
                self.purchase_records[player_id] = {}
            
            if today not in self.purchase_records[player_id]:
                self.purchase_records[player_id][today] = {}
            
            return self.purchase_records[player_id][today].get(item_id, 0)
        except Exception as e:
            print(f"获取购买记录失败: {e}")
            return 0
    
    def _record_purchase(self, player, item_id: str, quantity: int, cost: int):
        """
        记录购买信息
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            quantity: 购买数量
            cost: 消费金币
        """
        try:
            today = date.today().isoformat()
            player_id = getattr(player, 'name', 'unknown')
            
            if player_id not in self.purchase_records:
                self.purchase_records[player_id] = {}
            
            if today not in self.purchase_records[player_id]:
                self.purchase_records[player_id][today] = {}
            
            if item_id not in self.purchase_records[player_id][today]:
                self.purchase_records[player_id][today][item_id] = 0
            
            self.purchase_records[player_id][today][item_id] += quantity
            
        except Exception as e:
            print(f"记录购买信息失败: {e}") 