# -*- coding: utf-8 -*-
"""
测试装备管理器
"""

from game.managers.equipment_manager import EquipmentManager

def test_equipment_manager():
    # 创建装备管理器实例
    equipment_manager = EquipmentManager()
    
    # 测试get_equipment_set_bonus方法
    set_bonuses = equipment_manager.get_equipment_set_bonus()
    print(f"套装加成: {set_bonuses}")
    
    # 测试_recalculate_equipment_stats方法
    equipment_manager._recalculate_equipment_stats()
    print(f"装备属性: {equipment_manager.equipment_stats}")
    
    print("测试完成")

if __name__ == "__main__":
    test_equipment_manager()