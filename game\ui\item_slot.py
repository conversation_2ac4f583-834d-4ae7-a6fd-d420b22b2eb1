import pygame
import os
from game.core.resource_manager import get_game_asset_path
from game.core.log_manager import get_log_manager
from game.utils.attribute_translator import format_attribute_text, ATTRIBUTE_MAP
from game.data.equipment_converter import EquipmentConverter


class ItemSlot:
    """物品格子组件，用于显示物品图标和数量"""
    
    # 类级别的图片缓存
    item_images = {}
    
    # 缓存配置
    MAX_CACHE_SIZE = 1000
    
    # 颜色常量
    COLORS = {
        'selected_bg': (60, 60, 60),
        'normal_bg': (40, 40, 40),
        'selected_border': (255, 255, 0),
        'normal_border': (100, 100, 100),
        'locked_border': (255, 0, 0),
        'text': (255, 255, 255),
        'durability_high': (120, 200, 120),
        'durability_medium': (200, 200, 120),
        'durability_low': (200, 120, 120),
        'special_effect': (200, 120, 200),
        'gold': (255, 215, 0),
        'description': (180, 180, 220),
        'separator': (0, 0, 0),
        'stat_increase': (60, 220, 60),  # 绿色
        'stat_decrease': (220, 60, 60),  # 红色
    }
    
    # 配置常量
    TOOLTIP_OFFSET_X = 15
    TOOLTIP_OFFSET_Y = 10
    DURABILITY_THRESHOLD_HIGH = 0.5
    DURABILITY_THRESHOLD_LOW = 0.2
    BORDER_MARGIN = 6  # 内容边距
    
    def __init__(self, screen, position, size=(50, 50)):
        """
        初始化物品格子
        
        Args:
            screen: pygame显示表面
            position: 格子位置 (x, y)
            size: 格子大小 (width, height)
        """
        self.screen = screen
        self.position = position
        self.item_slot_width, self.item_slot_height = size
        self.rect = pygame.Rect(position[0], position[1], self.item_slot_width, self.item_slot_height)
        
        # 物品信息
        self.item = None
        self.quantity = 0
        
        # 状态
        self.selected = False
        self.locked = False
        
        # 字体
        self.font = pygame.font.SysFont("SimHei", 12)
        
        # 初始化tooltip系统（使用装备界面相同配置）
        self.tooltip_font = pygame.font.SysFont("SimHei", 14)
        self.tooltip_title_font = pygame.font.SysFont("SimHei", 16, bold=True)
        
        # 提示框样式配置（与装备界面完全相同）
        self.tooltip_config = {
            'background_color': (30, 30, 40),
            'border_color': (100, 100, 120),
            'title_color': (255, 215, 0),  # 金色标题
            'normal_text_color': (220, 220, 220),  # 普通文本
            'stat_color': (120, 200, 120),  # 属性值颜色（绿色）
            'requirement_color': (200, 120, 120),  # 需求颜色（红色）
            'comparison_increase_color': (60, 220, 60),  # 对比提升颜色
            'comparison_decrease_color': (220, 60, 60),  # 对比下降颜色
            'padding': 10,
            'line_spacing': 20,
            'max_width': 280,
            'border_width': 2
        }
    
    def set_item(self, item, quantity=1):
        """
        设置物品
        
        Args:
            item: 物品对象
            quantity: 物品数量
        """
        self.item = item
        self.quantity = quantity
    
    def clear_item(self):
        """清空物品"""
        self.item = None
        self.quantity = 0
    
    def toggle_selected(self):
        """切换选中状态"""
        self.selected = not self.selected
    
    def toggle_locked(self):
        """切换锁定状态"""
        self.locked = not self.locked
    
    def render(self):
        """
        渲染物品格子
        """
        # 绘制格子背景
        color = self.COLORS['selected_bg'] if self.selected else self.COLORS['normal_bg']
        pygame.draw.rect(self.screen, color, self.rect)
        
        # 绘制格子边框
        if self.locked:
            border_color = self.COLORS['locked_border']
        elif self.selected:
            border_color = self.COLORS['selected_border']
        else:
            border_color = self.COLORS['normal_border']
        pygame.draw.rect(self.screen, border_color, self.rect, 2)
        
        # 如果有物品，绘制物品图标
        if self.item:
            # 获取物品图标
            item_image = self._get_item_image(self.item)
            if item_image:
                # 计算图标位置（居中）
                image_rect = item_image.get_rect()
                image_rect.center = self.rect.center
                self.screen.blit(item_image, image_rect)
            else:
                # 如果没有图标，显示物品名称的首字
                name = getattr(self.item, 'name', '?')
                first_char = name[0] if name else '?'
                text_surface = self.font.render(first_char, True, self.COLORS['text'])
                text_rect = text_surface.get_rect(center=self.rect.center)
                self.screen.blit(text_surface, text_rect)
            
            # 显示数量（如果数量大于1）
            if self.quantity > 1:
                quantity_text = str(self.quantity)
                quantity_surface = self.font.render(quantity_text, True, self.COLORS['text'])
                quantity_rect = quantity_surface.get_rect()
                quantity_rect.bottomright = (self.rect.right - 2, self.rect.bottom - 2)
                self.screen.blit(quantity_surface, quantity_rect)
        
        # 显示锁定标志
        if self.locked:
            # 在物品格子左上角显示锁标志
            lock_text = "🔒"  # 使用锁的Unicode字符
            lock_surface = self.font.render(lock_text, True, self.COLORS['text'])
            lock_rect = lock_surface.get_rect()
            lock_rect.topleft = (self.rect.left + 2, self.rect.top + 2)
            self.screen.blit(lock_surface, lock_rect)
    
    def render_tooltip(self, mouse_pos, comparison_item=None):
        """
        渲染悬停提示框

        Args:
            mouse_pos: 鼠标位置
            comparison_item: 用于对比的装备 (可选)
        """
        if not self.item or not self.rect.collidepoint(mouse_pos):
            return

        try:
            # 使用装备界面相同的tooltip渲染逻辑
            self._render_equipment_tooltip(mouse_pos, comparison_item)
        except Exception as e:
            import traceback
            print(f"❌ 装备浮窗渲染错误: {e}")
            print(f"❌ 错误详情: {traceback.format_exc()}")
            print(f"❌ 装备数据: {getattr(self.item, 'name', '未知')}")
            print(f"❌ 对比装备: {comparison_item.get('name', '未知') if comparison_item else '无'}")
 
    def _get_rarity_color(self, rarity):
        """根据品质获取颜色（与装备界面完全相同）"""
        rarity_colors = {
            '普通': (200, 200, 200),      # 灰色
            '精良': (120, 200, 120),      # 绿色
            '稀有': (120, 120, 200),      # 蓝色
            '史诗': (200, 120, 200),      # 紫色
            '传说': (255, 165, 0),        # 橙色
            '神话': (255, 215, 0),        # 金色
            # 英文品质名称支持
            'common': (200, 200, 200),
            'uncommon': (120, 200, 120),
            'rare': (120, 120, 200),
            'epic': (200, 120, 200),
            'legendary': (255, 165, 0),
            'mythic': (255, 215, 0)
        }
        return rarity_colors.get(rarity, (200, 200, 200))
    
    def _get_durability_color(self, durability, max_durability):
        """获取耐久度显示颜色"""
        if max_durability <= 0:
            return self.COLORS['durability_high']
        
        ratio = durability / max_durability
        if ratio > self.DURABILITY_THRESHOLD_HIGH:
            return self.COLORS['durability_high']
        elif ratio > self.DURABILITY_THRESHOLD_LOW:
            return self.COLORS['durability_medium']
        else:
            return self.COLORS['durability_low']
    
    def _add_tooltip_separator(self, lines):
        """添加tooltip分隔空行"""
        lines.append({
            'text': '', 
            'color': self.COLORS['separator'], 
            'font': self.tooltip_font
        })
    
    @classmethod
    def _manage_cache(cls):
        """管理缓存大小，防止内存泄漏"""
        if len(cls.item_images) > cls.MAX_CACHE_SIZE:
            # 清理最老的一半缓存
            items_to_remove = list(cls.item_images.keys())[:cls.MAX_CACHE_SIZE // 2]
            for key in items_to_remove:
                del cls.item_images[key]
    
    def _create_tooltip_lines(self, equipment, comparison_item=None):
        """
        创建装备提示框数据，增加对比功能
        
        Args:
            equipment: 装备数据
            comparison_item: 用于对比的装备 (可选)
            
        Returns:
            list: 提示框行数据列表
        """
        if not equipment:
            return []

        lines = []

        # 1. 装备名称
        name = getattr(equipment, 'name', '未知装备')
        lines.append({'text': name, 'color': self.tooltip_config['title_color'], 'font': self.tooltip_title_font})

        # 删除第二行的品质和类型显示，移到最后
        self._add_tooltip_separator(lines)

        # 3. 核心属性（带对比）
        try:
            item_stats = self._convert_equipment_stats(equipment)
            comp_stats = self._convert_equipment_stats(comparison_item) if comparison_item else {}
        except Exception as e:
            print(f"❌ 转换装备属性时出错: {e}")
            item_stats = {}
            comp_stats = {}
        
        # 定义核心属性的显示顺序和更多属性
        core_stats_order = [
            'attack', 'magic', 'taoism', 'defense', 'magic_defense', 'hp', 'mp'
        ]

        # 额外属性显示顺序
        extra_stats_order = [
            'accuracy', 'agility', 'luck', 'critical_hit', 'critical_damage',
            'hp_regen', 'mp_regen', 'exp_bonus', 'drop_rate_bonus'
        ]

        # 显示核心属性
        for stat_key in core_stats_order:
            if stat_key in item_stats:
                try:
                    stat_text = format_attribute_text(stat_key, item_stats[stat_key])
                    comp_value = comp_stats.get(stat_key)

                    if comp_value is not None:
                        # 如果是范围属性，取平均值对比
                        item_value = item_stats[stat_key]
                        if isinstance(item_value, (list, tuple)) and len(item_value) >= 2:
                            item_avg = sum(item_value) / len(item_value)
                        else:
                            item_avg = float(item_value) if item_value is not None else 0

                        if isinstance(comp_value, (list, tuple)) and len(comp_value) >= 2:
                            comp_avg = sum(comp_value) / len(comp_value)
                        else:
                            comp_avg = float(comp_value) if comp_value is not None else 0

                        diff = item_avg - comp_avg

                        # 🔧 修复：显示对比信息，即使差异为0也显示
                        if diff > 0.1:  # 大于0.1才显示提升
                            stat_text += f" (+{diff:.1f} ↑)"
                            color = self.tooltip_config['comparison_increase_color']
                        elif diff < -0.1:  # 小于-0.1才显示降低
                            stat_text += f" ({diff:.1f} ↓)"
                            color = self.tooltip_config['comparison_decrease_color']
                        else:  # 差异很小或相等，显示当前装备的数值作为对比
                            if isinstance(comp_value, (list, tuple)) and len(comp_value) >= 2:
                                stat_text += f" (当前: {comp_value[0]}-{comp_value[1]})"
                            else:
                                stat_text += f" (当前: {comp_value})"
                            color = self.tooltip_config['normal_text_color']
                    else:
                        color = self.tooltip_config['normal_text_color']

                    lines.append({'text': stat_text, 'color': color, 'font': self.tooltip_font})
                except Exception as e:
                    print(f"❌ 格式化属性 {stat_key} 时出错: {e}")
                    # 添加一个简单的属性行作为备用
                    lines.append({'text': f"{stat_key}: {item_stats[stat_key]}", 'color': self.tooltip_config['normal_text_color'], 'font': self.tooltip_font})

        # 显示额外属性
        has_extra_stats = False
        for stat_key in extra_stats_order:
            if stat_key in item_stats:
                try:
                    stat_text = format_attribute_text(stat_key, item_stats[stat_key])
                    comp_value = comp_stats.get(stat_key)

                    if comp_value is not None:
                        # 对比逻辑与核心属性相同
                        item_value = item_stats[stat_key]
                        if isinstance(item_value, (list, tuple)) and len(item_value) >= 2:
                            item_avg = sum(item_value) / len(item_value)
                        else:
                            item_avg = float(item_value) if item_value is not None else 0

                        if isinstance(comp_value, (list, tuple)) and len(comp_value) >= 2:
                            comp_avg = sum(comp_value) / len(comp_value)
                        else:
                            comp_avg = float(comp_value) if comp_value is not None else 0

                        diff = item_avg - comp_avg

                        if diff > 0.1:
                            stat_text += f" (+{diff:.1f} ↑)"
                            color = self.tooltip_config['comparison_increase_color']
                        elif diff < -0.1:
                            stat_text += f" ({diff:.1f} ↓)"
                            color = self.tooltip_config['comparison_decrease_color']
                        else:
                            if isinstance(comp_value, (list, tuple)) and len(comp_value) >= 2:
                                stat_text += f" (当前: {comp_value[0]}-{comp_value[1]})"
                            else:
                                stat_text += f" (当前: {comp_value})"
                            color = self.tooltip_config['normal_text_color']
                    else:
                        color = self.tooltip_config['normal_text_color']

                    lines.append({'text': stat_text, 'color': color, 'font': self.tooltip_font})
                    has_extra_stats = True
                except Exception as e:
                    print(f"❌ 格式化额外属性 {stat_key} 时出错: {e}")

        # 如果有额外属性或核心属性，添加分隔符
        if has_extra_stats or any(stat_key in item_stats for stat_key in core_stats_order):
            self._add_tooltip_separator(lines)

        # 4. 其他属性
        other_attributes = getattr(equipment, 'attributes', {})
        for attr, value in other_attributes.items():
            if attr not in core_stats_order and value != 0:
                attr_text = format_attribute_text(attr, value)
                lines.append({'text': attr_text, 'color': self.tooltip_config['normal_text_color'], 'font': self.tooltip_font})
        
        # 5. 特殊效果
        special_effects = getattr(equipment, 'special_effects', {})
        if special_effects:
            self._add_tooltip_separator(lines)
            for effect, value in special_effects.items():
                effect_text = format_attribute_text(effect, value)
                lines.append({'text': effect_text, 'color': self.COLORS['special_effect'], 'font': self.tooltip_font})

        # 6. 耐久度
        durability = getattr(equipment, 'durability', None)
        max_durability = getattr(equipment, 'max_durability', None)
        if durability is not None and max_durability is not None:
            self._add_tooltip_separator(lines)
            dur_color = self._get_durability_color(durability, max_durability)
            lines.append({'text': f"耐久: {durability}/{max_durability}", 'color': dur_color, 'font': self.tooltip_font})

        # 7. 装备要求
        requirements = getattr(equipment, 'requirements', {})
        if requirements:
            self._add_tooltip_separator(lines)
            req_text = "需求: " + ", ".join([f"{ATTRIBUTE_MAP.get(k, k)} {v}" for k, v in requirements.items()])
            lines.append({'text': req_text, 'color': self.tooltip_config['requirement_color'], 'font': self.tooltip_font})

        # 8. 描述（过滤掉品质信息）
        description = getattr(equipment, 'description', None)
        if description:
            # 过滤掉包含品质信息的描述
            quality_keywords = ['品质', '普通', '精良', '稀有', '史诗', '传说', '神话', 'common', 'uncommon', 'rare', 'epic', 'legendary', 'mythic']
            if not any(keyword in description.lower() for keyword in quality_keywords):
                self._add_tooltip_separator(lines)
                lines.append({'text': description, 'color': self.COLORS['description'], 'font': self.tooltip_font})
        
        # 9. 售价
        price = getattr(equipment, 'price', 0)
        if price > 0:
            self._add_tooltip_separator(lines)
            lines.append({'text': f"售价: {price}", 'color': self.COLORS['gold'], 'font': self.tooltip_font})



        return lines

    def _convert_equipment_stats(self, equipment):
        """将装备原始属性转换为标准字典"""
        if not equipment:
            return {}

        try:
            stats = {}

            # 方式1: 直接从装备对象获取属性
            if hasattr(equipment, 'attributes') and equipment.attributes:
                stats.update(equipment.attributes)

            # 方式2: 从stats字段获取
            if hasattr(equipment, 'stats') and equipment.stats:
                stats.update(equipment.stats)

            # 方式3: 如果是字典格式
            if isinstance(equipment, dict):
                if 'attributes' in equipment and equipment['attributes']:
                    stats.update(equipment['attributes'])
                if 'stats' in equipment and equipment['stats']:
                    stats.update(equipment['stats'])

            # 方式4: 尝试从装备的基础属性获取
            basic_attrs = ['attack', 'defense', 'magic', 'taoism', 'magic_defense', 'hp', 'mp']
            for attr in basic_attrs:
                if hasattr(equipment, attr):
                    value = getattr(equipment, attr)
                    if value and value != 0:
                        stats[attr] = value
                elif isinstance(equipment, dict) and attr in equipment:
                    value = equipment[attr]
                    if value and value != 0:
                        stats[attr] = value

            # 方式5: 如果还是没有属性，尝试生成一些示例属性（基于装备类型）
            if not stats:
                stats = self._generate_sample_stats(equipment)

            return stats

        except Exception as e:
            print(f"❌ 转换装备属性时出错: {e}")
            return {}

    def _generate_sample_stats(self, equipment):
        """为没有属性的装备生成示例属性"""
        stats = {}

        # 获取装备名称和类型
        name = getattr(equipment, 'name', '') or (equipment.get('name', '') if isinstance(equipment, dict) else '')
        slot = getattr(equipment, 'category', '') or getattr(equipment, 'slot', '') or (equipment.get('category', '') or equipment.get('slot', '') if isinstance(equipment, dict) else '')

        # 根据装备类型生成基础属性
        if '武器' in slot or any(weapon_word in name for weapon_word in ['剑', '刀', '斧', '棍', '杖']):
            stats['attack'] = [5, 10]
            stats['accuracy'] = 0.05
        elif '防具' in slot or '胸甲' in slot or any(armor_word in name for armor_word in ['衣', '甲', '袍']):
            stats['defense'] = [3, 8]
            stats['hp'] = 50
        elif '头盔' in slot or any(helmet_word in name for helmet_word in ['帽', '盔']):
            stats['defense'] = [2, 5]
            stats['hp'] = 30
        elif '戒指' in slot:
            stats['attack'] = [1, 3]
            stats['magic'] = [1, 3]
            stats['taoism'] = [1, 3]
        elif '手镯' in slot:
            stats['defense'] = [1, 3]
            stats['magic_defense'] = [1, 3]
        elif '护符' in slot or '项链' in slot:
            stats['hp'] = 20
            stats['mp'] = 20

        # 添加一些通用属性
        if stats:  # 只有当有基础属性时才添加
            stats['durability'] = 100
            stats['max_durability'] = 100

        return stats

    def _render_equipment_tooltip(self, mouse_pos, comparison_item=None):
        """
        使用标准化的数据结构渲染装备提示框

        Args:
            mouse_pos: 鼠标位置
            comparison_item: 用于对比的装备 (可选)
        """
        try:
            lines = self._create_tooltip_lines(self.item, comparison_item)
            if not lines:
                return
        except Exception as e:
            print(f"❌ 创建浮窗行数据时出错: {e}")
            return

        # 计算Tooltip大小和位置
        padding = self.tooltip_config['padding']
        line_spacing = self.tooltip_config['line_spacing']
        max_width = 0
        total_height = padding * 2
        
        surfaces = []
        for line in lines:
            text_surface = line['font'].render(line['text'], True, line['color'])
            surfaces.append(text_surface)
            max_width = max(max_width, text_surface.get_width())
            total_height += text_surface.get_height()
            if lines.index(line) < len(lines) -1:
                if line['text'] != '':
                     total_height += 2 #行间距
                else:
                    total_height -=10


        # 创建Tooltip表面
        tooltip_width = max_width + padding * 2
        tooltip_height = total_height 
        tooltip_surface = pygame.Surface((tooltip_width, tooltip_height), pygame.SRCALPHA)
        
        # 绘制背景和边框
        bg_rect = tooltip_surface.get_rect()
        pygame.draw.rect(tooltip_surface, (*self.tooltip_config['background_color'], 230), bg_rect, border_radius=5)
        pygame.draw.rect(tooltip_surface, self.tooltip_config['border_color'], bg_rect, self.tooltip_config['border_width'], border_radius=5)

        # 渲染每一行文本
        y = padding
        for i, surface in enumerate(surfaces):
            line_data = lines[i]
            x = (tooltip_width - surface.get_width()) // 2 # 居中
            tooltip_surface.blit(surface, (x, y))
            y += surface.get_height()
            if i < len(surfaces) -1:
                if line_data['text'] != '':
                     y += 2 #行间距
                else:
                    y -=10


        # 确定Tooltip位置
        x, y = mouse_pos
        x += self.TOOLTIP_OFFSET_X
        y += self.TOOLTIP_OFFSET_Y
        
        # 防止Tooltip超出屏幕
        screen_w, screen_h = self.screen.get_size()
        if x + tooltip_width > screen_w:
            x = screen_w - tooltip_width
        if y + tooltip_height > screen_h:
            y = screen_h - tooltip_height
            
        self.screen.blit(tooltip_surface, (x, y))

    def handle_event(self, event):
        """
        处理事件
        
        Args:
            event: pygame事件对象
        
        Returns:
            bool: 如果事件被处理，则返回True
        """
        # 鼠标左键点击
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if self.rect.collidepoint(event.pos):
                self.toggle_selected()
                return True
        
        # 鼠标右键点击
        elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 3:
            if self.rect.collidepoint(event.pos):
                # 如果是装备，则切换锁定状态
                if self.item and hasattr(self.item, 'type') and self.item.type == "equipment":
                    self.toggle_locked()
                return True
        
        return False

    def _get_item_image(self, item):
        """
        获取物品图片
        
        Args:
            item: 物品数据
        
        Returns:
            pygame.Surface或None: 缩放后的物品图片
        """
        logger = get_log_manager()
        if not item:
            return None
        
        # 定义装备类型集合，用于判断路径
        EQUIPMENT_TYPES = {"武器", "防具", "头盔", "项链", "手镯", "戒指", "勋章", "装备", "equipment", "weapon", "armor", "helmet", "necklace", "bracelet", "ring", "medal"}

        # 处理Item对象和字典两种格式
        if hasattr(item, 'name'):  # Item对象
            item_name = item.name
            item_type = getattr(item, 'type', "")
            icon_path = getattr(item, 'icon_path', None)
        elif isinstance(item, dict):  # 字典格式
            item_name = item.get("name", "")
            item_type = item.get("type", "")
            icon_path = item.get("icon_path", None)
        else:
            return None
        
        if not item_name:
            return None
        
        # 检查缓存
        cache_key = f"{item_type}_{item_name}"
        if cache_key in ItemSlot.item_images:
            return ItemSlot.item_images[cache_key]
        
        # 确定图片路径
        image_path = None
        slot = None  # 初始化slot变量
        try:
            if icon_path:
                # 标准化路径分隔符，防止混合使用 \ 和 /
                icon_path = icon_path.replace("\\", "/")
                
                if os.path.isabs(icon_path) or icon_path.startswith("game/"):
                    image_path = icon_path
                else:
                    # 如果是装备类型，则在 equipment 目录下查找
                    if item_type in EQUIPMENT_TYPES:
                        image_path = get_game_asset_path(f"images/equipment/{icon_path}")
                    else:
                        image_path = get_game_asset_path(f"images/{icon_path}")
            else:
                # 优先使用槽位信息来确定目录
                slot = item.get('slot') if isinstance(item, dict) else getattr(item, 'slot', None)
                
                if slot:
                    # 基于槽位的目录映射
                    slot_to_dir = {
                        "武器": "武器", 
                        "头盔": "头盔", 
                        "胸甲": "防具",
                        "护腿": "防具",
                        "靴子": "防具", 
                        "护符": "项链",
                        "戒指": "戒指", 
                        "手镯": "手镯", 
                        "勋章": "勋章"
                    }
                    
                    if slot in slot_to_dir:
                        equipment_dir = slot_to_dir[slot]
                        image_path = get_game_asset_path(f"images/equipment/{equipment_dir}/{item_name}.png")
                
                if not image_path:
                    # 如果没有槽位信息或槽位未匹配，使用类型映射
                    equipment_types_map = {
                        "武器": "武器", "防具": "防具", "头盔": "头盔", "项链": "项链",
                        "手镯": "手镯", "戒指": "戒指", "勋章": "勋章"
                    }
                    
                    if item_type in equipment_types_map:
                        equipment_dir = equipment_types_map[item_type]
                        image_path = get_game_asset_path(f"images/equipment/{equipment_dir}/{item_name}.png")
                    elif item_type == "装备":
                        # 对于通用"装备"类型，尝试根据名称推断
                        if any(keyword in item_name for keyword in ['剑', '刀', '斧', '棍', '杖', '锄', '匕首', '扇', '刃']):
                            image_path = get_game_asset_path(f"images/equipment/武器/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['衣', '甲', '袍', '披风']):
                            image_path = get_game_asset_path(f"images/equipment/防具/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['头盔', '帽']):
                            image_path = get_game_asset_path(f"images/equipment/头盔/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['项链', '珠', '链', '竹笛', '镜', '铃铛']):
                            image_path = get_game_asset_path(f"images/equipment/项链/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['戒指']):
                            image_path = get_game_asset_path(f"images/equipment/戒指/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['手镯', '手套']):
                            image_path = get_game_asset_path(f"images/equipment/手镯/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['勋章', '塔']):
                            image_path = get_game_asset_path(f"images/equipment/勋章/{item_name}.png")
                    elif item_type == "equipment":
                        # 对于英文"equipment"类型，同样尝试根据名称推断
                        if any(keyword in item_name for keyword in ['剑', '刀', '斧', '棍', '杖', '锄', '匕首', '扇', '刃']):
                            image_path = get_game_asset_path(f"images/equipment/武器/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['衣', '甲', '袍', '披风']):
                            image_path = get_game_asset_path(f"images/equipment/防具/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['头盔', '帽']):
                            image_path = get_game_asset_path(f"images/equipment/头盔/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['项链', '珠', '链', '竹笛', '镜', '铃铛']):
                            image_path = get_game_asset_path(f"images/equipment/项链/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['戒指']):
                            image_path = get_game_asset_path(f"images/equipment/戒指/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['手镯', '手套']):
                            image_path = get_game_asset_path(f"images/equipment/手镯/{item_name}.png")
                        elif any(keyword in item_name for keyword in ['勋章', '塔']):
                            image_path = get_game_asset_path(f"images/equipment/勋章/{item_name}.png")
                    elif item_type in ["消耗品", "药品", "药剂", "药水", "任务道具", "consumable"]:
                        image_path = get_game_asset_path(f"images/消耗品/{item_name}.png")
                    elif item_type in ["技能书", "skill_book"]:
                        image_path = get_game_asset_path("images/equipment/技能书/技能书.png")
        
        except Exception as e:
            logger.log_error("ItemSlot", f"确定图片路径时出错: {e}")
            return None
        
        # 检查路径是否生成成功
        if not image_path:
            slot_display = slot if slot else '无'
            return None
        
        # 加载图片
        image = None
        if image_path and os.path.exists(image_path):
            try:
                image = pygame.image.load(image_path).convert_alpha()
            except Exception as e:
                logger.log_error("ItemSlot", f"加载图片时发生异常: {e}")
                return None
        else:
            return None
        
        # 缩放和缓存
        if image:
            try:
                # 留出更多边距，确保图片不会超出格子
                border_margin = 6  # 增加边距
                content_width = self.item_slot_width - border_margin
                content_height = self.item_slot_height - border_margin

                # 确保最小尺寸
                content_width = max(content_width, 20)
                content_height = max(content_height, 20)

                # 获取原始图片尺寸
                img_width, img_height = image.get_size()

                # 计算缩放比例，确保图片完全适应格子
                scale_factor = min(content_width / img_width, content_height / img_height)
                # 确保缩放比例不会太小
                scale_factor = max(scale_factor, 0.1)

                new_width = int(img_width * scale_factor)
                new_height = int(img_height * scale_factor)

                # 确保缩放后的尺寸不为0
                new_width = max(new_width, 1)
                new_height = max(new_height, 1)

                # 缩放图片
                scaled_image = pygame.transform.scale(image, (new_width, new_height))

                # 缓存前检查大小限制
                ItemSlot._manage_cache()
                ItemSlot.item_images[cache_key] = scaled_image

                return scaled_image

            except Exception as e:
                logger.log_error("ItemSlot", f"缩放图片时发生异常: {e}")
                return None
        
        return None