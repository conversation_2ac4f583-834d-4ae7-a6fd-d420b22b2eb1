import heapq
import math
from typing import List, Tuple, Optional, Set


class PathNode:
    """路径节点类"""
    def __init__(self, x: int, y: int, g_cost: float = 0, h_cost: float = 0, parent=None):
        self.x = x
        self.y = y
        self.g_cost = g_cost  # 从起点到当前节点的实际代价
        self.h_cost = h_cost  # 从当前节点到终点的启发式代价
        self.f_cost = g_cost + h_cost  # 总代价
        self.parent = parent
        
    def __lt__(self, other):
        return self.f_cost < other.f_cost
        
    def __eq__(self, other):
        return self.x == other.x and self.y == other.y
        
    def __hash__(self):
        return hash((self.x, self.y))
        

class Pathfinding:
    """路径查找类"""
    
    def __init__(self, map_width: int = 50, map_height: int = 50):
        """
        初始化路径查找器
        
        参数:
            map_width: 地图宽度
            map_height: 地图高度
        """
        self.map_width = map_width
        self.map_height = map_height
        self.obstacles = set()  # 障碍物集合
        
    def add_obstacle(self, x: int, y: int):
        """
        添加障碍物
        
        参数:
            x: X坐标
            y: Y坐标
        """
        self.obstacles.add((x, y))
        
    def remove_obstacle(self, x: int, y: int):
        """
        移除障碍物
        
        参数:
            x: X坐标
            y: Y坐标
        """
        self.obstacles.discard((x, y))
        
    def clear_obstacles(self):
        """
        清除所有障碍物
        """
        self.obstacles.clear()
        
    def is_valid_position(self, x: int, y: int) -> bool:
        """
        检查位置是否有效
        
        参数:
            x: X坐标
            y: Y坐标
            
        返回:
            bool: 位置是否有效
        """
        return (0 <= x < self.map_width and 
                0 <= y < self.map_height and 
                (x, y) not in self.obstacles)
                
    def get_neighbors(self, node: PathNode) -> List[PathNode]:
        """
        获取节点的邻居
        
        参数:
            node: 当前节点
            
        返回:
            List[PathNode]: 邻居节点列表
        """
        neighbors = []
        
        # 8方向移动（包括对角线）
        directions = [
            (-1, -1), (-1, 0), (-1, 1),
            (0, -1),           (0, 1),
            (1, -1),  (1, 0),  (1, 1)
        ]
        
        for dx, dy in directions:
            new_x = node.x + dx
            new_y = node.y + dy
            
            if self.is_valid_position(new_x, new_y):
                # 计算移动代价（对角线移动代价更高）
                move_cost = 1.414 if dx != 0 and dy != 0 else 1.0
                neighbors.append(PathNode(new_x, new_y))
                
        return neighbors
        
    def heuristic(self, node: PathNode, goal: PathNode) -> float:
        """
        启发式函数（曼哈顿距离）
        
        参数:
            node: 当前节点
            goal: 目标节点
            
        返回:
            float: 启发式代价
        """
        return abs(node.x - goal.x) + abs(node.y - goal.y)
        
    def euclidean_distance(self, node: PathNode, goal: PathNode) -> float:
        """
        欧几里得距离
        
        参数:
            node: 当前节点
            goal: 目标节点
            
        返回:
            float: 欧几里得距离
        """
        return math.sqrt((node.x - goal.x) ** 2 + (node.y - goal.y) ** 2)
        
    def reconstruct_path(self, node: PathNode) -> List[Tuple[int, int]]:
        """
        重构路径
        
        参数:
            node: 终点节点
            
        返回:
            List[Tuple[int, int]]: 路径坐标列表
        """
        path = []
        current = node
        
        while current:
            path.append((current.x, current.y))
            current = current.parent
            
        return path[::-1]  # 反转路径，从起点到终点
        
    def find_path_astar(self, start: Tuple[int, int], goal: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        使用A*算法查找路径
        
        参数:
            start: 起点坐标 (x, y)
            goal: 终点坐标 (x, y)
            
        返回:
            List[Tuple[int, int]]: 路径坐标列表，如果找不到路径则返回空列表
        """
        start_node = PathNode(start[0], start[1])
        goal_node = PathNode(goal[0], goal[1])
        
        # 检查起点和终点是否有效
        if not self.is_valid_position(start[0], start[1]):
            return []
        if not self.is_valid_position(goal[0], goal[1]):
            return []
            
        # 如果起点就是终点
        if start == goal:
            return [start]
            
        open_set = [start_node]
        closed_set = set()
        
        # 用于快速查找开放集合中的节点
        open_dict = {(start_node.x, start_node.y): start_node}
        
        while open_set:
            # 获取f_cost最小的节点
            current = heapq.heappop(open_set)
            current_pos = (current.x, current.y)
            
            # 从开放字典中移除
            open_dict.pop(current_pos, None)
            
            # 添加到关闭集合
            closed_set.add(current_pos)
            
            # 检查是否到达目标
            if current.x == goal_node.x and current.y == goal_node.y:
                return self.reconstruct_path(current)
                
            # 检查所有邻居
            for neighbor in self.get_neighbors(current):
                neighbor_pos = (neighbor.x, neighbor.y)
                
                # 跳过已经在关闭集合中的节点
                if neighbor_pos in closed_set:
                    continue
                    
                # 计算移动代价
                move_cost = 1.414 if abs(neighbor.x - current.x) + abs(neighbor.y - current.y) == 2 else 1.0
                tentative_g_cost = current.g_cost + move_cost
                
                # 检查是否已经在开放集合中
                if neighbor_pos in open_dict:
                    existing_neighbor = open_dict[neighbor_pos]
                    if tentative_g_cost < existing_neighbor.g_cost:
                        # 找到更好的路径，更新节点
                        existing_neighbor.g_cost = tentative_g_cost
                        existing_neighbor.f_cost = tentative_g_cost + existing_neighbor.h_cost
                        existing_neighbor.parent = current
                        heapq.heapify(open_set)  # 重新排序堆
                else:
                    # 新节点，添加到开放集合
                    neighbor.g_cost = tentative_g_cost
                    neighbor.h_cost = self.heuristic(neighbor, goal_node)
                    neighbor.f_cost = neighbor.g_cost + neighbor.h_cost
                    neighbor.parent = current
                    
                    heapq.heappush(open_set, neighbor)
                    open_dict[neighbor_pos] = neighbor
                    
        # 没有找到路径
        return []
        
    def find_path_simple(self, start: Tuple[int, int], goal: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        简单的直线路径查找（不考虑障碍物）
        
        参数:
            start: 起点坐标 (x, y)
            goal: 终点坐标 (x, y)
            
        返回:
            List[Tuple[int, int]]: 路径坐标列表
        """
        if start == goal:
            return [start]
            
        path = []
        start_x, start_y = start
        end_x, end_y = goal
        
        # 计算步数
        dx = abs(end_x - start_x)
        dy = abs(end_y - start_y)
        steps = max(dx, dy)
        
        if steps == 0:
            return [start]
            
        # 计算每步的增量
        x_step = (end_x - start_x) / steps
        y_step = (end_y - start_y) / steps
        
        # 生成路径点
        for i in range(steps + 1):
            x = int(start_x + x_step * i)
            y = int(start_y + y_step * i)
            
            # 确保坐标在地图范围内
            x = max(0, min(x, self.map_width - 1))
            y = max(0, min(y, self.map_height - 1))
            
            path.append((x, y))
            
        return path
        
    def find_nearest_walkable_position(self, target: Tuple[int, int], max_radius: int = 5) -> Optional[Tuple[int, int]]:
        """
        查找最近的可行走位置
        
        参数:
            target: 目标位置
            max_radius: 最大搜索半径
            
        返回:
            Optional[Tuple[int, int]]: 最近的可行走位置，如果找不到则返回None
        """
        target_x, target_y = target
        
        # 如果目标位置本身就可行走，直接返回
        if self.is_valid_position(target_x, target_y):
            return target
            
        # 螺旋搜索最近的可行走位置
        for radius in range(1, max_radius + 1):
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    # 只检查当前半径圆周上的点
                    if abs(dx) == radius or abs(dy) == radius:
                        new_x = target_x + dx
                        new_y = target_y + dy
                        
                        if self.is_valid_position(new_x, new_y):
                            return (new_x, new_y)
                            
        return None
        
    def optimize_path(self, path: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """
        优化路径，移除不必要的中间点
        
        参数:
            path: 原始路径
            
        返回:
            List[Tuple[int, int]]: 优化后的路径
        """
        if len(path) <= 2:
            return path
            
        optimized = [path[0]]  # 起点
        
        i = 0
        while i < len(path) - 1:
            # 尝试找到最远的可直达点
            j = len(path) - 1
            while j > i + 1:
                if self._can_walk_straight(path[i], path[j]):
                    optimized.append(path[j])
                    i = j
                    break
                j -= 1
            else:
                # 如果没有找到可直达的远点，移动到下一个点
                i += 1
                if i < len(path):
                    optimized.append(path[i])
                    
        return optimized
        
    def _can_walk_straight(self, start: Tuple[int, int], end: Tuple[int, int]) -> bool:
        """
        检查两点之间是否可以直线行走
        
        参数:
            start: 起点
            end: 终点
            
        返回:
            bool: 是否可以直线行走
        """
        # 使用Bresenham直线算法检查路径上是否有障碍物
        x0, y0 = start
        x1, y1 = end
        
        dx = abs(x1 - x0)
        dy = abs(y1 - y0)
        
        x_step = 1 if x0 < x1 else -1
        y_step = 1 if y0 < y1 else -1
        
        error = dx - dy
        
        x, y = x0, y0
        
        while True:
            # 检查当前位置是否可行走
            if not self.is_valid_position(x, y):
                return False
                
            if x == x1 and y == y1:
                break
                
            error2 = 2 * error
            
            if error2 > -dy:
                error -= dy
                x += x_step
                
            if error2 < dx:
                error += dx
                y += y_step
                
        return True