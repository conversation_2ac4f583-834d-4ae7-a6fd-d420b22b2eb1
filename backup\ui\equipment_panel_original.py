"""
装备面板
显示角色装备和属性
"""
import os
import pygame
from game.ui.ui_panel import UIPanel
from game.core.resource_manager import get_game_asset_path

class EquipmentPanel(UIPanel):
    """装备面板类"""
    
    def __init__(self, screen, player, position, size):
        """
        初始化装备面板
        
        Args:
            screen: 屏幕对象
            player: 玩家对象
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
        """
        super().__init__(screen, position, size)
        
        # 玩家引用
        self.player = player
        
        # 设置面板标题
        self.title = "装备面板"
        
        # 设置面板背景色（较深的颜色，适合突显装备图标）
        self.background_color = (20, 20, 20)
        
        # 角色性别（默认为男性，现在不可更改）
        self.character_gender = "male"
        
        # 加载角色图片
        self.male_character_image = None
        self.female_character_image = None
        # 不再尝试加载外部图片，直接使用占位符
        
        # 装备格子大小 - 使用固定大小而不是基于面板大小的百分比
        self.slot_size = 40  # 固定40像素的槽位大小
        
        # 装备槽间距 - 使用固定间距
        slot_spacing_h = 10  # 水平间距
        slot_spacing_v = 10  # 垂直间距
        
        # 角色显示区域 - 去掉小人图像，只保留一个简单的中心区域标识
        character_width = 50
        character_height = 50
        self.character_rect = pygame.Rect(
            self.rect.centerx - character_width // 2,
            self.rect.centery - character_height // 2,
            character_width,
            character_height
        )
        
        # 装备槽位置定义 - 重新调整分布，更加分散和合理
        center_x = self.rect.centerx
        center_y = self.rect.centery
        
        self.equipment_slots = {
            # 顶部装备槽（武器，副手，圣物）- 分散一些
            "weapon": {"position": (center_x - 60, center_y - 100), "equipped": None, "display_name": "武器"},
            "offhand": {"position": (center_x, center_y - 100), "equipped": None, "display_name": "副手"},
            "artifact": {"position": (center_x + 60, center_y - 100), "equipped": None, "display_name": "圣物"},
            
            # 左侧装备槽（从上到下：头盔，胸甲，护腿，靴子）- 距离中心更远
            "hat": {"position": (center_x - 120, center_y - 60), "equipped": None, "display_name": "头盔"},
            "armor": {"position": (center_x - 120, center_y - 20), "equipped": None, "display_name": "胸甲"},
            "leggings": {"position": (center_x - 120, center_y + 20), "equipped": None, "display_name": "护腿"},
            "shoes": {"position": (center_x - 120, center_y + 60), "equipped": None, "display_name": "靴子"},
            
            # 右侧装备槽（从上到下：护符，戒指1，戒指2，手镯）- 距离中心更远
            "amulet": {"position": (center_x + 120, center_y - 60), "equipped": None, "display_name": "护符"},
            "ring1": {"position": (center_x + 120, center_y - 20), "equipped": None, "display_name": "戒指1"},
            "ring2": {"position": (center_x + 120, center_y + 20), "equipped": None, "display_name": "戒指2"},
            "bracelet": {"position": (center_x + 120, center_y + 60), "equipped": None, "display_name": "手镯"},
            
            # 底部装备槽（特殊装备）- 分散一些
            "special1": {"position": (center_x - 60, center_y + 100), "equipped": None, "display_name": "特殊1"},
            "special2": {"position": (center_x, center_y + 100), "equipped": None, "display_name": "特殊2"},
            "special3": {"position": (center_x + 60, center_y + 100), "equipped": None, "display_name": "特殊3"},
        }
        
        # 属性面板显示标志
        self.show_attributes = False
        
        # 添加属性按钮 - 使用固定尺寸
        button_width = 60  # 固定按钮宽度
        button_height = 25  # 固定按钮高度
        self.add_button(
            "属性",
            (10, self.rect.height - button_height - 10, button_width, button_height),
            self.toggle_attributes
        )
        
        # 添加关闭按钮 - 调整位置和样式使其更明显
        self.add_button(
            "关闭",
            (self.rect.width - button_width - 10, 10, button_width, button_height),
            self.close_panel
        )
        
        # 添加装备槽位按钮
        self.create_equipment_slot_buttons()
        
        # 角色轮廓颜色
        self.male_outline_color = (80, 80, 120)
        self.female_outline_color = (150, 80, 100)
        
        # 属性面板设置
        self.attributes_panel_rect = None
        
        # 悬停提示相关属性
        self.hovered_slot = None  # 当前悬停的装备槽位
        self.tooltip_visible = False  # 提示框是否可见
        self.tooltip_data = None  # 提示框数据
        self.hover_timer = 0  # 悬停计时器
        self.hover_delay = 500  # 悬停延迟时间（毫秒）
        self.tooltip_font = pygame.font.SysFont("SimHei", 14)  # 提示框字体
        self.tooltip_title_font = pygame.font.SysFont("SimHei", 16, bold=True)  # 提示框标题字体
        
        # 提示框样式配置
        self.tooltip_config = {
            'background_color': (30, 30, 40),
            'border_color': (100, 100, 120),
            'title_color': (255, 215, 0),  # 金色标题
            'normal_text_color': (220, 220, 220),  # 普通文本
            'stat_color': (120, 200, 120),  # 属性值颜色（绿色）
            'requirement_color': (200, 120, 120),  # 需求颜色（红色）
            'padding': 10,
            'line_spacing': 20,
            'max_width': 280,
            'border_width': 2
        }
    
    def close_panel(self):
        """关闭面板"""
        self.visible = False
        self.show_attributes = False
        # 隐藏悬停提示
        self.tooltip_visible = False
        self.hovered_slot = None
        print("关闭了装备面板")
    
    def create_equipment_slot_buttons(self):
        """创建装备槽位按钮"""
        for slot_name, slot_data in self.equipment_slots.items():
            # 计算按钮位置（相对于面板）
            button_pos = (
                slot_data["position"][0] - self.rect.left,
                slot_data["position"][1] - self.rect.top
            )
            
            # 添加按钮
            self.add_button(
                "", 
                (button_pos[0], button_pos[1], self.slot_size, self.slot_size),
                lambda slot=slot_name: self.on_equipment_slot_click(slot)
            )
    
    def on_equipment_slot_click(self, slot_name):
        """
        处理装备槽位点击事件
        
        Args:
            slot_name: 槽位名称
        """
        print(f"点击了装备槽位: {slot_name}")
        # TODO: 显示装备详情或打开装备选择界面
    
    def toggle_attributes(self):
        """切换角色属性面板显示状态"""
        self.show_attributes = not self.show_attributes
        print(f"{'显示' if self.show_attributes else '隐藏'}角色属性面板")
    
    def close_attributes_panel(self):
        """关闭属性面板"""
        self.show_attributes = False
        print("关闭了属性面板")
    
    def _generate_equipment_icon_path(self, item):
        """根据装备信息生成图片路径"""
        if not item:
            return None
        
        item_name = item.get('name', '')
        slot = item.get('slot', '')
        
        # 槽位到目录的映射
        slot_to_directory = {
            '武器': '武器',
            '头盔': '头盔', 
            '胸甲': '防具',
            '护腿': '防具',
            '靴子': '防具',
            '护符': '项链',
            '戒指': '戒指',
            '手镯': '手镯'
        }
        
        directory = slot_to_directory.get(slot)
        if directory and item_name:
            # 生成图片路径: 目录/装备名称.png
            icon_path = f"{directory}/{item_name}.png"
            return icon_path
        
        return None
    
    def update_hover_state(self, mouse_pos, delta_time):
        """
        更新悬停状态
        
        Args:
            mouse_pos: 鼠标位置
            delta_time: 时间增量（毫秒）
        """
        if not self.visible:
            self.tooltip_visible = False
            self.hovered_slot = None
            return
        
        # 检查鼠标是否悬停在装备槽位上
        current_hover_slot = None
        for slot_name, slot_data in self.equipment_slots.items():
            slot_rect = pygame.Rect(
                slot_data["position"][0],
                slot_data["position"][1],
                self.slot_size,
                self.slot_size
            )
            
            if slot_rect.collidepoint(mouse_pos):
                current_hover_slot = slot_name
                break
        
        # 如果悬停的槽位发生变化
        if current_hover_slot != self.hovered_slot:
            self.hovered_slot = current_hover_slot
            self.hover_timer = 0
            self.tooltip_visible = False
            
            # 如果有新的悬停槽位且有装备，准备显示提示
            if self.hovered_slot and self.equipment_slots[self.hovered_slot]["equipped"]:
                self.tooltip_data = self.equipment_slots[self.hovered_slot]["equipped"]
        
        # 如果鼠标悬停在有装备的槽位上
        if self.hovered_slot and self.equipment_slots[self.hovered_slot]["equipped"]:
            self.hover_timer += delta_time
            
            # 如果悬停时间足够长，显示提示框
            if self.hover_timer >= self.hover_delay:
                self.tooltip_visible = True
        else:
            # 没有悬停或槽位没有装备，隐藏提示框
            self.tooltip_visible = False
    
    def create_equipment_tooltip_data(self, equipment):
        """
        创建装备提示框数据
        
        Args:
            equipment: 装备数据
            
        Returns:
            list: 提示框行数据列表
        """
        if not equipment:
            return []
        
        lines = []
        
        # 装备名称（标题）
        name = equipment.get('name', '未知装备')
        lines.append({
            'text': name,
            'color': self.tooltip_config['title_color'],
            'font': self.tooltip_title_font
        })
        
        # 装备品质和类型
        rarity = equipment.get('rarity', '普通')
        slot = equipment.get('slot', '未知')
        
        # 品质英文到中文的翻译映射
        rarity_translation = {
            'common': '普通',
            'uncommon': '精良', 
            'rare': '稀有',
            'epic': '史诗',
            'legendary': '传说',
            'normal': '普通',
            'fine': '精良',
            'magic': '魔法',
            'unique': '史诗'
        }
        
        # 如果是英文品质名，翻译成中文
        if rarity.lower() in rarity_translation:
            rarity = rarity_translation[rarity.lower()]
        
        rarity_text = f"{rarity} {slot}"
        lines.append({
            'text': rarity_text,
            'color': self._get_rarity_color(rarity),
            'font': self.tooltip_font
        })
        
        # 空行分隔
        lines.append({'text': '', 'color': (0, 0, 0), 'font': self.tooltip_font})
        
        # 装备属性
        stats = equipment.get('stats') or equipment.get('attributes', {})
        if stats:
            lines.append({
                'text': '装备属性:',
                'color': self.tooltip_config['normal_text_color'],
                'font': self.tooltip_font
            })
            
            for stat_name, value in stats.items():
                if value != 0:
                    if stat_name in ['攻击下限', '攻击上限']:
                        continue  # 攻击范围单独处理
                    elif stat_name in ['防御下限', '防御上限']:
                        continue  # 防御范围单独处理
                    elif stat_name in ['魔法攻击下限', '魔法攻击上限']:
                        continue  # 魔法攻击范围单独处理
                    elif stat_name in ['道术攻击下限', '道术攻击上限']:
                        continue  # 道术攻击范围单独处理
                    else:
                        # 其他单一属性 - 直接显示中文属性名，不再进行翻译
                        # 如果stat_name已经是中文就直接使用，如果是英文则翻译成中文
                        if any('\u4e00' <= char <= '\u9fff' for char in stat_name):
                            # 包含中文字符，直接使用
                            display_name = stat_name
                        else:
                            # 英文属性名，翻译成中文
                            from game.utils.attribute_translator import translate_attr_to_chinese
                            display_name = translate_attr_to_chinese(stat_name)
                        
                        # 格式化显示值
                        if isinstance(value, (int, float)):
                            if '率' in display_name or 'rate' in stat_name.lower():
                                # 百分比属性
                                if isinstance(value, float) and value < 1:
                                    stat_text = f"  {display_name}: +{value * 100:.1f}%"
                                else:
                                    stat_text = f"  {display_name}: +{value:.1f}%"
                            elif '速度' in display_name or 'speed' in stat_name.lower():
                                # 速度属性，保留一位小数
                                stat_text = f"  {display_name}: +{value:.1f}"
                            else:
                                # 普通数值属性
                                if isinstance(value, float) and value.is_integer():
                                    stat_text = f"  {display_name}: +{int(value)}"
                                else:
                                    stat_text = f"  {display_name}: +{value}"
                        else:
                            # 其他类型，直接显示
                            stat_text = f"  {display_name}: {value}"
                        
                        lines.append({
                            'text': stat_text,
                            'color': self.tooltip_config['stat_color'],
                            'font': self.tooltip_font
                        })
            
            # 处理范围属性
            if stats.get('攻击下限', 0) > 0 or stats.get('攻击上限', 0) > 0:
                attack_text = f"  攻击力: +{stats.get('攻击下限', 0)}-{stats.get('攻击上限', 0)}"
                lines.append({
                    'text': attack_text,
                    'color': self.tooltip_config['stat_color'],
                    'font': self.tooltip_font
                })
            
            if stats.get('防御下限', 0) > 0 or stats.get('防御上限', 0) > 0:
                defense_text = f"  防御力: +{stats.get('防御下限', 0)}-{stats.get('防御上限', 0)}"
                lines.append({
                    'text': defense_text,
                    'color': self.tooltip_config['stat_color'],
                    'font': self.tooltip_font
                })
            
            if stats.get('魔法攻击下限', 0) > 0 or stats.get('魔法攻击上限', 0) > 0:
                magic_attack_text = f"  魔法攻击: +{stats.get('魔法攻击下限', 0)}-{stats.get('魔法攻击上限', 0)}"
                lines.append({
                    'text': magic_attack_text,
                    'color': self.tooltip_config['stat_color'],
                    'font': self.tooltip_font
                })
            
            if stats.get('道术攻击下限', 0) > 0 or stats.get('道术攻击上限', 0) > 0:
                tao_attack_text = f"  道术攻击: +{stats.get('道术攻击下限', 0)}-{stats.get('道术攻击上限', 0)}"
                lines.append({
                    'text': tao_attack_text,
                    'color': self.tooltip_config['stat_color'],
                    'font': self.tooltip_font
                })
        
        # 装备需求
        required_level = equipment.get('required_level', 1)
        required_class = equipment.get('required_class', [])
        
        if required_level > 1 or required_class:
            # 空行分隔
            lines.append({'text': '', 'color': (0, 0, 0), 'font': self.tooltip_font})
            lines.append({
                'text': '装备需求:',
                'color': self.tooltip_config['normal_text_color'],
                'font': self.tooltip_font
            })
            
            if required_level > 1:
                level_text = f"  等级: {required_level}"
                # 检查玩家是否满足等级需求
                level_color = self.tooltip_config['stat_color'] if self.player.level >= required_level else self.tooltip_config['requirement_color']
                lines.append({
                    'text': level_text,
                    'color': level_color,
                    'font': self.tooltip_font
                })
            
            if required_class:
                if isinstance(required_class, list):
                    class_text = f"  职业: {'/'.join(required_class)}"
                else:
                    class_text = f"  职业: {required_class}"
                
                # 检查玩家是否满足职业需求
                player_meets_class_req = (
                    not required_class or 
                    self.player.character_class in required_class if isinstance(required_class, list) 
                    else self.player.character_class == required_class
                )
                class_color = self.tooltip_config['stat_color'] if player_meets_class_req else self.tooltip_config['requirement_color']
                lines.append({
                    'text': class_text,
                    'color': class_color,
                    'font': self.tooltip_font
                })
        
        # 装备描述（如果有）
        description = equipment.get('description', '')
        if description:
            lines.append({'text': '', 'color': (0, 0, 0), 'font': self.tooltip_font})
            lines.append({
                'text': description,
                'color': (180, 180, 180),
                'font': self.tooltip_font
            })
        
        return lines
    
    def _get_rarity_color(self, rarity):
        """根据品质获取颜色"""
        rarity_colors = {
            '普通': (200, 200, 200),
            '精良': (120, 200, 120),
            '稀有': (120, 120, 200),
            '史诗': (200, 120, 200),
            '传说': (255, 165, 0)
        }
        return rarity_colors.get(rarity, (200, 200, 200))
    
    def render_equipment_tooltip(self, mouse_pos):
        """
        渲染装备提示框
        
        Args:
            mouse_pos: 鼠标位置
        """
        if not self.tooltip_visible or not self.tooltip_data:
            return
        
        # 创建提示框数据
        tooltip_lines = self.create_equipment_tooltip_data(self.tooltip_data)
        if not tooltip_lines:
            return
        
        # 计算提示框尺寸
        max_width = 0
        total_height = self.tooltip_config['padding'] * 2
        
        for line in tooltip_lines:
            if line['text']:  # 非空行
                text_surface = line['font'].render(line['text'], True, line['color'])
                max_width = max(max_width, text_surface.get_width())
                total_height += self.tooltip_config['line_spacing']
            else:  # 空行
                total_height += self.tooltip_config['line_spacing'] // 2
        
        tooltip_width = min(max_width + self.tooltip_config['padding'] * 2, self.tooltip_config['max_width'])
        tooltip_height = total_height
        
        # 智能定位提示框位置
        tooltip_x = mouse_pos[0] + 15  # 鼠标右侧偏移
        tooltip_y = mouse_pos[1] - 10  # 鼠标上方偏移
        
        # 边界检查和调整
        screen_width = self.screen.get_width()
        screen_height = self.screen.get_height()
        
        # 右边界检查
        if tooltip_x + tooltip_width > screen_width:
            tooltip_x = mouse_pos[0] - tooltip_width - 15  # 移到鼠标左侧
        
        # 下边界检查
        if tooltip_y + tooltip_height > screen_height:
            tooltip_y = screen_height - tooltip_height - 10
        
        # 上边界检查
        if tooltip_y < 0:
            tooltip_y = 10
        
        # 左边界检查
        if tooltip_x < 0:
            tooltip_x = 10
        
        # 创建提示框矩形
        tooltip_rect = pygame.Rect(tooltip_x, tooltip_y, tooltip_width, tooltip_height)
        
        # 绘制提示框背景
        pygame.draw.rect(self.screen, self.tooltip_config['background_color'], tooltip_rect)
        pygame.draw.rect(self.screen, self.tooltip_config['border_color'], tooltip_rect, self.tooltip_config['border_width'])
        
        # 绘制提示框内容
        current_y = tooltip_y + self.tooltip_config['padding']
        
        for line in tooltip_lines:
            if line['text']:  # 非空行
                text_surface = line['font'].render(line['text'], True, line['color'])
                text_x = tooltip_x + self.tooltip_config['padding']
                self.screen.blit(text_surface, (text_x, current_y))
                current_y += self.tooltip_config['line_spacing']
            else:  # 空行
                current_y += self.tooltip_config['line_spacing'] // 2
    
    def render(self):
        """渲染装备面板"""
        # 如果面板不可见，则不渲染任何内容
        if not self.visible:
            return
            
        # 调用父类的渲染方法（绘制背景和标题）
        super().render()
        
        # 渲染角色图片
        self.render_character()
        
        # 渲染装备槽位
        self.render_equipment_slots()
        
        # 渲染装备属性
        if not self.show_attributes:
            self.render_equipment_stats()
        else:
            self.render_character_attributes()
            
            # 检查是否点击了属性面板上的关闭按钮
            mouse_pos = pygame.mouse.get_pos()
            if pygame.mouse.get_pressed()[0] and self.attributes_panel_close_rect and self.attributes_panel_close_rect.collidepoint(mouse_pos):
                self.close_attributes_panel()
        
        # 渲染装备悬停提示框（最后渲染，确保在最上层显示）
        mouse_pos = pygame.mouse.get_pos()
        self.render_equipment_tooltip(mouse_pos)
    
    def render_character(self):
        """渲染角色区域 - 简化版本，只显示一个中心方框"""
        # 绘制一个简单的中心方框，作为装备槽位的参考点
        pygame.draw.rect(self.screen, (40, 40, 50), self.character_rect)
        pygame.draw.rect(self.screen, (100, 100, 120), self.character_rect, 2)
        
        # 在方框中显示角色名称和等级信息
        name_text = f"{self.player.name}"
        name_surface = self.small_font.render(name_text, True, (200, 200, 200))
        name_rect = name_surface.get_rect(center=(self.character_rect.centerx, self.character_rect.centery - 10))
        self.screen.blit(name_surface, name_rect)
        
        # 显示等级
        level_text = f"Lv.{self.player.level}"
        level_surface = self.small_font.render(level_text, True, (180, 180, 220))
        level_rect = level_surface.get_rect(center=(self.character_rect.centerx, self.character_rect.centery + 10))
        self.screen.blit(level_surface, level_rect)
    
    def render_equipment_slots(self):
        """渲染装备槽位"""
        for slot_name, slot_data in self.equipment_slots.items():
            # 创建槽位矩形
            slot_rect = pygame.Rect(
                slot_data["position"][0],
                slot_data["position"][1],
                self.slot_size,
                self.slot_size
            )
            
            # 绘制槽位背景
            pygame.draw.rect(self.screen, (40, 40, 40), slot_rect)
            
            # 绘制槽位边框
            pygame.draw.rect(self.screen, (100, 100, 100), slot_rect, 2)
            
            # 如果有装备，则绘制装备信息
            if slot_data["equipped"]:
                equipped_item = slot_data["equipped"]
                
                # 绘制装备背景（有装备时使用不同颜色）
                pygame.draw.rect(self.screen, (60, 80, 60), slot_rect)
                pygame.draw.rect(self.screen, (120, 180, 120), slot_rect, 2)
                
                # 尝试加载并显示装备图片
                icon_path = equipped_item.get('icon_path')
                item_name = equipped_item.get('name', '未知装备')
                
                # 如果没有icon_path，根据装备名称和槽位生成路径
                if not icon_path:
                    icon_path = self._generate_equipment_icon_path(equipped_item)
                
                if icon_path:
                    try:
                        # 构建完整的图片路径 - 使用资源管理器
                        full_icon_path = get_game_asset_path(f"images/equipment/{icon_path}")
                        if os.path.exists(full_icon_path):
                            # 加载装备图片
                            equipment_image = pygame.image.load(full_icon_path).convert_alpha()
                            # 缩放图片以适应槽位大小
                            scaled_image = pygame.transform.scale(equipment_image, (self.slot_size - 4, self.slot_size - 4))
                            # 居中显示图片
                            image_rect = scaled_image.get_rect(center=slot_rect.center)
                            self.screen.blit(scaled_image, image_rect)
                        else:
                            # 如果图片不存在，显示装备名称
                            display_name = item_name[:6] if len(item_name) > 6 else item_name
                            text = self.small_font.render(display_name, True, (220, 255, 220))
                            text_rect = text.get_rect(center=slot_rect.center)
                            self.screen.blit(text, text_rect)
                    except Exception as e:
                        # 如果加载图片失败，显示装备名称
                        print(f"加载装备图片失败: {icon_path}, 错误: {e}")
                        display_name = item_name[:6] if len(item_name) > 6 else item_name
                        text = self.small_font.render(display_name, True, (220, 255, 220))
                        text_rect = text.get_rect(center=slot_rect.center)
                        self.screen.blit(text, text_rect)
                else:
                    # 如果没有图片路径，显示装备名称
                    display_name = item_name[:6] if len(item_name) > 6 else item_name
                    text = self.small_font.render(display_name, True, (220, 255, 220))
                    text_rect = text.get_rect(center=slot_rect.center)
                    self.screen.blit(text, text_rect)
                
                # 在装备槽位右下角显示装备等级（如果有）
                if 'level' in equipped_item and equipped_item['level'] > 0:
                    level_text = f"+{equipped_item['level']}"
                    level_surface = pygame.font.Font(None, 16).render(level_text, True, (255, 215, 0))
                    level_pos = (slot_rect.right - 15, slot_rect.bottom - 12)
                    self.screen.blit(level_surface, level_pos)
            else:
                # 绘制槽位名称作为占位符
                text = self.small_font.render(slot_data["display_name"][:4], True, (150, 150, 150))
                text_rect = text.get_rect(center=slot_rect.center)
                self.screen.blit(text, text_rect)
    
    def render_equipment_stats(self):
        """渲染装备属性 - 简化版本，适用于小面板"""
        if self.show_attributes:
            return  # 如果显示详细属性面板，则不显示简化版
            
        # 固定位置显示装备统计
        stats_x = self.rect.left + 10
        stats_y = self.rect.top + 30  # 在顶部显示
        line_height = 16  # 固定行高
        
        # 获取实际的装备属性数据
        equipment_stats = self.player.equipment_manager.get_equipment_stats()
        
        # 显示装备总属性 - 只显示主要属性
        stats = []
        
        # 攻击属性
        attack_min = equipment_stats.get('攻击下限', 0)
        attack_max = equipment_stats.get('攻击上限', 0)
        if attack_min > 0 or attack_max > 0:
            stats.append(f"攻击: +{attack_min}-{attack_max}")
        
        # 防御力
        def_min = equipment_stats.get('防御下限', 0)
        def_max = equipment_stats.get('防御上限', 0)
        if def_min > 0 or def_max > 0:
            stats.append(f"防御: +{def_min}-{def_max}")
        
        # 魔法攻击
        magic_min = equipment_stats.get('魔法攻击下限', 0)
        magic_max = equipment_stats.get('魔法攻击上限', 0)
        if magic_min > 0 or magic_max > 0:
            stats.append(f"魔攻: +{magic_min}-{magic_max}")
        
        # 如果没有任何装备加成，显示提示
        if not stats:
            stats = ["无装备加成"]
        
        # 限制显示的属性数量，避免占用太多空间
        stats = stats[:3]  # 最多显示3个属性
        
        # 计算背景大小
        bg_width = 120  # 固定宽度
        bg_height = len(stats) * line_height + 10  # 固定高度
        
        # 绘制半透明背景
        stats_bg_rect = pygame.Rect(stats_x, stats_y, bg_width, bg_height)
        stats_bg_surface = pygame.Surface((bg_width, bg_height))
        stats_bg_surface.set_alpha(150)
        stats_bg_surface.fill((20, 20, 30))
        self.screen.blit(stats_bg_surface, (stats_x, stats_y))
        
        # 绘制边框
        pygame.draw.rect(self.screen, (80, 80, 100), stats_bg_rect, 1)
        
        # 绘制属性
        for i, stat in enumerate(stats):
            color = (200, 200, 200)
            text = self.small_font.render(stat, True, color)
            self.screen.blit(text, (stats_x + 5, stats_y + 5 + i * line_height))
    
    def render_character_attributes(self):
        """渲染角色属性"""
        # 属性面板位置和大小 - 调整为更大的居中显示
        panel_width = int(self.rect.width * 0.85)
        panel_height = int(self.rect.height * 0.85)
        panel_x = self.rect.centerx - panel_width // 2
        panel_y = self.rect.centery - panel_height // 2
        
        # 创建属性面板背景
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        self.attributes_panel_rect = panel_rect
        
        # 绘制背景渐变效果
        pygame.draw.rect(self.screen, (25, 25, 35), panel_rect)
        pygame.draw.rect(self.screen, (35, 35, 45), panel_rect, 3)
        pygame.draw.rect(self.screen, (120, 120, 140), panel_rect, 2)
        
        # 绘制标题背景条
        title_bg_rect = pygame.Rect(panel_x, panel_y, panel_width, 40)
        pygame.draw.rect(self.screen, (45, 45, 65), title_bg_rect)
        pygame.draw.rect(self.screen, (80, 80, 100), title_bg_rect, 1)
        
        # 绘制标题
        title_text = "角色属性详情"
        title_surface = self.normal_font.render(title_text, True, (220, 220, 170))
        title_rect = title_surface.get_rect(center=(panel_rect.centerx, panel_y + 20))
        self.screen.blit(title_surface, title_rect)
        
        # 添加属性面板的关闭按钮 - 改进设计
        close_button_size = 30
        close_button_x = panel_rect.right - close_button_size - 8
        close_button_y = panel_rect.top + 5
        close_button_rect = pygame.Rect(close_button_x, close_button_y, close_button_size, close_button_size)
        self.attributes_panel_close_rect = close_button_rect
        
        # 绘制关闭按钮背景
        pygame.draw.rect(self.screen, (80, 50, 50), close_button_rect)
        pygame.draw.rect(self.screen, (150, 80, 80), close_button_rect, 2)
        
        # 绘制X符号 - 更清晰的设计
        center_x = close_button_x + close_button_size // 2
        center_y = close_button_y + close_button_size // 2
        offset = 8
        pygame.draw.line(self.screen, (220, 220, 220), 
                         (center_x - offset, center_y - offset),
                         (center_x + offset, center_y + offset), 3)
        pygame.draw.line(self.screen, (220, 220, 220), 
                         (center_x - offset, center_y + offset),
                         (center_x + offset, center_y - offset), 3)
        
        # 调整布局参数
        column_width = (panel_width - 60) // 3  # 减去边距
        line_height = max(18, int(panel_height * 0.035))  # 最小行高18px
        
        # 三列布局位置
        column1_x = panel_x + 20
        column2_x = panel_x + 20 + column_width + 10
        column3_x = panel_x + 20 + 2 * (column_width + 10)
        start_y = panel_y + 55  # 给标题更多空间
        
        # 基础信息（第一列）
        basic_info = [
            f"名称: {self.player.name}",
            f"等级: {self.player.level}",
            f"VIP等级: {self.player.vip_level}",
            f"经验值: {self.player.exp}/{self.player.max_exp}",
            f"VIP经验: {self.player.vip_exp}"
        ]
        
        # 获取基础属性和装备属性
        base_stats = self.player.stats.get_all_stats()
        equipment_stats = self.player.equipment_manager.get_equipment_stats()
        
        # 计算生命值和魔法值的加成
        base_hp = base_stats.get('生命值', 0)
        eq_hp = equipment_stats.get('生命值', 0)
        total_hp = base_hp + eq_hp
        
        base_mp = base_stats.get('魔法值', 0)
        eq_mp = equipment_stats.get('魔法值', 0)
        total_mp = base_mp + eq_mp
        
        # 战斗资源（第二列）- 显示装备加成
        combat_resources = []
        if eq_hp > 0:
            combat_resources.append(f"生命值: {base_hp}(+{eq_hp}) = {total_hp}")
        else:
            combat_resources.append(f"生命值: {total_hp}")
            
        if eq_mp > 0:
            combat_resources.append(f"魔法值: {base_mp}(+{eq_mp}) = {total_mp}")
        else:
            combat_resources.append(f"魔法值: {total_mp}")
        
        # 绘制分组标题 - 基础信息
        self._draw_section_title("基础信息", column1_x, start_y - 25, column_width)
        
        # 绘制基础信息（第一列）
        for i, attr in enumerate(basic_info):
            text = self.small_font.render(attr, True, (200, 200, 200))
            self.screen.blit(text, (column1_x + 5, start_y + i * line_height))
        
        # 绘制分组标题 - 战斗资源
        self._draw_section_title("战斗资源", column2_x, start_y - 25, column_width)
        
        # 绘制战斗资源（第二列上部）
        for i, attr in enumerate(combat_resources):
            text = self.small_font.render(attr, True, (200, 200, 200))
            self.screen.blit(text, (column2_x + 5, start_y + i * line_height))
        
        # 攻击属性（第二列下部）- 显示基础+装备属性
        
        # 计算攻击力
        base_attack_min = base_stats.get('攻击下限', 0)
        base_attack_max = base_stats.get('攻击上限', 0)
        eq_attack_min = equipment_stats.get('攻击下限', 0)
        eq_attack_max = equipment_stats.get('攻击上限', 0)
        total_attack_min = base_attack_min + eq_attack_min
        total_attack_max = base_attack_max + eq_attack_max
        
        # 计算魔法攻击力
        base_magic_min = base_stats.get('魔法攻击下限', 0)
        base_magic_max = base_stats.get('魔法攻击上限', 0)
        eq_magic_min = equipment_stats.get('魔法攻击下限', 0)
        eq_magic_max = equipment_stats.get('魔法攻击上限', 0)
        total_magic_min = base_magic_min + eq_magic_min
        total_magic_max = base_magic_max + eq_magic_max
        
        # 计算道术攻击力
        base_tao_min = base_stats.get('道术攻击下限', 0)
        base_tao_max = base_stats.get('道术攻击上限', 0)
        eq_tao_min = equipment_stats.get('道术攻击下限', 0)
        eq_tao_max = equipment_stats.get('道术攻击上限', 0)
        total_tao_min = base_tao_min + eq_tao_min
        total_tao_max = base_tao_max + eq_tao_max
        
        attack_attributes = []
        
        # 物理攻击显示 - 总是显示
        if eq_attack_min > 0 or eq_attack_max > 0:
            attack_attributes.append(f"物理攻击: {base_attack_min}-{base_attack_max}(+{eq_attack_min}-{eq_attack_max}) = {total_attack_min}-{total_attack_max}")
        else:
            attack_attributes.append(f"物理攻击: {total_attack_min}-{total_attack_max}")
        
        # 魔法攻击显示 - 总是显示
        if eq_magic_min > 0 or eq_magic_max > 0:
            attack_attributes.append(f"魔法攻击: {base_magic_min}-{base_magic_max}(+{eq_magic_min}-{eq_magic_max}) = {total_magic_min}-{total_magic_max}")
        else:
            attack_attributes.append(f"魔法攻击: {total_magic_min}-{total_magic_max}")
        
        # 道术攻击显示 - 总是显示
        if eq_tao_min > 0 or eq_tao_max > 0:
            attack_attributes.append(f"道术攻击: {base_tao_min}-{base_tao_max}(+{eq_tao_min}-{eq_tao_max}) = {total_tao_min}-{total_tao_max}")
        else:
            attack_attributes.append(f"道术攻击: {total_tao_min}-{total_tao_max}")
        
        # 攻击速度显示
        base_attack_speed = self.player.stats.attack_speed
        eq_attack_speed = equipment_stats.get('攻速', 0)
        total_attack_speed = base_attack_speed + eq_attack_speed
        if eq_attack_speed > 0:
            attack_attributes.append(f"攻击速度: {base_attack_speed:.1f}(+{eq_attack_speed:.1f}) = {total_attack_speed:.1f}")
        else:
            attack_attributes.append(f"攻击速度: {total_attack_speed:.1f}")
        
        # 暴击率显示
        base_crit = self.player.stats.critical_rate
        eq_crit = equipment_stats.get('暴击率', 0)
        total_crit = base_crit + eq_crit
        if eq_crit > 0:
            attack_attributes.append(f"暴击率: {base_crit*100:.1f}%(+{eq_crit*100:.1f}%) = {total_crit*100:.1f}%")
        else:
            attack_attributes.append(f"暴击率: {total_crit*100:.1f}%")
        
        # 绘制分组标题 - 攻击属性
        attack_title_y = start_y + (len(combat_resources) + 1) * line_height
        self._draw_section_title("攻击属性", column2_x, attack_title_y, column_width)
        
        # 绘制攻击属性
        for i, attr in enumerate(attack_attributes):
            text = self.small_font.render(attr, True, (200, 200, 200))
            self.screen.blit(text, (column2_x + 5, attack_title_y + 25 + i * line_height))
        
        # 防御属性（第三列上部）- 显示基础+装备属性
        # 计算物理防御
        base_def_min = base_stats.get('防御下限', 0)
        base_def_max = base_stats.get('防御上限', 0)
        eq_def_min = equipment_stats.get('防御下限', 0)
        eq_def_max = equipment_stats.get('防御上限', 0)
        total_def_min = base_def_min + eq_def_min
        total_def_max = base_def_max + eq_def_max
        
        # 计算魔法防御
        base_magic_def = base_stats.get('魔抗', 0)
        eq_magic_def = equipment_stats.get('魔抗', 0)
        total_magic_def = base_magic_def + eq_magic_def
        
        defense_attributes = []
        
        # 物理防御显示 - 总是显示
        if eq_def_min > 0 or eq_def_max > 0:
            defense_attributes.append(f"物理防御: {base_def_min}-{base_def_max}(+{eq_def_min}-{eq_def_max}) = {total_def_min}-{total_def_max}")
        else:
            defense_attributes.append(f"物理防御: {total_def_min}-{total_def_max}")
        
        # 魔法防御显示 - 总是显示
        if eq_magic_def > 0:
            defense_attributes.append(f"魔法防御: {base_magic_def}(+{eq_magic_def}) = {total_magic_def}")
        else:
            defense_attributes.append(f"魔法防御: {total_magic_def}")
        
        # 准确度显示 - 总是显示
        base_accuracy = base_stats.get('准确', 0)
        eq_accuracy = equipment_stats.get('准确', 0)
        total_accuracy = base_accuracy + eq_accuracy
        if eq_accuracy > 0:
            defense_attributes.append(f"准确度: {base_accuracy}(+{eq_accuracy}) = {total_accuracy}")
        else:
            defense_attributes.append(f"准确度: {total_accuracy}")
            
        # 敏捷显示 - 总是显示
        base_agility = base_stats.get('敏捷', 0)
        eq_agility = equipment_stats.get('敏捷', 0)
        total_agility = base_agility + eq_agility
        if eq_agility > 0:
            defense_attributes.append(f"敏捷: {base_agility}(+{eq_agility}) = {total_agility}")
        else:
            defense_attributes.append(f"敏捷: {total_agility}")
        
        # 幸运值显示 - 总是显示
        base_luck = getattr(self.player, 'luck', 0)
        eq_luck = equipment_stats.get('幸运', 0)
        total_luck = base_luck + eq_luck
        if eq_luck > 0:
            defense_attributes.append(f"幸运: {base_luck}(+{eq_luck}) = {total_luck}")
        else:
            defense_attributes.append(f"幸运: {total_luck}")
        
        # 绘制分组标题 - 防御属性
        self._draw_section_title("防御属性", column3_x, start_y - 25, column_width)
        
        # 绘制防御属性
        for i, attr in enumerate(defense_attributes):
            text = self.small_font.render(attr, True, (200, 200, 200))
            self.screen.blit(text, (column3_x + 5, start_y + i * line_height))
        
        # 额外属性（第三列下部）
        bonus_attributes = [
            f"经验加成: {self.player.get_attribute_display('exp_bonus')}",
            f"金币加成: {self.player.get_attribute_display('gold_bonus')}",
            f"装备掉落率: {self.player.get_attribute_display('drop_rate_bonus')}"
        ]
        
        # 绘制分组标题 - 额外属性
        bonus_title_y = start_y + (len(defense_attributes) + 1) * line_height
        self._draw_section_title("额外属性", column3_x, bonus_title_y, column_width)
        
        # 绘制额外属性
        for i, attr in enumerate(bonus_attributes):
            text = self.small_font.render(attr, True, (180, 180, 220))
            self.screen.blit(text, (column3_x + 5, bonus_title_y + 25 + i * line_height))
        
        # 绘制装备总评（底部居中）
        bottom_section_y = panel_y + panel_height - int(panel_height * 0.12)
        
        # 绘制装饰性分隔线
        line_y = bottom_section_y - 10
        pygame.draw.line(self.screen, (80, 80, 100), 
                         (panel_rect.left + 50, line_y), 
                         (panel_rect.right - 50, line_y), 2)
        
        # 计算实际的装备评分
        equipment_score = self._calculate_equipment_score()
        
        # 绘制装备总评背景
        total_bg_rect = pygame.Rect(panel_rect.centerx - 120, bottom_section_y, 240, 35)
        pygame.draw.rect(self.screen, (40, 40, 60), total_bg_rect)
        pygame.draw.rect(self.screen, (100, 100, 140), total_bg_rect, 1)
        
        # 绘制装备评分
        rating_text = f"装备评分: {equipment_score}"
        rating_surface = self.normal_font.render(rating_text, True, (220, 200, 120))
        rating_rect = rating_surface.get_rect(center=(panel_rect.centerx, bottom_section_y + 17))
        self.screen.blit(rating_surface, rating_rect)
    
    def _draw_section_title(self, title, x, y, width):
        """绘制分组标题"""
        # 绘制标题背景
        title_bg_rect = pygame.Rect(x, y, width, 20)
        pygame.draw.rect(self.screen, (50, 50, 70), title_bg_rect)
        pygame.draw.rect(self.screen, (120, 120, 150), title_bg_rect, 1)
        
        # 绘制标题文字
        title_surface = self.small_font.render(title, True, (200, 200, 140))
        title_rect = title_surface.get_rect(center=(x + width // 2, y + 10))
        self.screen.blit(title_surface, title_rect)
    
    def _calculate_equipment_score(self):
        """计算装备评分"""
        try:
            equipment_stats = self.player.equipment_manager.get_equipment_stats()
            score = 0
            
            # 攻击力评分
            attack_min = equipment_stats.get('攻击下限', 0)
            attack_max = equipment_stats.get('攻击上限', 0)
            score += (attack_min + attack_max) * 10
            
            # 魔法攻击评分
            magic_min = equipment_stats.get('魔法攻击下限', 0)
            magic_max = equipment_stats.get('魔法攻击上限', 0)
            score += (magic_min + magic_max) * 10
            
            # 道术攻击评分
            tao_min = equipment_stats.get('道术攻击下限', 0)
            tao_max = equipment_stats.get('道术攻击上限', 0)
            score += (tao_min + tao_max) * 10
            
            # 防御力评分
            def_min = equipment_stats.get('防御下限', 0)
            def_max = equipment_stats.get('防御上限', 0)
            score += (def_min + def_max) * 8
            
            # 生命值和魔法值评分
            hp_bonus = equipment_stats.get('生命值', 0)
            mp_bonus = equipment_stats.get('魔法值', 0)
            score += hp_bonus * 2 + mp_bonus * 2
            
            # 其他属性评分
            score += equipment_stats.get('准确', 0) * 15
            score += equipment_stats.get('敏捷', 0) * 12
            
            return int(score)
        except:
            return 0
    
    def handle_event(self, event):
        """处理事件 - 修复版，完全消费鼠标事件防止穿透"""
        if not self.visible:
            return False
        
        # 处理鼠标点击事件
        if event.type == pygame.MOUSEBUTTONDOWN:
            # 先检查是否点击在面板区域内
            if self.rect.collidepoint(event.pos):
                # 在面板内部的点击
                if event.button == 1:  # 左键点击
                    # 如果属性面板打开，检查是否点击了关闭按钮
                    if self.show_attributes and self.attributes_panel_close_rect:
                        if self.attributes_panel_close_rect.collidepoint(event.pos):
                            self.close_attributes_panel()
                            return True
                    
                    # 调用父类的事件处理（处理按钮点击等）
                    if super().handle_event(event):
                        return True
                
                # 在面板内的任何点击都应该被消费，防止穿透
                return True
            else:
                # 点击在面板外部，不处理但也不关闭面板（由UIManager处理）
                return False
        
        # 其他事件类型，调用父类处理
        return super().handle_event(event)
    
    def update(self):
        """更新装备面板状态"""
        # 更新装备信息 - 从玩家装备管理器获取最新装备数据
        if hasattr(self.player, 'equipment_manager'):
            equipped_items = self.player.equipment_manager.get_all_equipped_items()
            
            # 装备面板槽位名称到装备管理器槽位名称的映射
            slot_mapping = {
                "weapon": "武器",
                "hat": "头盔", 
                "armor": "胸甲",
                "leggings": "护腿",
                "shoes": "靴子",
                "amulet": "护符",
                "ring1": "戒指",
                "ring2": "戒指",  # 第二个戒指槽位也映射到戒指
                "bracelet": "手镯",  # 手镯映射
                # 其他槽位暂时不映射，保持原有逻辑
            }
            
            # 重置所有槽位
            for slot_name in self.equipment_slots.keys():
                self.equipment_slots[slot_name]["equipped"] = None
            
            # 更新每个装备槽位的装备信息
            for ui_slot_name in self.equipment_slots.keys():
                # 获取对应的装备管理器槽位名称
                manager_slot_name = slot_mapping.get(ui_slot_name)
                if manager_slot_name:
                    # 获取该槽位的装备
                    equipped_item = equipped_items.get(manager_slot_name)
                    # 更新槽位数据
                    self.equipment_slots[ui_slot_name]["equipped"] = equipped_item
                
            # 只在第一次检测到装备时打印调试信息，避免刷屏
            equipped_count = sum(1 for item in equipped_items.values() if item is not None)
            if equipped_count > 0:
                # 只打印一次，不重复打印
                if not hasattr(self, '_last_equipped_count') or self._last_equipped_count != equipped_count:
                    print(f"装备面板更新: 已装备 {equipped_count} 件装备")
                    self._last_equipped_count = equipped_count