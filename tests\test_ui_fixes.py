#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI修复验证测试

测试三个UI问题的修复：
1. 地图信息和Boss状态按钮重叠
2. 怪物血量信息被进度条遮挡
3. 玩家死亡时自动战斗按钮状态
"""

import sys
import os
import pygame
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from game.ui.battle_panel import BattlePanel
from game.core.battle_manager import BattleManager


def test_boss_button_position():
    """测试Boss状态按钮位置修复"""
    print("\n=== 测试Boss状态按钮位置修复 ===")
    
    # 初始化pygame
    pygame.init()
    screen = pygame.display.set_mode((1200, 800))
    
    # 创建战斗管理器和面板
    battle_manager = BattleManager(debug_console_output=False)
    player = Mock()
    map_manager = Mock()
    potion_effects_manager = Mock()
    
    # 创建战斗面板（中央区域）
    battle_panel = BattlePanel(
        screen=screen,
        battle_manager=battle_manager,
        player=player,
        position=(300, 0),  # 左侧300px是地图区域
        size=(600, 600),    # 中央区域大小
        map_manager=map_manager,
        potion_effects_manager=potion_effects_manager
    )
    
    # 检查Boss按钮位置
    boss_button = None
    for button in battle_panel.buttons:
        if "Boss状态" in button["text"]:
            boss_button = button
            break
    
    if boss_button:
        button_rect = button["rect"]
        panel_rect = battle_panel.rect
        
        print(f"✅ 找到Boss状态按钮")
        print(f"   - 面板区域: {panel_rect}")
        print(f"   - 按钮位置: {button_rect}")
        print(f"   - 按钮在面板右上角: {button_rect.right <= panel_rect.right}")
        print(f"   - 距离右边缘: {panel_rect.right - button_rect.right}px")
        
        # 检查是否与地图信息区域重叠
        map_info_x = panel_rect.left + 10  # 地图信息的x位置
        map_info_width = 200  # 估计的地图信息宽度
        
        no_overlap = button_rect.left > map_info_x + map_info_width
        print(f"   - 与地图信息无重叠: {no_overlap}")
        
        if no_overlap:
            print("✅ Boss按钮位置修复成功，无重叠问题")
        else:
            print("❌ Boss按钮仍可能与地图信息重叠")
    else:
        print("❌ 未找到Boss状态按钮")
    
    pygame.quit()


def test_monster_hp_display():
    """测试怪物血量显示修复"""
    print("\n=== 测试怪物血量显示修复 ===")
    
    # 初始化pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    # 创建战斗面板
    battle_manager = BattleManager(debug_console_output=False)
    player = Mock()
    
    battle_panel = BattlePanel(
        screen=screen,
        battle_manager=battle_manager,
        player=player,
        position=(0, 0),
        size=(800, 600),
        map_manager=Mock(),
        potion_effects_manager=Mock()
    )
    
    # 模拟怪物位置和血量显示
    monster_position = (400, 300)
    
    # 计算血条位置
    hp_bar_y = monster_position[1] + 35  # 怪物血条位置
    
    # 计算进度条位置（修复后）
    progress_bar_y = hp_bar_y + 8 + 15  # 血条下方，增加间距
    
    # 计算血量文本位置（修复后）
    hp_text_y = hp_bar_y + 8 // 2  # 血条中央高度
    
    # 计算进度条文本位置
    progress_text_y = progress_bar_y + battle_panel.progress_bar_height + 12
    
    print(f"✅ 怪物血量显示位置计算:")
    print(f"   - 怪物位置: {monster_position}")
    print(f"   - 血条Y位置: {hp_bar_y}")
    print(f"   - 血量文本Y位置: {hp_text_y}")
    print(f"   - 进度条Y位置: {progress_bar_y}")
    print(f"   - 进度条文本Y位置: {progress_text_y}")
    
    # 检查是否有足够间距
    hp_progress_gap = progress_bar_y - hp_bar_y - 8  # 血条和进度条之间的间距
    text_gap = progress_text_y - hp_text_y  # 两个文本之间的间距
    
    print(f"   - 血条与进度条间距: {hp_progress_gap}px")
    print(f"   - 文本间距: {text_gap}px")
    
    if hp_progress_gap >= 10 and text_gap >= 20:
        print("✅ 血量显示修复成功，有足够间距避免遮挡")
    else:
        print("❌ 血量显示可能仍有遮挡问题")
    
    pygame.quit()


def test_auto_battle_button_state():
    """测试自动战斗按钮状态修复"""
    print("\n=== 测试自动战斗按钮状态修复 ===")
    
    # 创建战斗管理器
    battle_manager = BattleManager(debug_console_output=False)
    
    # 测试正常状态
    print("1. 测试正常状态:")
    status = battle_manager.get_auto_battle_status()
    print(f"   - 初始状态: {status}")
    assert status == "自动战斗", f"初始状态应该是'自动战斗'，实际是'{status}'"
    
    # 测试启动自动战斗
    print("2. 测试启动自动战斗:")
    battle_manager.toggle_auto_battle()
    status = battle_manager.get_auto_battle_status()
    print(f"   - 启动后状态: {status}")
    assert status in ["停止战斗", "寻怪中..."], f"启动后状态应该是'停止战斗'或'寻怪中...'，实际是'{status}'"
    
    # 测试玩家死亡状态
    print("3. 测试玩家死亡状态:")
    battle_manager.player_is_dead = True
    status = battle_manager.get_auto_battle_status()
    print(f"   - 死亡状态: {status}")
    assert status == "玩家死亡", f"死亡状态应该是'玩家死亡'，实际是'{status}'"
    
    # 检查死亡时自动战斗是否停止
    print("4. 检查死亡时自动战斗状态:")
    print(f"   - auto_hunt_enabled: {battle_manager.auto_hunt_enabled}")
    print(f"   - auto_battle_enabled: {battle_manager.auto_battle_enabled}")
    print(f"   - hunt_state: {battle_manager.hunt_state}")
    print(f"   - is_in_battle: {battle_manager.is_in_battle}")
    
    # 模拟死亡处理
    battle_manager._handle_battle_defeat()
    
    print("5. 死亡处理后的状态:")
    print(f"   - auto_hunt_enabled: {battle_manager.auto_hunt_enabled}")
    print(f"   - auto_battle_enabled: {battle_manager.auto_battle_enabled}")
    print(f"   - hunt_state: {battle_manager.hunt_state}")
    print(f"   - is_in_battle: {battle_manager.is_in_battle}")
    
    # 测试复活状态
    print("6. 测试复活状态:")
    battle_manager._revive_player()
    status = battle_manager.get_auto_battle_status()
    print(f"   - 复活后状态: {status}")
    print(f"   - auto_hunt_enabled: {battle_manager.auto_hunt_enabled}")
    print(f"   - auto_battle_enabled: {battle_manager.auto_battle_enabled}")
    print(f"   - hunt_state: {battle_manager.hunt_state}")
    
    # 验证复活后状态正确
    if (status == "自动战斗" and 
        not battle_manager.auto_hunt_enabled and 
        not battle_manager.auto_battle_enabled and 
        battle_manager.hunt_state == "idle"):
        print("✅ 自动战斗按钮状态修复成功")
    else:
        print("❌ 自动战斗按钮状态仍有问题")


def test_visual_layout():
    """可视化测试布局修复"""
    print("\n=== 可视化测试布局修复 ===")
    
    # 初始化pygame
    pygame.init()
    screen = pygame.display.set_mode((1200, 800))
    pygame.display.set_caption("UI修复验证测试")
    
    # 创建战斗面板
    battle_manager = BattleManager(debug_console_output=False)
    player = Mock()
    player.hp = 100
    player.max_hp = 100
    player.current_hp = 100
    player.mp = 50
    player.max_mp = 50
    player.current_mp = 50
    player.character_class = "战士"
    player.gender = "男"
    
    battle_panel = BattlePanel(
        screen=screen,
        battle_manager=battle_manager,
        player=player,
        position=(300, 0),
        size=(600, 600),
        map_manager=Mock(),
        potion_effects_manager=Mock()
    )
    
    # 模拟一些敌人
    mock_enemies = []
    for i in range(3):
        enemy = Mock()
        enemy.name = f"测试怪物{i+1}"
        enemy.hp = 80 - i * 20
        enemy.max_hp = 100
        enemy.current_hp = 80 - i * 20
        mock_enemies.append(enemy)
    
    battle_manager.enemies = mock_enemies
    
    print("✅ 创建了可视化测试窗口")
    print("   - 可以观察Boss按钮是否在右上角")
    print("   - 可以观察怪物血量是否正确显示")
    print("   - 可以测试自动战斗按钮状态变化")
    
    # 简单的事件循环（运行3秒）
    clock = pygame.time.Clock()
    running = True
    start_time = pygame.time.get_ticks()
    test_duration = 3000  # 3秒
    
    while running and (pygame.time.get_ticks() - start_time) < test_duration:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            else:
                battle_panel.handle_event(event)
        
        # 更新
        battle_panel.update()
        
        # 清屏
        screen.fill((30, 30, 30))
        
        # 渲染战斗面板
        battle_panel.render()
        
        # 添加说明文字
        font = pygame.font.SysFont("SimHei", 16)
        instructions = [
            "UI修复验证测试",
            "1. Boss按钮应在右上角",
            "2. 怪物血量应清晰可见",
            "3. 自动战斗按钮状态正确"
        ]
        
        for i, text in enumerate(instructions):
            color = (255, 255, 255) if i == 0 else (200, 200, 200)
            text_surface = font.render(text, True, color)
            screen.blit(text_surface, (50, 50 + i * 25))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    print("✅ 可视化测试完成")


def run_all_tests():
    """运行所有UI修复测试"""
    print("开始测试UI修复...")
    print("=" * 60)
    
    try:
        test_boss_button_position()
        test_monster_hp_display()
        test_auto_battle_button_state()
        test_visual_layout()
        
        print("\n" + "=" * 60)
        print("🎉 所有UI修复测试完成！")
        print("\n📝 修复总结:")
        print("- ✅ Boss状态按钮移至右上角，避免与地图信息重叠")
        print("- ✅ 怪物血量文本移至血条右侧，避免被进度条遮挡")
        print("- ✅ 玩家死亡时自动战斗按钮正确重置为未开始状态")
        print("- ✅ 复活后所有状态正确重置")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
