import bisect  # 用于在排序列表中高效查找

# 1. 定义属性名称
STAT_NAMES = [
    "生命值", "魔法值",
    "攻击下限", "攻击上限",
    "防御下限", "防御上限",
    "魔法攻击下限", "魔法攻击上限",
    "道术攻击下限", "道术攻击上限",
    "魔抗", "攻速"
]

# 原始数据 (保持不变)
WARRIOR_STATS_RAW = {
    1: [19, 15, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1.0], 5: [44, 18, 1, 2, 0, 0, 0, 0, 0, 0, 0, 1.0],
    10: [89, 46, 1, 2, 0, 1, 0, 0, 0, 0, 0, 1.0], 15: [149, 63, 2, 3, 0, 2, 0, 0, 0, 0, 0, 1.0],
    20: [224, 81, 3, 4, 0, 4, 0, 0, 0, 0, 0, 1.0], 25: [314, 99, 4, 5, 0, 5, 0, 0, 0, 0, 0, 1.0],
    30: [419, 116, 5, 6, 0, 6, 0, 0, 0, 0, 0, 1.0], 35: [539, 133, 6, 7, 0, 7, 0, 0, 0, 0, 0, 1.0],
    40: [674, 151, 7, 8, 0, 8, 0, 0, 0, 0, 0, 1.0], 45: [824, 169, 8, 9, 0, 9, 0, 0, 0, 0, 0, 1.0],
    50: [984, 187, 9, 10, 0, 10, 0, 0, 0, 0, 0, 1.0], 55: [1159, 205, 10, 11, 0, 11, 0, 0, 0, 0, 0, 1.0],
    60: [1334, 223, 11, 12, 1, 12, 0, 0, 0, 0, 0, 1.0], 65: [1519, 241, 12, 13, 1, 13, 0, 0, 0, 0, 0, 1.0],
    70: [1704, 259, 13, 14, 1, 14, 0, 0, 0, 0, 0, 1.0], 75: [1899, 277, 14, 15, 1, 15, 0, 0, 0, 0, 0, 1.0],
    80: [2094, 295, 15, 16, 2, 16, 0, 0, 0, 0, 0, 1.0], 85: [2299, 313, 16, 17, 2, 17, 0, 0, 0, 0, 0, 1.0],
    90: [2494, 331, 17, 18, 2, 18, 0, 0, 0, 0, 0, 1.0], 95: [2699, 349, 18, 19, 2, 19, 0, 0, 0, 0, 0, 1.0],
    100: [2894, 367, 19, 20, 3, 20, 0, 0, 0, 0, 0, 1.0],
}
MAGE_STATS_RAW = {
    1: [16, 18, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0.8], 5: [25, 46, 0, 0, 0, 0, 1, 2, 0, 0, 0, 0.8],
    10: [39, 71, 0, 0, 0, 0, 1, 3, 0, 0, 0, 0.8], 15: [53, 178, 0, 0, 0, 0, 2, 5, 0, 0, 0, 0.8],
    20: [77, 277, 0, 0, 0, 0, 3, 7, 0, 0, 0, 0.8], 25: [101, 391, 0, 0, 0, 0, 4, 9, 0, 0, 0, 0.8],
    30: [128, 545, 0, 0, 0, 0, 5, 11, 0, 0, 0, 0.8], 35: [159, 706, 0, 0, 0, 0, 6, 13, 0, 0, 0, 0.8],
    40: [193, 894, 0, 0, 0, 0, 7, 15, 0, 0, 0, 0.8], 45: [239, 1108, 0, 0, 0, 0, 8, 17, 0, 0, 0, 0.8],
    50: [276, 1362, 0, 0, 0, 0, 9, 19, 0, 0, 0, 0.8], 55: [315, 1516, 0, 0, 0, 0, 10, 21, 0, 0, 0, 0.8],
    60: [362, 1870, 0, 0, 0, 0, 11, 23, 0, 0, 0, 0.8], 65: [387, 1624, 0, 0, 0, 0, 12, 25, 0, 0, 0, 0.8], # 修正65级法师HP
    70: [414, 1778, 0, 0, 0, 0, 13, 27, 0, 0, 0, 0.8], 75: [441, 1932, 0, 0, 0, 0, 14, 29, 0, 0, 0, 0.8],
    80: [468, 2086, 0, 0, 0, 0, 15, 31, 0, 0, 0, 0.8], 85: [495, 2240, 0, 0, 0, 0, 16, 33, 0, 0, 0, 0.8],
    90: [522, 2394, 0, 0, 0, 0, 17, 35, 0, 0, 0, 0.8], 95: [549, 2548, 0, 0, 0, 0, 18, 37, 0, 0, 0, 0.8],
    100: [576, 2702, 0, 0, 0, 0, 19, 39, 0, 0, 0, 0.8],
}
TAOIST_STATS_RAW = {
    1: [17, 13, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0.9], 5: [31, 20, 0, 0, 0, 0, 0, 0, 1, 2, 0, 0.9],
    10: [56, 41, 0, 0, 0, 0, 0, 0, 2, 4, 0, 0.9], 15: [89, 75, 0, 0, 0, 1, 0, 0, 3, 6, 0, 0.9],
    20: [131, 123, 0, 0, 0, 2, 0, 0, 4, 8, 0, 0.9], 25: [181, 185, 0, 0, 0, 3, 0, 0, 5, 10, 0, 0.9],
    30: [239, 261, 0, 0, 0, 4, 0, 0, 6, 12, 0, 0.9], 35: [306, 350, 0, 0, 0, 5, 0, 0, 7, 14, 0, 0.9],
    40: [381, 453, 0, 0, 0, 6, 0, 0, 8, 16, 0, 0.9], 45: [464, 570, 0, 0, 0, 7, 0, 0, 9, 18, 0, 0.9],
    50: [553, 700, 0, 0, 0, 8, 0, 0, 10, 20, 0, 0.9], 55: [656, 845, 0, 0, 0, 9, 0, 0, 11, 22, 0, 0.9],
    60: [764, 1000, 0, 0, 0, 10, 0, 0, 12, 24, 0, 0.9], 65: [865, 1150, 0, 0, 0, 11, 0, 0, 13, 26, 0, 0.9],
    70: [973, 1325, 0, 0, 0, 12, 0, 0, 14, 28, 0, 0.9], 75: [1080, 1480, 0, 0, 0, 13, 0, 0, 15, 30, 0, 0.9],
    80: [1200, 1600, 0, 0, 0, 14, 0, 0, 15, 32, 0, 0.9], 85: [1320, 1740, 0, 0, 0, 15, 0, 0, 17, 34, 0, 0.9],
    90: [1440, 1825, 0, 0, 0, 16, 0, 0, 18, 36, 0, 0.9], 95: [1560, 1980, 0, 0, 0, 17, 0, 0, 19, 38, 0, 0.9],
    100: [1680, 2100, 0, 0, 0, 18, 0, 0, 20, 40, 0, 0.9],
}

# 2. 统一数据结构
ALL_CLASSES_RAW_STATS = {
    "战士": WARRIOR_STATS_RAW,
    "法师": MAGE_STATS_RAW,
    "道士": TAOIST_STATS_RAW,
}

# 3. 数据预处理
_processed_stats_cache = {}  # 用于缓存处理后的数据

def _preprocess_raw_data_if_needed():
    """如果尚未处理，则将原始列表型属性数据转换为字典型属性数据。"""
    if _processed_stats_cache:  # 如果缓存中已有数据，则表示已处理
        return

    for class_name, raw_stats_dict in ALL_CLASSES_RAW_STATS.items():
        _processed_stats_cache[class_name] = {}
        # 确保等级是排序的，bisect 才能正常工作
        sorted_levels = sorted(raw_stats_dict.keys())
        _processed_stats_cache[class_name]['_sorted_levels'] = sorted_levels  # 存储排序后的等级列表

        for level in sorted_levels:
            stat_values = raw_stats_dict[level]
            _processed_stats_cache[class_name][level] = {
                STAT_NAMES[i]: stat_values[i] for i in range(len(STAT_NAMES))
            }

# 程序启动时执行一次预处理
_preprocess_raw_data_if_needed()


# 4. 创建插值函数
def get_character_stats(class_name: str, target_level: int) -> dict:
    """
    根据职业名称和目标等级计算角色属性。
    如果目标等级未精确定义，则进行线性插值。
    
    参数:
        class_name: 职业名称 ("战士", "法师", "道士")
        target_level: 目标等级 (1-100)
        
    返回:
        包含所有属性的字典
    """
    if class_name not in _processed_stats_cache:
        raise ValueError(f"未知的职业: {class_name}")

    class_data = _processed_stats_cache[class_name]
    defined_levels = class_data['_sorted_levels']  # 获取预存的排序等级列表

    if not defined_levels:
        raise ValueError(f"职业 {class_name} 没有定义属性数据。")

    # 处理边界情况：目标等级低于或高于已定义的最小/最大等级
    if target_level <= defined_levels[0]:
        # 返回一个副本，防止外部修改原始缓存数据
        return class_data[defined_levels[0]].copy()
    if target_level >= defined_levels[-1]:
        return class_data[defined_levels[-1]].copy()

    # 使用 bisect_left 找到 target_level 在 defined_levels 中的插入点
    # idx 使得 all(defined_levels[i] < target_level for i in range(idx))
    # 和 all(defined_levels[i] >= target_level for i in range(idx, len(defined_levels)))
    idx = bisect.bisect_left(defined_levels, target_level)

    # 如果目标等级恰好是一个已定义的等级
    if defined_levels[idx] == target_level:
        return class_data[target_level].copy()

    # 否则，在 defined_levels[idx-1] 和 defined_levels[idx] 之间进行插值
    upper_level_key = defined_levels[idx]
    lower_level_key = defined_levels[idx - 1]

    stats_lower = class_data[lower_level_key]
    stats_upper = class_data[upper_level_key]

    interpolated_stats = {}
    level_range = float(upper_level_key - lower_level_key)  # 等级差
    # 目标等级在两个已知等级间的进度 (0.0 到 1.0)
    progress = (target_level - lower_level_key) / level_range

    for stat_name in STAT_NAMES:
        val_lower = stats_lower[stat_name]
        val_upper = stats_upper[stat_name]

        # 线性插值公式: val_lower + (val_upper - val_lower) * progress
        interp_val = val_lower + (val_upper - val_lower) * progress

        # 根据属性类型处理：攻速是浮点数，其他通常是整数
        if stat_name == "攻速":
            interpolated_stats[stat_name] = round(interp_val, 3)  # 攻速保留3位小数
        else:
            interpolated_stats[stat_name] = int(round(interp_val))  # 其他属性四舍五入为整数

    return interpolated_stats

# 5. 职业特性和技能
CLASS_FEATURES = {
    "战士": {
        "描述": "力量型职业，拥有高生命值和物理攻击力，适合近战。",
        "技能": ["基本剑法", "刺杀剑法", "半月弯刀", "烈火剑法", "野蛮冲撞"],
        "推荐属性": "力量 > 体力 > 敏捷",
        "武器类型": ["剑", "刀"],
        "主要属性": "力量"
    },
    "法师": {
        "描述": "智力型职业，拥有高魔法攻击力和魔法值，适合远程攻击。",
        "技能": ["火球术", "雷电术", "冰箭术", "地狱火", "魔法盾"],
        "推荐属性": "智力 > 魔力 > 体力",
        "武器类型": ["法杖", "魔棒"],
        "主要属性": "智力"
    },
    "道士": {
        "描述": "辅助型职业，拥有治疗和召唤能力，适合团队作战。",
        "技能": ["治愈术", "召唤骷髅", "幽灵盾", "施毒术", "召唤神兽"],
        "推荐属性": "道术 > 体力 > 智力",
        "武器类型": ["符", "扇"],
        "主要属性": "道术"
    }
}

# 6. 获取职业基本信息
def get_class_info(class_name: str) -> dict:
    """
    获取职业的基本信息
    
    参数:
        class_name: 职业名称
        
    返回:
        包含职业信息的字典
    """
    if class_name not in CLASS_FEATURES:
        raise ValueError(f"未知的职业: {class_name}")
    
    return CLASS_FEATURES[class_name].copy()

# 7. 获取所有可用职业
def get_available_classes() -> list:
    """
    获取所有可用的职业名称
    
    返回:
        职业名称列表
    """
    return list(CLASS_FEATURES.keys())

"""
角色属性系统
定义了游戏中角色的各种属性
"""

class CharacterStats:
    """
    角色属性类，定义了角色的各种基础属性和战斗属性
    """
    def __init__(self):
        # 基础属性
        self.level = 1         # 等级
        self.max_hp = 100      # 最大生命值
        self.max_mp = 100      # 最大魔法值
        
        # 攻击属性
        self.attack_min = 10   # 最小物理攻击力
        self.attack_max = 20   # 最大物理攻击力
        self.magic_min = 5     # 最小魔法攻击力
        self.magic_max = 15    # 最大魔法攻击力
        self.tao_min = 8       # 最小道术攻击力
        self.tao_max = 18      # 最大道术攻击力
        
        # 防御属性
        self.defense_min = 5   # 最小防御力
        self.defense_max = 5   # 最大防御力(如果是固定值，则与最小值相同)
        self.magic_defense = 5 # 魔法防御力
        self.percent_defense = 0.0  # 百分比防御，如0.1表示10%
        
        # 命中与闪避相关
        self.accuracy = 10     # 准确值
        self.agility = 10      # 敏捷值
        
        # 幸运与诅咒
        self.luck = 0          # 幸运值(0-9)
        self.curse = 0         # 诅咒值(0-9)
        
        # 战斗属性
        self.attack_speed = 1.0  # 攻击速度
        self.move_speed = 1.0    # 移动速度
        self.critical_rate = 0.05  # 暴击率
        self.critical_damage = 1.5  # 暴击伤害倍率
        
        # 属性加成(来自装备、技能等)
        self.hp_bonus = 0
        self.mp_bonus = 0
        self.attack_bonus = 0
        self.defense_bonus = 0
        self.accuracy_bonus = 0
        self.agility_bonus = 0
        self.luck_bonus = 0
        self.curse_bonus = 0
    
    def get_max_hp(self):
        """获取最大生命值(包含加成)"""
        return self.max_hp + self.hp_bonus
    
    def get_max_mp(self):
        """获取最大魔法值(包含加成)"""
        return self.max_mp + self.mp_bonus
    
    def get_attack_range(self):
        """获取攻击范围(包含加成)"""
        return (self.attack_min + self.attack_bonus, self.attack_max + self.attack_bonus)
    
    def get_magic_range(self):
        """获取魔法攻击范围(包含加成)"""
        return (self.magic_min + self.attack_bonus, self.magic_max + self.attack_bonus)
    
    def get_tao_range(self):
        """获取道术攻击范围(包含加成)"""
        return (self.tao_min + self.attack_bonus, self.tao_max + self.attack_bonus)
    
    def get_defense_range(self):
        """获取防御范围(包含加成)"""
        return (self.defense_min + self.defense_bonus, self.defense_max + self.defense_bonus)
    
    def get_accuracy(self):
        """获取准确值(包含加成)"""
        return self.accuracy + self.accuracy_bonus
    
    def get_agility(self):
        """获取敏捷值(包含加成)"""
        return self.agility + self.agility_bonus
    
    def get_total_luck(self):
        """获取总幸运值(包含加成)"""
        return min(9, max(0, self.luck + self.luck_bonus))
    
    def get_total_curse(self):
        """获取总诅咒值(包含加成)"""
        return min(9, max(0, self.curse + self.curse_bonus))
    
    def to_dict(self):
        """将属性转换为字典格式"""
        return {
            "level": self.level,
            "max_hp": self.max_hp,
            "max_mp": self.max_mp,
            "attack_min": self.attack_min,
            "attack_max": self.attack_max,
            "magic_min": self.magic_min,
            "magic_max": self.magic_max,
            "tao_min": self.tao_min,
            "tao_max": self.tao_max,
            "defense_min": self.defense_min,
            "defense_max": self.defense_max,
            "magic_defense": self.magic_defense,
            "percent_defense": self.percent_defense,
            "accuracy": self.accuracy,
            "agility": self.agility,
            "luck": self.luck,
            "curse": self.curse,
            "attack_speed": self.attack_speed,
            "move_speed": self.move_speed,
            "critical_rate": self.critical_rate,
            "critical_damage": self.critical_damage
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建属性对象"""
        stats = cls()
        for key, value in data.items():
            if hasattr(stats, key):
                setattr(stats, key, value)
        return stats 