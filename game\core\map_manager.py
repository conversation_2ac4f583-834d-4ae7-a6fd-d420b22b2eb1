import json
import os
import random
from typing import Dict, List, Tuple, Optional, Any

from game.models.enemy import enemy_factory
from game.core.resource_manager import get_game_data_path


class Map:
    """
    地图类，表示游戏中的一个地图区域
    """
    def __init__(self, name, data):
        """
        初始化地图
        
        参数:
            name: 地图名称
            data: 地图数据
        """
        self.name = name
        self.difficulty = data.get("difficulty", 1)
        self.level_required = data.get("level_required", 1)
        self.description = data.get("description", "")
        self.monsters = data.get("monsters", [])
        self.is_dungeon = data.get("is_dungeon", False)
        self.dungeon_type = data.get("dungeon_type", "")
        self.time_limit = data.get("time_limit", 0)
        self.equipment_drop_rate_multiplier = data.get("equipment_drop_rate_multiplier", 1.0)
        
        # 地图解锁条件 - 新增功能
        self.unlock_requirements = data.get("unlock_requirements", {"monsters_needed": 0, "bosses_needed": 0})
        
        # 当前地图中的怪物实例
        self.active_enemies = []
        
        # 地图尺寸（简化处理，使用固定尺寸）
        self.width = 50
        self.height = 50
        
        # 地图格子，用于存储地图元素
        self.grid = [[None for _ in range(self.height)] for _ in range(self.width)]
        
        # 玩家出生点（默认在地图中心）
        self.spawn_point = (self.width // 2, self.height // 2)
    
    def generate_enemies(self, count=10, boss_spawn_enabled=True):
        """
        在地图上生成怪物 - 改进版，确保怪物分散分布避免扎堆

        参数:
            count: 生成的怪物数量
            boss_spawn_enabled: 是否允许生成Boss

        返回:
            生成的怪物列表
        """
        self.active_enemies = []
        # 用于记录已占用的位置，确保怪物分散
        occupied_positions = set()
        min_distance_normal = 3  # 普通怪物之间最小距离
        min_distance_boss = 8    # BOSS与其他怪物的最小距离
        
        def is_position_valid(pos_x, pos_y, min_dist, occupied_pos):
            """检查位置是否有效（不与其他怪物太近）"""
            # 防止生成在玩家出生点附近
            if abs(pos_x - self.spawn_point[0]) < 5 and abs(pos_y - self.spawn_point[1]) < 5:
                return False
            
            for occupied_x, occupied_y in occupied_pos:
                distance = ((pos_x - occupied_x) ** 2 + (pos_y - occupied_y) ** 2) ** 0.5
                if distance < min_dist:
                    return False
            return True
        
        def find_scattered_position(min_dist, occupied_pos, is_boss=False):
            """寻找分散的位置"""
            max_attempts = 100
            
            for attempt in range(max_attempts):
                if is_boss:
                    # BOSS尽量远离玩家出生点
                    spawn_x, spawn_y = self.spawn_point
                    # 在地图的4个角落区域随机选择
                    corner_regions = [
                        (5, self.width//3, 5, self.height//3),           # 左上
                        (self.width*2//3, self.width-5, 5, self.height//3),  # 右上
                        (5, self.width//3, self.height*2//3, self.height-5),  # 左下
                        (self.width*2//3, self.width-5, self.height*2//3, self.height-5)  # 右下
                    ]
                    x_min, x_max, y_min, y_max = random.choice(corner_regions)
                    pos_x = random.randint(x_min, x_max)
                    pos_y = random.randint(y_min, y_max)
                else:
                    # 普通怪物使用网格化分布
                    if attempt < 70:
                        grid_size = 8
                        grid_x = random.randint(0, (self.width - 1) // grid_size)
                        grid_y = random.randint(0, (self.height - 1) // grid_size)
                        # 在网格内随机偏移，避免太规整
                        pos_x = grid_x * grid_size + random.randint(1, grid_size - 1)
                        pos_y = grid_y * grid_size + random.randint(1, grid_size - 1)
                    else:
                        # 备用随机策略
                        pos_x = random.randint(3, self.width - 3)
                        pos_y = random.randint(3, self.height - 3)
                
                # 确保位置在地图范围内
                pos_x = max(0, min(pos_x, self.width - 1))
                pos_y = max(0, min(pos_y, self.height - 1))
                
                # 检查位置是否有效
                if is_position_valid(pos_x, pos_y, min_dist, occupied_pos):
                    return pos_x, pos_y
            
            # 降级处理
            print(f"警告: 生成怪物时找不到合适的分散位置")
            return random.randint(5, self.width-5), random.randint(5, self.height-5)
        
        # 创建普通怪物
        normal_count = max(1, count - 1)
        if normal_count > 0:
            for i in range(normal_count):
                # 使用分散位置算法
                pos_x, pos_y = find_scattered_position(min_distance_normal, occupied_positions, is_boss=False)
                
                # 记录占用位置
                occupied_positions.add((pos_x, pos_y))
                
                # 创建地图怪物
                enemy = enemy_factory.create_enemies_for_map(self.__dict__, 1, (pos_x, pos_y))
                if enemy:
                    self.active_enemies.extend(enemy)
                    self.grid[pos_x][pos_y] = {"type": "enemy", "enemy": enemy[0]}
        
        # 创建BOSS（如果允许生成Boss且满足条件）
        if boss_spawn_enabled and (self.is_dungeon or random.random() < 0.1):
            # 使用分散位置算法，BOSS有更大的最小距离
            pos_x, pos_y = find_scattered_position(min_distance_boss, occupied_positions, is_boss=True)

            # 记录占用位置
            occupied_positions.add((pos_x, pos_y))

            # 创建BOSS
            boss = enemy_factory.create_boss_for_map(self.__dict__, (pos_x, pos_y))
            if boss:
                self.active_enemies.append(boss)
                self.grid[pos_x][pos_y] = {"type": "enemy", "enemy": boss}
                print(f"✅ 生成了Boss: {getattr(boss, 'name', '未知Boss')}")
        elif not boss_spawn_enabled:
            print("ℹ️ Boss生成已被禁用")
        
        print(f"地图怪物生成完成: {len(self.active_enemies)} 个怪物分散分布")
        print(f"分布范围: {len(occupied_positions)} 个不同位置，最小间距: {min_distance_normal} 格")
        
        return self.active_enemies

    def is_unlocked(self, player):
        """检查地图是否已解锁"""
        if not hasattr(player, 'kill_statistics'):
            return True  # 如果玩家没有击杀统计，默认解锁

        monsters_needed = self.unlock_requirements.get('monsters_needed', 0)
        bosses_needed = self.unlock_requirements.get('bosses_needed', 0)
        prerequisite_map = self.unlock_requirements.get('prerequisite_map', None)

        # 如果不需要击杀任何怪物且没有前置地图，则默认解锁
        if monsters_needed == 0 and bosses_needed == 0 and prerequisite_map is None:
            return True

        # 如果有前置地图要求，检查前置地图的击杀统计
        if prerequisite_map:
            map_kills = player.kill_statistics.get('map_kills', {})
            prerequisite_stats = map_kills.get(prerequisite_map, {'monsters': 0, 'bosses': 0})

            prerequisite_monsters = prerequisite_stats.get('monsters', 0)
            prerequisite_bosses = prerequisite_stats.get('bosses', 0)

            # 检查是否在前置地图达到了击杀要求
            return prerequisite_monsters >= monsters_needed and prerequisite_bosses >= bosses_needed

        # 如果没有前置地图，检查总击杀统计（向后兼容）
        total_monsters = player.kill_statistics.get('total_monsters', 0)
        total_bosses = player.kill_statistics.get('total_bosses', 0)

        return total_monsters >= monsters_needed and total_bosses >= bosses_needed

    def get_unlock_progress(self, player):
        """获取地图解锁进度"""
        monsters_needed = self.unlock_requirements.get('monsters_needed', 0)
        bosses_needed = self.unlock_requirements.get('bosses_needed', 0)
        prerequisite_map = self.unlock_requirements.get('prerequisite_map', None)

        if not hasattr(player, 'kill_statistics'):
            return {
                'monsters_current': 0,
                'monsters_needed': monsters_needed,
                'bosses_current': 0,
                'bosses_needed': bosses_needed,
                'progress_text': '需要击杀统计数据',
                'prerequisite_map': prerequisite_map
            }

        # 如果有前置地图，获取前置地图的击杀统计
        if prerequisite_map:
            map_kills = player.kill_statistics.get('map_kills', {})
            prerequisite_stats = map_kills.get(prerequisite_map, {'monsters': 0, 'bosses': 0})

            current_monsters = prerequisite_stats.get('monsters', 0)
            current_bosses = prerequisite_stats.get('bosses', 0)
        else:
            # 如果没有前置地图，使用总击杀统计
            current_monsters = player.kill_statistics.get('total_monsters', 0)
            current_bosses = player.kill_statistics.get('total_bosses', 0)

        # 检查是否已解锁
        is_unlocked = self.is_unlocked(player)

        if is_unlocked:
            progress_text = "✅ 已解锁"
        elif monsters_needed == 0 and bosses_needed == 0:
            progress_text = "✅ 已解锁"
        else:
            remaining_monsters = max(0, monsters_needed - current_monsters)
            remaining_bosses = max(0, bosses_needed - current_bosses)

            if prerequisite_map:
                progress_text = f"🔒 需要在{prerequisite_map}: 怪物{remaining_monsters}, Boss{remaining_bosses}"
            else:
                progress_text = f"🔒 需要: 怪物{remaining_monsters}, Boss{remaining_bosses}"

        return {
            'monsters_current': current_monsters,
            'monsters_needed': monsters_needed,
            'bosses_current': current_bosses,
            'bosses_needed': bosses_needed,
            'progress_text': progress_text,
            'prerequisite_map': prerequisite_map
        }
    
    def spawn_monsters(self, data_manager, boss_spawn_enabled=True):
        """
        在地图上生成怪物 - 改进版，确保怪物分散分布避免扎堆

        参数:
            data_manager: 数据管理器，用于获取怪物数据
            boss_spawn_enabled: 是否允许生成Boss
        """
        import random
        from ..models.enemy import Enemy
        
        self.active_enemies = []
        # 用于记录已占用的位置，确保怪物分散
        occupied_positions = set()
        # 最小距离设置
        min_distance_normal = 3  # 普通怪物之间最小距离
        min_distance_boss = 8    # BOSS与其他怪物的最小距离
        
        def is_position_valid(pos_x, pos_y, min_dist, occupied_pos):
            """检查位置是否有效（不与其他怪物太近）"""
            for occupied_x, occupied_y in occupied_pos:
                distance = ((pos_x - occupied_x) ** 2 + (pos_y - occupied_y) ** 2) ** 0.5
                if distance < min_dist:
                    return False
            return True
        
        def find_scattered_position(min_dist, occupied_pos, is_boss=False):
            """寻找分散的位置"""
            max_attempts = 100  # 最大尝试次数
            
            for attempt in range(max_attempts):
                if is_boss:
                    # BOSS优先在地图中心区域
                    center_zone = max(5, min(self.width // 4, self.height // 4))
                    pos_x = self.width // 2 + random.randint(-center_zone, center_zone)
                    pos_y = self.height // 2 + random.randint(-center_zone, center_zone)
                else:
                    # 普通怪物使用网格化分布策略
                    if attempt < 50:
                        # 前50次尝试使用网格化分布
                        grid_size = 8  # 网格大小
                        grid_x = random.randint(0, (self.width - 1) // grid_size)
                        grid_y = random.randint(0, (self.height - 1) // grid_size)
                        # 在网格内随机偏移
                        pos_x = grid_x * grid_size + random.randint(1, grid_size - 1)
                        pos_y = grid_y * grid_size + random.randint(1, grid_size - 1)
                    else:
                        # 后50次使用完全随机
                        pos_x = random.randint(3, self.width - 3)
                        pos_y = random.randint(3, self.height - 3)
                
                # 确保位置在地图范围内
                pos_x = max(1, min(pos_x, self.width - 1))
                pos_y = max(1, min(pos_y, self.height - 1))
                
                # 检查位置是否有效
                if is_position_valid(pos_x, pos_y, min_dist, occupied_pos):
                    return pos_x, pos_y
            
            # 如果找不到合适位置，返回随机位置（降级处理）
            print(f"警告: 在{max_attempts}次尝试后仍无法找到合适的分散位置，使用随机位置")
            return random.randint(1, self.width - 1), random.randint(1, self.height - 1)
        
        # 遍历地图配置中的所有怪物
        for monster_info in self.monsters:
            monster_name = monster_info["name"]
            weight = monster_info["weight"]
            is_boss = monster_info.get("is_boss", False)  # 检查是否为BOSS
            
            # 根据权重决定生成数量（BOSS只生成1个）
            if is_boss:
                if boss_spawn_enabled:
                    spawn_count = 1
                    print(f"准备生成BOSS: {monster_name}")
                else:
                    spawn_count = 0
                    print(f"跳过BOSS生成: {monster_name} (Boss生成已禁用)")
            else:
                # 调整普通怪物数量，确保不会太密集
                base_count = max(1, int(weight / 15))  # 降低密度
                spawn_count = min(base_count, 8)  # 限制最大数量
            
            for _ in range(spawn_count):
                # 从数据管理器获取怪物数据
                monster_data = data_manager.get_monster_data(monster_name)
                if monster_data:
                    # 为怪物设置分散位置
                    monster_data = monster_data.copy()
                    
                    if is_boss:
                        # BOSS使用更大的最小距离
                        pos_x, pos_y = find_scattered_position(min_distance_boss, occupied_positions, is_boss=True)
                    else:
                        # 普通怪物使用标准最小距离
                        pos_x, pos_y = find_scattered_position(min_distance_normal, occupied_positions, is_boss=False)
                    
                    monster_data["position"] = (pos_x, pos_y)
                    
                    # 记录占用的位置
                    occupied_positions.add((pos_x, pos_y))
                    
                    # 从 DataManager 获取怪物技能数据
                    monster_skills = data_manager.get_monster_skills_data(monster_name)
                    
                    # 创建怪物实例
                    enemy = Enemy(monster_data, skills=monster_skills if monster_skills else [])
                    # 确保 enemy 实例自身也保存了位置信息
                    enemy.position = (pos_x, pos_y)
                    
                    self.active_enemies.append(enemy)
                    
                    # 打印生成信息
                    if is_boss:
                        print(f"✨ 生成BOSS: {enemy.name} at {enemy.position}, 等级: {enemy.level}, HP: {enemy.hp}")
                    else:
                        print(f"🐾 生成怪物: {enemy.name} at {enemy.position}, 等级: {enemy.level}")
        
        print(f"地图 {self.name} 怪物生成完成，共生成 {len(self.active_enemies)} 个怪物")
        print(f"怪物分布区域: {len(occupied_positions)} 个不同位置，平均间距: {min_distance_normal} 格")
    
    def get_minimap_data(self, player_pos):
        """
        获取小地图所需的数据
        
        参数:
            player_pos: 玩家位置 (x, y)
            
        返回:
            dict: 包含小地图数据的字典
        """
        data = {
            "player_pos": player_pos,
            "enemies": [],
            "map_bounds": (self.width, self.height)
        }
        
        # 获取当前地图的所有怪物位置
        for enemy in self.active_enemies:
            if hasattr(enemy, 'position') and enemy.position:
                data["enemies"].append((
                    enemy.position[0],
                    enemy.position[1],
                    getattr(enemy, 'type', '普通'),
                    getattr(enemy, 'name', '未知怪物')
                ))
        
        return data
    
    def find_nearest_enemy(self, player_pos, enemy_type=None, max_distance=20):
        """
        寻找最近的怪物
        
        参数:
            player_pos: 玩家位置 (x, y)
            enemy_type: 目标怪物类型，None表示任意类型
            max_distance: 最大搜索距离
            
        返回:
            tuple: (enemy, distance) 最近的怪物和距离，如果没找到返回 (None, None)
        """
        nearest_enemy = None
        nearest_distance = float('inf')
        
        player_x, player_y = player_pos
        
        for enemy in self.active_enemies:
            if not hasattr(enemy, 'position') or not enemy.position:
                continue
                
            # 检查怪物类型
            if enemy_type and getattr(enemy, 'type', '普通') != enemy_type:
                continue
                
            # 计算距离
            enemy_x, enemy_y = enemy.position
            distance = ((enemy_x - player_x) ** 2 + (enemy_y - player_y) ** 2) ** 0.5
            
            # 检查距离限制
            if distance > max_distance:
                continue
                
            # 更新最近的怪物
            if distance < nearest_distance:
                nearest_distance = distance
                nearest_enemy = enemy
                
        return (nearest_enemy, nearest_distance if nearest_enemy else None)
    
    def get_enemy_at(self, x, y):
        """
        获取指定位置的怪物
        
        参数:
            x: X坐标
            y: Y坐标
            
        返回:
            怪物实例或None
        """
        if 0 <= x < self.width and 0 <= y < self.height:
            cell = self.grid[x][y]
            if cell and cell.get("type") == "enemy":
                return cell.get("enemy")
        return None
    
    def get_enemies_in_range(self, x, y, radius):
        """
        获取指定范围内的所有怪物
        
        参数:
            x: 中心X坐标
            y: 中心Y坐标
            radius: 范围半径
            
        返回:
            怪物列表
        """
        enemies = []
        
        for i in range(max(0, x - radius), min(self.width, x + radius + 1)):
            for j in range(max(0, y - radius), min(self.height, y + radius + 1)):
                enemy = self.get_enemy_at(i, j)
                if enemy:
                    enemies.append(enemy)
        
        return enemies
    
    def remove_enemy(self, enemy):
        """
        从地图上移除怪物
        
        参数:
            enemy: 要移除的怪物
        """
        if enemy in self.active_enemies:
            self.active_enemies.remove(enemy)
            
            # 从格子中移除
            x, y = enemy.position
            if 0 <= x < self.width and 0 <= y < self.height:
                if self.grid[x][y] and self.grid[x][y].get("type") == "enemy" and self.grid[x][y].get("enemy") == enemy:
                    self.grid[x][y] = None
    
    def is_position_valid(self, x, y):
        """
        检查位置是否有效（在地图范围内且没有障碍物）
        
        参数:
            x: X坐标
            y: Y坐标
            
        返回:
            位置是否有效
        """
        if 0 <= x < self.width and 0 <= y < self.height:
            cell = self.grid[x][y]
            return cell is None or (cell.get("type") != "obstacle" and cell.get("type") != "wall")
        return False
    
    def get_data(self):
        """
        获取地图数据
        
        返回:
            地图数据字典
        """
        return {
            "name": self.name,
            "difficulty": self.difficulty,
            "level_required": self.level_required,
            "description": self.description,
            "is_dungeon": self.is_dungeon,
            "dungeon_type": self.dungeon_type,
            "time_limit": self.time_limit,
            "width": self.width,
            "height": self.height,
            "spawn_point": self.spawn_point,
            "enemy_count": len(self.active_enemies)
        }
    
    def is_unlocked(self, player):
        """
        检查地图是否已解锁
        
        参数:
            player: 玩家对象
            
        返回:
            是否已解锁
        """
        # 检查等级要求
        if player.level < self.level_required:
            return False
        
        # 检查击杀要求
        if hasattr(player, 'can_unlock_map'):
            return player.can_unlock_map(self.unlock_requirements)
        
        # 如果玩家没有击杀统计功能，默认按等级解锁
        return True
    
    def get_unlock_progress(self, player):
        """
        获取地图解锁进度
        
        参数:
            player: 玩家对象
            
        返回:
            解锁进度信息字典
        """
        if not hasattr(player, 'kill_statistics'):
            return {
                'unlocked': True,
                'monsters_current': 0,
                'monsters_needed': 0,
                'bosses_current': 0,
                'bosses_needed': 0,
                'progress_text': '已解锁'
            }
        
        stats = player.kill_statistics
        monsters_current = stats['total_monsters']
        bosses_current = stats['total_bosses']
        monsters_needed = self.unlock_requirements.get('monsters_needed', 0)
        bosses_needed = self.unlock_requirements.get('bosses_needed', 0)
        
        unlocked = self.is_unlocked(player)
        
        if unlocked:
            progress_text = "✅ 已解锁"
        else:
            missing_monsters = max(0, monsters_needed - monsters_current)
            missing_bosses = max(0, bosses_needed - bosses_current)
            progress_text = f"🔒 需要: 怪物{missing_monsters}, Boss{missing_bosses}"
        
        return {
            'unlocked': unlocked,
            'monsters_current': monsters_current,
            'monsters_needed': monsters_needed,
            'bosses_current': bosses_current,
            'bosses_needed': bosses_needed,
            'progress_text': progress_text
        }


class MapManager:
    """
    地图管理器，负责加载和管理游戏地图
    """
    def __init__(self, data_manager=None):
        """初始化地图管理器"""
        self.maps = {}
        self.current_map = None
        self.current_map_name = "比奇省"  # 当前地图名称
        self.data_manager = data_manager # 数据管理器引用
        
        # 兼容性属性
        self.auto_challenge_enabled = False
        self.boss_challenge_storage = 15
        self.boss_respawn_times = {}

        # Boss生成控制
        self.boss_spawn_enabled = True  # 默认开启Boss生成
        
        self.load_maps()
    
    def load_maps(self):
        """加载地图数据"""
        map_file = get_game_data_path("maps_config.json")
        print(f"正在加载地图文件: {map_file}")
        
        try:
            with open(map_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                maps_data = data.get("maps", {})
                print(f"从配置文件读取到 {len(maps_data)} 个地图")
                
                for map_name, map_data in maps_data.items():
                    try:
                        self.maps[map_name] = Map(map_name, map_data)
                        print(f"成功创建地图: {map_name}")
                    except Exception as map_error:
                        print(f"创建地图 {map_name} 失败: {map_error}")
                
                print(f"总共创建了 {len(self.maps)} 个地图对象")
                print(f"可用地图: {list(self.maps.keys())}")
                
                # 设置默认地图
                if "比奇省" in self.maps:
                    self.current_map = self.maps["比奇省"]
                    print(f"设置默认地图: {self.current_map.name}")
                    # 初始化时生成怪物
                    if self.data_manager:
                        self.current_map.spawn_monsters(self.data_manager)
                elif self.maps:
                    self.current_map = next(iter(self.maps.values()))
                    print(f"设置默认地图: {self.current_map.name}")
                    # 初始化时生成怪物
                    if self.data_manager:
                        self.current_map.spawn_monsters(self.data_manager)
                else:
                    print("警告: 没有可用的地图数据")
                    self.current_map = None
                    
                print(f"最终current_map状态: {self.current_map.name if self.current_map else 'None'}")
                
        except Exception as e:
            print(f"加载地图数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_map(self, map_name):
        """
        获取指定名称的地图
        
        参数:
            map_name: 地图名称
            
        返回:
            地图实例或None
        """
        return self.maps.get(map_name)
    
    def get_all_maps(self):
        """
        获取所有地图
        
        返回:
            地图字典
        """
        return self.maps
    
    def get_all_map_names(self):
        """
        获取所有地图名称
        
        返回:
            地图名称列表
        """
        return list(self.maps.keys())
    
    def switch_map(self, map_name):
        """
        切换当前地图
        
        参数:
            map_name: 地图名称
            
        返回:
            是否切换成功
        """
        if map_name in self.maps:
            self.current_map = self.maps[map_name]
            self.current_map_name = map_name
            # 生成怪物（使用数据管理器）
            if self.data_manager:
                self.current_map.spawn_monsters(self.data_manager, self.boss_spawn_enabled)
            else:
                # 如果没有数据管理器，使用默认方法
                self.current_map.generate_enemies(10, self.boss_spawn_enabled)
            return True
        return False
    
    def get_current_map(self):
        """
        获取当前地图
        
        返回:
            当前地图实例
        """
        if self.current_map:
            return self.current_map
        elif self.current_map_name in self.maps:
            return self.maps[self.current_map_name]
        else:
            # 返回第一个可用的地图
            if self.maps:
                return list(self.maps.values())[0]
        return None
    
    def get_available_maps_for_level(self, player_level):
        """
        获取玩家等级可进入的地图
        
        参数:
            player_level: 玩家等级
            
        返回:
            可进入的地图列表
        """
        available_maps = []
        
        for map_name, map_obj in self.maps.items():
            if player_level >= map_obj.level_required:
                available_maps.append(map_name)
        
        return available_maps
    
    def get_map_by_id(self, map_id):
        """
        根据地图ID获取地图数据（兼容性方法）
        
        参数:
            map_id: 地图ID（如 "map_001"）
            
        返回:
            地图数据字典或None
        """
        # 简单的ID到名称映射
        id_to_name = {
            "map_001": "比奇省",
            "map_002": "沃玛森林", 
            "map_003": "骷髅洞",
            "map_004": "比奇矿区",
            "map_005": "毒蛇山谷"
        }
        
        map_name = id_to_name.get(map_id)
        if map_name and map_name in self.maps:
            map_obj = self.maps[map_name]
            return {
                "id": map_id,
                "name": map_name,
                "difficulty": map_obj.difficulty,
                "level_required": map_obj.level_required,
                "description": map_obj.description
            }
        return None
    
    def get_maps_by_category(self, category, player=None):
        """
        根据分类获取地图列表（兼容性方法）
        
        参数:
            category: 地图分类（"玛法"、"世界Boss"、"幻境塔"、"副本"）
            player: 玩家对象，用于检查地图解锁状态
            
        返回:
            该分类下的地图列表
        """
        # 创建基础地图数据结构
        available_maps = []
        
        # 根据实际加载的地图创建兼容的地图数据
        for i, (map_name, map_obj) in enumerate(self.maps.items(), 1):
            # 获取地图解锁进度
            unlock_progress = map_obj.get_unlock_progress(player) if player else {
                'unlocked': True,
                'monsters_current': 0,
                'monsters_needed': map_obj.unlock_requirements.get('monsters_needed', 0),
                'bosses_current': 0,
                'bosses_needed': map_obj.unlock_requirements.get('bosses_needed', 0),
                'progress_text': '需要玩家数据'
            }
            
            # 统计地图中的Boss数量
            boss_count = len([m for m in map_obj.monsters if m.get('is_boss', False)])
            
            map_data = {
                "id": f"map_{i:03d}",
                "name": map_name,
                "unlocked": unlock_progress['unlocked'],
                "difficulty": map_obj.difficulty,
                "category": "副本" if map_obj.is_dungeon else "玛法",
                "monsters_info": f"怪:{len(map_obj.monsters)}",
                "boss_info": f"Boss:{boss_count}",
                "challenge1_name": "击杀统计",
                "challenge1_stored": unlock_progress['monsters_current'],
                "unlock_req_kill": unlock_progress['monsters_needed'],
                "unlock_req_boss_kill": unlock_progress['bosses_needed'],
                "level_required": map_obj.level_required,
                "description": map_obj.description,
                "unlock_progress_text": unlock_progress['progress_text'],
                "monsters_current": unlock_progress['monsters_current'],
                "bosses_current": unlock_progress['bosses_current']
            }
            available_maps.append(map_data)
        
        # 根据分类过滤
        if category == "副本":
            return [map_data for map_data in available_maps if map_data.get("category", "玛法") == "副本"]
        else:
            return [map_data for map_data in available_maps if map_data.get("category", "玛法") == category]
    
    def get_available_maps_for_player(self, player):
        """
        获取玩家可进入的地图（考虑等级和击杀要求）
        
        参数:
            player: 玩家对象
            
        返回:
            可进入的地图列表
        """
        available_maps = []
        
        for map_name, map_obj in self.maps.items():
            if map_obj.is_unlocked(player):
                available_maps.append(map_name)
        
        return available_maps
    
    def can_enter_map(self, map_name, player):
        """
        检查玩家是否可以进入指定地图
        
        参数:
            map_name: 地图名称
            player: 玩家对象
            
        返回:
            是否可以进入，以及原因信息
        """
        if map_name not in self.maps:
            return False, "地图不存在"
        
        map_obj = self.maps[map_name]
        
        # 检查等级要求
        if player.level < map_obj.level_required:
            return False, f"需要等级 {map_obj.level_required}"
        
        # 检查击杀要求
        if not map_obj.is_unlocked(player):
            progress = map_obj.get_unlock_progress(player)
            return False, f"解锁条件未满足：{progress['progress_text']}"
        
        return True, "可以进入"
    
    def get_boss_attempts(self):
        """
        获取当前BOSS挑战次数信息（兼容性方法）
        
        返回:
            格式化的BOSS次数字符串，如 "15/15"
        """
        return "15/15"  # 默认值
    
    @property
    def available_maps(self):
        """
        获取所有可用地图（兼容性属性）
        
        返回:
            地图列表
        """
        return self.get_maps_by_category("玛法")
    
    def get_current_map_boss_info(self, map_data):
        """
        获取当前地图的Boss信息（兼容性方法）
        
        参数:
            map_data: 地图数据
            
        返回:
            Boss信息字典，包含名称、刷新状态等
        """
        if not map_data:
            return None
            
        # 检查是否有Boss
        boss_info = map_data.get("boss_info", "Boss:0")
        if boss_info == "Boss:0":
            return None
            
        map_name = map_data.get("name", "")
        
        # 简化的Boss信息
        return {
            "name": map_name,
            "has_boss": True,
            "is_available": True,
            "respawn_time": 300,
            "time_until_respawn": 0
        }
    
    def can_challenge_boss(self, map_data):
        """
        检查是否可以挑战Boss（兼容性方法）
        
        参数:
            map_data: 地图数据
            
        返回:
            是否可以挑战Boss
        """
        # 简化实现，总是返回True
        boss_info = self.get_current_map_boss_info(map_data)
        return boss_info is not None and boss_info.get("has_boss", False)
    
    def start_boss_challenge(self, map_data, is_manual=False):
        """
        开始Boss挑战（兼容性方法）
        
        参数:
            map_data: 地图数据
            is_manual: 是否为手动挑战
            
        返回:
            是否成功开始挑战
        """
        if not self.can_challenge_boss(map_data):
            return False
            
        map_name = map_data.get("name", "")
        print(f"开始挑战Boss: {map_name} ({'手动' if is_manual else '自动'})")
        
        # 实际启动战斗 - 这是关键的修复！
        if hasattr(self, 'battle_manager') and self.battle_manager:
            # 创建Boss敌人
            try:
                from game.models.enemy import enemy_factory
                boss_name = f"BOSS {map_name}"  # 根据地图名称创建Boss
                is_world_boss_map = map_data.get("is_world_boss", False)
                boss_enemy = enemy_factory.create_enemy(boss_name, 0, "BOSS", None, is_world_boss_map)
                
                # 获取玩家对象
                player = None
                if hasattr(self, 'player_instance'):
                    player = self.player_instance
                elif hasattr(self, 'data_manager') and self.data_manager:
                    # 尝试从数据管理器获取玩家
                    player = getattr(self.data_manager, 'player', None)
                
                if player and boss_enemy:
                    # 开始战斗
                    success = self.battle_manager.start_battle(player, boss_enemy)
                    if success:
                        print(f"成功开始Boss战斗: {boss_name}")
                        return True
                    else:
                        print(f"启动Boss战斗失败")
                        return False
                else:
                    print(f"无法获取玩家或Boss对象")
                    return False
                    
            except Exception as e:
                print(f"创建Boss战斗时出错: {e}")
                return False
        else:
            print("警告: 没有战斗管理器引用，无法启动实际战斗")
            
        return True
    
    def toggle_auto_challenge(self):
        """
        切换自动挑战Boss状态（兼容性方法）
        
        返回:
            当前自动挑战状态
        """
        # 简化实现，使用实例变量
        if not hasattr(self, 'auto_challenge_enabled'):
            self.auto_challenge_enabled = False
        self.auto_challenge_enabled = not self.auto_challenge_enabled
        return self.auto_challenge_enabled
    
    def get_next_unlockable_map_reqs(self, player):
        """
        获取下一个可解锁地图的需求信息（兼容性方法）
        
        参数:
            player: 玩家对象
        
        返回:
            包含解锁需求的字典，如果没有可解锁地图则返回None
        """
        # 简化实现，返回默认需求
        return {
            "map_name": "下一个地图",
            "kill": 100,
            "boss": 5
        }
    
    def spawn_monsters(self):
        """
        生成怪物（兼容性方法）
        """
        if self.current_map:
            if self.data_manager:
                self.current_map.spawn_monsters(self.data_manager, self.boss_spawn_enabled)
            else:
                self.current_map.generate_enemies(10, self.boss_spawn_enabled)
    
    def load_data(self, map_data):
        """
        加载地图数据（兼容性方法）
        
        参数:
            map_data: 地图数据
        """
        # 简化实现，暂时不做处理
        pass
    
    def get_data(self):
        """
        获取地图数据（兼容性方法）
        
        返回:
            地图数据字典
        """
        # 简化实现，返回基本信息
        return {
            "current_map_name": self.current_map_name,
            "maps": list(self.maps.keys())
        }

    def enable_boss_spawn(self):
        """开启Boss生成"""
        self.boss_spawn_enabled = True
        print("✅ Boss生成已开启")
        # 如果当前地图存在，重新生成怪物以包含Boss
        if self.current_map:
            self._regenerate_current_map_monsters()

    def disable_boss_spawn(self):
        """关闭Boss生成"""
        self.boss_spawn_enabled = False
        print("❌ Boss生成已关闭")
        # 如果当前地图存在，移除所有Boss
        if self.current_map:
            self._remove_bosses_from_current_map()

    def set_boss_spawn_enabled(self, enabled):
        """设置Boss生成状态"""
        if enabled:
            self.enable_boss_spawn()
        else:
            self.disable_boss_spawn()

    def get_boss_spawn_enabled(self):
        """获取Boss生成状态"""
        return self.boss_spawn_enabled

    def _regenerate_current_map_monsters(self):
        """重新生成当前地图的怪物"""
        try:
            if not self.current_map:
                return

            print("🔄 重新生成地图怪物...")

            # 清除现有怪物
            if hasattr(self.current_map, 'active_enemies'):
                self.current_map.active_enemies.clear()

            # 重新生成怪物
            if hasattr(self.current_map, 'spawn_monsters') and self.data_manager:
                self.current_map.spawn_monsters(self.data_manager, self.boss_spawn_enabled)
            elif hasattr(self.current_map, 'generate_enemies'):
                self.current_map.generate_enemies(10, self.boss_spawn_enabled)

            monster_count = len(self.current_map.active_enemies) if hasattr(self.current_map, 'active_enemies') else 0
            print(f"✅ 重新生成了 {monster_count} 个怪物")

        except Exception as e:
            print(f"❌ 重新生成怪物失败: {e}")

    def _remove_bosses_from_current_map(self):
        """从当前地图移除所有Boss"""
        try:
            if not self.current_map or not hasattr(self.current_map, 'active_enemies'):
                return

            # 移除Boss怪物
            original_count = len(self.current_map.active_enemies)
            self.current_map.active_enemies = [
                enemy for enemy in self.current_map.active_enemies
                if not getattr(enemy, 'is_boss', False)
            ]

            removed_count = original_count - len(self.current_map.active_enemies)
            if removed_count > 0:
                print(f"✅ 移除了 {removed_count} 个Boss")
            else:
                print("ℹ️ 当前地图没有Boss需要移除")

        except Exception as e:
            print(f"❌ 移除Boss失败: {e}")

    def replace_boss_with_normal_monsters(self):
        """将Boss替换为普通怪物"""
        self._remove_bosses_from_current_map()

    def restore_boss_monsters(self):
        """恢复Boss怪物"""
        self._regenerate_current_map_monsters()

    def get_next_unlockable_map(self, player):
        """
        获取下一个可解锁的地图
        
        参数:
            player: 玩家对象
            
        返回:
            下一个未解锁地图的信息，如果所有地图都已解锁则返回None
        """
        # 按等级要求排序，找到下一个未解锁的地图
        sorted_maps = sorted(
            self.maps.items(), 
            key=lambda x: (x[1].level_required, x[1].unlock_requirements.get('monsters_needed', 0))
        )
        
        for map_name, map_obj in sorted_maps:
            if not map_obj.is_unlocked(player):
                progress = map_obj.get_unlock_progress(player)
                return {
                    'name': map_name,
                    'level_required': map_obj.level_required,
                    'monsters_current': progress['monsters_current'],
                    'monsters_needed': progress['monsters_needed'],
                    'bosses_current': progress['bosses_current'],
                    'bosses_needed': progress['bosses_needed'],
                    'progress_text': progress['progress_text']
                }
        
        return None  # 所有地图都已解锁

    def get_categories(self):
        """
        获取所有地图分类
        
        返回:
            地图分类列表
        """
        # 基础分类
        categories = []
        
        # 检查是否有"玛法"类型的地图
        if any(not map_obj.is_dungeon for map_obj in self.maps.values()):
            categories.append("玛法")
        
        # 检查是否有"副本"类型的地图
        if any(map_obj.is_dungeon for map_obj in self.maps.values()):
            categories.append("副本")
        
        # 如果没有地图或者分类，返回默认分类
        if not categories:
            categories = ["玛法", "世界Boss", "幻境塔", "副本"]
        
        return categories


# 创建全局地图管理器实例
map_manager = MapManager()