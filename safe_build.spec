# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 最小化的安全配置
datas = [
    ('game/assets', 'game/assets'),
    ('game/data', 'game/data'),
]

# 确保包含必要的标准库模块
hiddenimports = [
    'email',
    'email.mime',
    'email.mime.text', 
    'pkg_resources',
    'setuptools',
    'tkinter',
    'tkinter.ttk',
    'pygame',
    'pyperclip',
]

# 保守的排除策略 - 只排除明确不需要的大型库
excludes = [
    'numpy',
    'scipy', 
    'matplotlib',
    'pandas',
    'tensorflow',
    'torch',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='萝卜传奇_安全版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩避免问题
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 使用控制台模式便于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
