#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页公告面板测试

测试新的分页公告系统，包括掉落信息、重要信息和消息去重功能
"""

import sys
import os
import time
import pygame
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from game.ui.announcement_panel import AnnouncementPanel, DropInfoManager, ImportantInfoManager


def test_drop_info_manager():
    """测试掉落信息管理器"""
    print("\n=== 测试掉落信息管理器 ===")
    
    drop_manager = DropInfoManager()
    
    # 添加一些掉落
    test_drops = [
        ("铁剑", "武器", "普通", "骷髅", "比奇省"),
        ("魔法戒指", "装备", "稀有", "半兽勇士", "比奇大陆"),
        ("传说宝石", "材料", "传说", "BOSS", "封魔谷"),
        ("生命药水", "消耗品", "普通", "鹿", "银杏村"),
        ("金币", "货币", "普通", "蛤蟆", "比奇省")
    ]
    
    for item_name, item_type, rarity, source, location in test_drops:
        drop_manager.add_drop(item_name, item_type, rarity, source, location)
    
    # 测试获取掉落记录
    recent_drops = drop_manager.get_recent_drops(3)
    print(f"✅ 最近3个掉落: {len(recent_drops)} 条记录")
    for drop in recent_drops:
        print(f"   - {drop['item_name']} ({drop['rarity']}) from {drop['source']}")
    
    # 测试重要掉落
    important_drops = drop_manager.get_important_drops()
    print(f"✅ 重要掉落: {len(important_drops)} 条记录")
    for drop in important_drops:
        print(f"   - {drop['item_name']} ({drop['rarity']})")
    
    # 测试统计摘要
    summary = drop_manager.get_drop_summary()
    print(f"✅ 掉落统计: {summary}")


def test_important_info_manager():
    """测试重要信息管理器"""
    print("\n=== 测试重要信息管理器 ===")
    
    important_manager = ImportantInfoManager()
    
    # 添加各种重要信息
    important_manager.add_level_up(9, 10)
    important_manager.add_achievement("初出茅庐")
    important_manager.add_death_info("强力骷髅", "比奇省")
    important_manager.add_important_message("系统维护通知", "系统", "重要", (255, 255, 0))
    
    # 获取最近消息
    recent_messages = important_manager.get_recent_messages(5)
    print(f"✅ 最近重要信息: {len(recent_messages)} 条记录")
    for msg in recent_messages:
        print(f"   - [{msg['type']}] {msg['message']}")


def test_announcement_panel_basic():
    """测试公告面板基本功能"""
    print("\n=== 测试公告面板基本功能 ===")
    
    # 初始化pygame（最小化）
    pygame.init()
    screen = pygame.display.set_mode((100, 100))
    
    # 创建模拟对象
    mock_battle_manager = Mock()
    mock_player = Mock()
    mock_player.name = "测试玩家"

    # 创建一个更完整的用户消息管理器模拟
    mock_user_message_manager = Mock()
    mock_user_message_manager.get_messages_by_type.return_value = []  # 返回空列表
    
    # 创建公告面板
    panel = AnnouncementPanel(
        screen=screen,
        battle_manager=mock_battle_manager,
        position=(0, 0),
        size=(400, 300),
        player=mock_player,
        user_message_manager=mock_user_message_manager
    )
    
    print(f"✅ 公告面板创建成功")
    print(f"   - 标签页数量: {len(panel.tabs)}")
    print(f"   - 当前标签页: {panel.tabs[panel.current_tab]}")
    print(f"   - 页面大小: {panel.page_size}")
    
    # 测试添加掉落信息
    panel.add_drop_info("测试武器", "武器", "史诗", "测试怪物", "测试地图")
    panel.add_drop_info("测试药水", "消耗品", "普通", "小怪", "新手村")
    
    # 测试添加重要信息
    panel.add_important_info("测试升级信息", "升级", "重要", (0, 255, 0))
    panel.add_important_info("测试成就信息", "成就", "重要", (255, 215, 0))
    
    # 测试获取各标签页消息
    for i, tab_name in enumerate(panel.tabs):
        panel.current_tab = i
        messages = panel.get_current_tab_messages()
        print(f"✅ {tab_name}: {len(messages)} 条消息")
    
    pygame.quit()


def test_message_deduplication():
    """测试消息去重功能"""
    print("\n=== 测试消息去重功能 ===")
    
    # 初始化pygame（最小化）
    pygame.init()
    screen = pygame.display.set_mode((100, 100))
    
    # 创建公告面板
    panel = AnnouncementPanel(
        screen=Mock(),
        battle_manager=Mock(),
        position=(0, 0),
        size=(400, 300),
        player=Mock(),
        user_message_manager=Mock()
    )
    
    # 测试掉落去重
    print("测试掉落信息去重...")
    initial_count = len(panel.drop_manager.get_recent_drops())
    
    # 添加相同掉落（应该被去重）
    panel.add_drop_info("重复武器", "武器", "普通", "同一怪物", "同一地图")
    panel.add_drop_info("重复武器", "武器", "普通", "同一怪物", "同一地图")  # 重复
    panel.add_drop_info("重复武器", "武器", "普通", "同一怪物", "同一地图")  # 重复
    
    after_duplicate_count = len(panel.drop_manager.get_recent_drops())
    print(f"   - 添加3次相同掉落，实际增加: {after_duplicate_count - initial_count} 条")
    
    # 添加不同掉落（不应该被去重）
    panel.add_drop_info("不同武器", "武器", "普通", "不同怪物", "不同地图")
    final_count = len(panel.drop_manager.get_recent_drops())
    print(f"   - 添加不同掉落，总计: {final_count} 条")
    
    # 测试重要信息去重
    print("测试重要信息去重...")
    initial_important_count = len(panel.important_manager.get_recent_messages())
    
    # 添加相同重要信息（应该被去重）
    panel.add_important_info("重复系统消息", "系统", "重要")
    panel.add_important_info("重复系统消息", "系统", "重要")  # 重复
    panel.add_important_info("重复系统消息", "系统", "重要")  # 重复
    
    after_important_count = len(panel.important_manager.get_recent_messages())
    print(f"   - 添加3次相同重要信息，实际增加: {after_important_count - initial_important_count} 条")
    
    pygame.quit()


def test_pagination():
    """测试分页功能"""
    print("\n=== 测试分页功能 ===")

    # 初始化pygame字体
    pygame.init()
    screen = pygame.display.set_mode((100, 100))

    # 创建公告面板
    mock_user_message_manager = Mock()
    mock_user_message_manager.get_messages_by_type.return_value = []

    panel = AnnouncementPanel(
        screen=screen,
        battle_manager=Mock(),
        position=(0, 0),
        size=(400, 300),
        player=Mock(),
        user_message_manager=mock_user_message_manager
    )
    
    # 添加大量掉落信息以测试分页
    for i in range(25):
        panel.add_drop_info(
            f"测试物品{i+1}",
            "测试类型",
            "普通" if i % 3 == 0 else "稀有",
            f"怪物{i+1}",
            f"地图{i % 3 + 1}"
        )
    
    # 切换到掉落标签页
    panel.current_tab = 1
    messages = panel.get_current_tab_messages()
    total_pages = max(1, (len(messages) + panel.page_size - 1) // panel.page_size)
    
    print(f"✅ 掉落信息分页测试:")
    print(f"   - 总消息数: {len(messages)}")
    print(f"   - 每页大小: {panel.page_size}")
    print(f"   - 总页数: {total_pages}")
    print(f"   - 当前页: {panel.current_page + 1}")
    
    # 测试翻页
    for page in range(min(3, total_pages)):
        panel.current_page = page
        start_index = page * panel.page_size
        end_index = min(start_index + panel.page_size, len(messages))
        page_messages = messages[start_index:end_index]
        print(f"   - 第{page+1}页: {len(page_messages)} 条消息")

    pygame.quit()


def run_all_tests():
    """运行所有测试"""
    print("开始测试分页公告面板系统...")
    print("=" * 60)
    
    try:
        test_drop_info_manager()
        test_important_info_manager()
        test_announcement_panel_basic()
        test_message_deduplication()
        test_pagination()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！分页公告面板系统工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
