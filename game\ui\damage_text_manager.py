#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
伤害文本管理器
管理战斗中的伤害数字弹出动画效果
"""

import pygame
import random
import math


class DamageText:
    """单个伤害文本对象"""
    
    def __init__(self, damage, position, text_type="damage", font_size=24):
        """
        初始化伤害文本
        
        Args:
            damage: 伤害数值
            position: 初始位置 (x, y)
            text_type: 文本类型 ("damage", "heal", "critical", "miss", "exp", "gold")
            font_size: 字体大小
        """
        self.damage = damage
        self.original_position = position
        self.position = list(position)  # 当前位置，可变
        self.text_type = text_type
        
        # 动画属性
        self.life_time = 90  # 生命周期（帧数）
        self.current_time = 0
        self.velocity_y = -3.0  # 初始向上速度
        self.velocity_x = random.uniform(-1.0, 1.0)  # 随机水平偏移
        self.gravity = 0.04  # 重力加速度
        
        # 视觉属性
        self.font_size = font_size
        self.scale = 1.0
        self.alpha = 255
        self.rotation = 0
        
        # 根据类型设置颜色和特效
        self._setup_visual_style()
        
        # 创建字体和文本表面
        self._create_text_surface()
    
    def _setup_visual_style(self):
        """根据文本类型设置视觉样式"""
        styles = {
            "damage": {
                "color": (255, 80, 80),  # 鲜红色
                "outline_color": (120, 0, 0),
                "scale_effect": True,
                "shake_effect": False
            },
            "critical": {
                "color": (255, 255, 80),  # 鲜黄色
                "outline_color": (180, 180, 0),
                "scale_effect": True,
                "shake_effect": True,
                "font_size_bonus": 10
            }
        }
        
        style = styles.get(self.text_type, styles["damage"])
        self.color = style["color"]
        self.outline_color = style["outline_color"]
        self.scale_effect = style["scale_effect"]
        self.shake_effect = style["shake_effect"]
        
        # 应用字体大小加成
        if "font_size_bonus" in style:
            self.font_size += style["font_size_bonus"]
    
    def _create_text_surface(self):
        """创建文本表面"""
        # 确保pygame字体模块已初始化
        if not pygame.get_init():
            pygame.init()
        if not pygame.font.get_init():
            pygame.font.init()
            
        # 创建字体
        try:
            self.font = pygame.font.Font(None, self.font_size)
        except pygame.error:
            # 如果默认字体失败，使用系统字体
            self.font = pygame.font.SysFont("Arial", self.font_size)
        
        # 格式化文本 - 只显示伤害数字
        text = str(int(self.damage))
        
        # 创建带描边的文本
        self.text_surface = self._create_outlined_text(text)
    
    def _create_outlined_text(self, text):
        """创建带描边的文本表面"""
        # 创建描边
        outline_surface = self.font.render(text, True, self.outline_color)
        outline_rect = outline_surface.get_rect()
        
        # 创建主文本
        main_surface = self.font.render(text, True, self.color)
        main_rect = main_surface.get_rect()
        
        # 创建最终表面（比原文本稍大以容纳描边）
        final_surface = pygame.Surface(
            (outline_rect.width + 4, outline_rect.height + 4), 
            pygame.SRCALPHA
        )
        
        # 绘制描边（在多个位置绘制以形成描边效果）
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                if dx != 0 or dy != 0:
                    final_surface.blit(outline_surface, (dx + 2, dy + 2))
        
        # 绘制主文本
        final_surface.blit(main_surface, (2, 2))
        
        return final_surface
    
    def update(self):
        """更新动画"""
        self.current_time += 1
        
        # 位置更新
        self.position[0] += self.velocity_x
        self.position[1] += self.velocity_y
        self.velocity_y += self.gravity
        
        # 计算生命周期进度（0到1）
        progress = self.current_time / self.life_time
        
        # 缩放效果
        if self.scale_effect:
            if progress < 0.15:
                # 前15%时间快速放大
                self.scale = 1.0 + (progress / 0.15) * 0.8
            elif progress < 0.3:
                # 接下来15%时间缩回正常大小
                self.scale = 1.8 - ((progress - 0.15) / 0.15) * 0.3
            else:
                # 保持稍大的大小
                self.scale = 1.5
        
        # 抖动效果（暴击）
        if self.shake_effect and progress < 0.3:
            shake_intensity = (1 - progress / 0.3) * 2
            self.position[0] += random.uniform(-shake_intensity, shake_intensity)
        
        # 透明度变化
        if progress > 0.7:
            # 最后30%时间淡出
            fade_progress = (progress - 0.7) / 0.3
            self.alpha = int(255 * (1 - fade_progress))
        
        # 返回是否还活着
        return self.current_time < self.life_time
    
    def render(self, screen):
        """渲染伤害文本"""
        if self.alpha <= 0:
            return
        
        # 应用缩放
        if self.scale != 1.0:
            scaled_size = (
                int(self.text_surface.get_width() * self.scale),
                int(self.text_surface.get_height() * self.scale)
            )
            scaled_surface = pygame.transform.scale(self.text_surface, scaled_size)
        else:
            scaled_surface = self.text_surface
        
        # 应用透明度
        if self.alpha < 255:
            scaled_surface = scaled_surface.copy()
            scaled_surface.set_alpha(self.alpha)
        
        # 计算渲染位置（居中）
        text_rect = scaled_surface.get_rect()
        text_rect.center = (int(self.position[0]), int(self.position[1]))
        
        # 渲染到屏幕
        screen.blit(scaled_surface, text_rect)


class DamageTextManager:
    """伤害文本管理器"""
    
    def __init__(self):
        """初始化伤害文本管理器"""
        self.damage_texts = []
        self.max_texts = 20  # 最大同时显示的文本数量
    
    def add_damage_text(self, damage, position, text_type="damage", font_size=24):
        """
        添加伤害文本
        
        Args:
            damage: 伤害数值
            position: 位置 (x, y)
            text_type: 文本类型
            font_size: 字体大小
        """
        # 如果文本太多，移除最旧的
        if len(self.damage_texts) >= self.max_texts:
            self.damage_texts.pop(0)
        
        # 添加位置随机偏移，避免重叠
        offset_x = random.uniform(-15, 15)
        offset_y = random.uniform(-10, 10)
        offset_position = (position[0] + offset_x, position[1] + offset_y)
        
        # 创建新的伤害文本
        damage_text = DamageText(damage, offset_position, text_type, font_size)
        self.damage_texts.append(damage_text)
    
    def add_player_damage(self, damage, position, is_critical=False):
        """添加玩家受到的伤害文本"""
        text_type = "critical" if is_critical else "damage"
        font_size = 30 if is_critical else 26
        self.add_damage_text(damage, position, text_type, font_size)
    
    def add_enemy_damage(self, damage, position, is_critical=False):
        """添加敌人受到的伤害文本"""
        text_type = "critical" if is_critical else "damage"
        font_size = 28 if is_critical else 24
        self.add_damage_text(damage, position, text_type, font_size)
    
    def update(self):
        """更新所有伤害文本"""
        # 更新所有文本，移除已过期的
        self.damage_texts = [
            text for text in self.damage_texts 
            if text.update()
        ]
    
    def render(self, screen):
        """渲染所有伤害文本"""
        for text in self.damage_texts:
            text.render(screen)
    
    def clear(self):
        """清除所有伤害文本"""
        self.damage_texts.clear() 