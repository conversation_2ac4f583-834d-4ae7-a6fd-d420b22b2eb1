import pygame
import tkinter as tk
from tkinter import ttk
from game.ui.ui_panel import UIPanel

class AutoPotionPanel(UIPanel):
    """
    自动吃药设置面板
    """
    def __init__(self, screen, battle_panel, position, size):
        super().__init__(screen, position, size)
        
        # 战斗面板引用
        self.battle_panel = battle_panel
        
        # 设置面板标题
        self.title = "自动吃药设置"
        
        # 设置面板背景色
        self.background_color = (40, 40, 50)
        
        # 创建设置控件
        self.create_controls()
    
    def create_controls(self):
        """
        创建设置控件
        """
        # 关闭按钮
        self.add_button(
            "关闭",
            (self.size[0] - 60, 5, 50, 20),
            self.close_panel,
            bg_color=(150, 50, 50)
        )
        
        # HP阈值调节按钮
        self.add_button(
            "HP-",
            (20, 60, 30, 20),
            lambda: self.adjust_hp_threshold(-5)
        )
        
        self.add_button(
            "HP+",
            (200, 60, 30, 20),
            lambda: self.adjust_hp_threshold(5)
        )
        
        # MP阈值调节按钮
        self.add_button(
            "MP-",
            (20, 100, 30, 20),
            lambda: self.adjust_mp_threshold(-5)
        )
        
        self.add_button(
            "MP+",
            (200, 100, 30, 20),
            lambda: self.adjust_mp_threshold(5)
        )
        
        # 冷却时间调节按钮
        self.add_button(
            "冷却-",
            (20, 140, 40, 20),
            lambda: self.adjust_cooldown(-500)
        )
        
        self.add_button(
            "冷却+",
            (190, 140, 40, 20),
            lambda: self.adjust_cooldown(500)
        )
        
        # 开启/关闭自动吃药按钮
        self.add_button(
            "启用自动吃药",
            (50, 180, 100, 25),
            self.toggle_auto_potion,
            bg_color=(0, 100, 0)
        )
    
    def render(self):
        """
        渲染设置面板
        """
        super().render()
        
        # 渲染设置信息
        self.render_settings_info()
    
    def render_settings_info(self):
        """
        渲染设置信息
        """
        y_offset = 40
        
        # HP阈值设置
        hp_text = f"HP阈值: {self.battle_panel.hp_threshold}%"
        hp_surface = self.normal_font.render(hp_text, True, self.normal_color)
        self.screen.blit(hp_surface, (self.rect.left + 60, self.rect.top + y_offset + 20))
        
        # HP阈值进度条
        hp_bar_rect = pygame.Rect(self.rect.left + 60, self.rect.top + y_offset + 40, 120, 10)
        pygame.draw.rect(self.screen, (100, 100, 100), hp_bar_rect)
        hp_fill_width = int(120 * (self.battle_panel.hp_threshold / 100))
        hp_fill_rect = pygame.Rect(self.rect.left + 60, self.rect.top + y_offset + 40, hp_fill_width, 10)
        pygame.draw.rect(self.screen, (255, 0, 0), hp_fill_rect)
        
        y_offset += 40
        
        # MP阈值设置
        mp_text = f"MP阈值: {self.battle_panel.mp_threshold}%"
        mp_surface = self.normal_font.render(mp_text, True, self.normal_color)
        self.screen.blit(mp_surface, (self.rect.left + 60, self.rect.top + y_offset + 20))
        
        # MP阈值进度条
        mp_bar_rect = pygame.Rect(self.rect.left + 60, self.rect.top + y_offset + 40, 120, 10)
        pygame.draw.rect(self.screen, (100, 100, 100), mp_bar_rect)
        mp_fill_width = int(120 * (self.battle_panel.mp_threshold / 100))
        mp_fill_rect = pygame.Rect(self.rect.left + 60, self.rect.top + y_offset + 40, mp_fill_width, 10)
        pygame.draw.rect(self.screen, (0, 0, 255), mp_fill_rect)
        
        y_offset += 40
        
        # 冷却时间设置
        cooldown_text = f"冷却时间: {self.battle_panel.potion_cooldown / 1000:.1f}秒"
        cooldown_surface = self.normal_font.render(cooldown_text, True, self.normal_color)
        self.screen.blit(cooldown_surface, (self.rect.left + 60, self.rect.top + y_offset + 20))
        
        y_offset += 40
        
        # 自动吃药状态
        status_text = "状态: " + ("已启用" if self.battle_panel.auto_potion_enabled else "已禁用")
        status_color = (0, 255, 0) if self.battle_panel.auto_potion_enabled else (255, 0, 0)
        status_surface = self.normal_font.render(status_text, True, status_color)
        self.screen.blit(status_surface, (self.rect.left + 60, self.rect.top + y_offset + 20))
    
    def adjust_hp_threshold(self, delta):
        """
        调整HP阈值
        """
        new_threshold = self.battle_panel.hp_threshold + delta
        self.battle_panel.hp_threshold = max(10, min(90, new_threshold))
        print(f"HP阈值调整为: {self.battle_panel.hp_threshold}%")
    
    def adjust_mp_threshold(self, delta):
        """
        调整MP阈值
        """
        new_threshold = self.battle_panel.mp_threshold + delta
        self.battle_panel.mp_threshold = max(10, min(90, new_threshold))
        print(f"MP阈值调整为: {self.battle_panel.mp_threshold}%")
    
    def adjust_cooldown(self, delta):
        """
        调整冷却时间
        """
        new_cooldown = self.battle_panel.potion_cooldown + delta
        self.battle_panel.potion_cooldown = max(500, min(10000, new_cooldown))
        print(f"冷却时间调整为: {self.battle_panel.potion_cooldown / 1000:.1f}秒")
    
    def toggle_auto_potion(self):
        """
        切换自动吃药状态
        """
        self.battle_panel.toggle_auto_potion()
        
        # 更新按钮文本和颜色
        for button in self.buttons:
            if "自动吃药" in button["text"]:
                if self.battle_panel.auto_potion_enabled:
                    button["text"] = "禁用自动吃药"
                    button["bg_color"] = (150, 50, 50)
                else:
                    button["text"] = "启用自动吃药"
                    button["bg_color"] = (0, 100, 0)
                break
    
    def close_panel(self):
        """
        关闭面板
        """
        self.visible = False
        print("自动吃药设置面板已关闭")

class AutoPotionConfigWindow:
    """
    自动吃药配置窗口（使用Tkinter实现更复杂的UI）
    """
    def __init__(self, battle_panel):
        self.battle_panel = battle_panel
        self.window = None
        
    def show(self):
        """
        显示配置窗口
        """
        if self.window is not None:
            self.window.lift()
            return
            
        self.window = tk.Toplevel()
        self.window.title("自动吃药设置")
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.close_window)
        
        self.create_widgets()
    
    def create_widgets(self):
        """
        创建控件
        """
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="自动吃药系统设置", font=("SimHei", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 启用/禁用自动吃药
        self.auto_enabled_var = tk.BooleanVar(value=self.battle_panel.auto_potion_enabled)
        auto_check = ttk.Checkbutton(
            main_frame, 
            text="启用自动吃药", 
            variable=self.auto_enabled_var,
            command=self.toggle_auto_potion
        )
        auto_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # HP阈值设置
        ttk.Label(main_frame, text="HP阈值 (%)").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.hp_threshold_var = tk.IntVar(value=self.battle_panel.hp_threshold)
        hp_scale = ttk.Scale(
            main_frame, 
            from_=10, 
            to=90, 
            variable=self.hp_threshold_var,
            orient=tk.HORIZONTAL,
            length=200,
            command=self.update_hp_threshold
        )
        hp_scale.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.hp_value_label = ttk.Label(main_frame, text=f"{self.battle_panel.hp_threshold}%")
        self.hp_value_label.grid(row=4, column=0, sticky=tk.W, pady=(0, 10))
        
        # MP阈值设置
        ttk.Label(main_frame, text="MP阈值 (%)").grid(row=5, column=0, sticky=tk.W, pady=(0, 5))
        self.mp_threshold_var = tk.IntVar(value=self.battle_panel.mp_threshold)
        mp_scale = ttk.Scale(
            main_frame, 
            from_=10, 
            to=90, 
            variable=self.mp_threshold_var,
            orient=tk.HORIZONTAL,
            length=200,
            command=self.update_mp_threshold
        )
        mp_scale.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.mp_value_label = ttk.Label(main_frame, text=f"{self.battle_panel.mp_threshold}%")
        self.mp_value_label.grid(row=7, column=0, sticky=tk.W, pady=(0, 10))
        
        # 冷却时间设置
        ttk.Label(main_frame, text="冷却时间 (秒)").grid(row=8, column=0, sticky=tk.W, pady=(0, 5))
        self.cooldown_var = tk.DoubleVar(value=self.battle_panel.potion_cooldown / 1000)
        cooldown_scale = ttk.Scale(
            main_frame, 
            from_=0.5, 
            to=10.0, 
            variable=self.cooldown_var,
            orient=tk.HORIZONTAL,
            length=200,
            command=self.update_cooldown
        )
        cooldown_scale.grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.cooldown_value_label = ttk.Label(main_frame, text=f"{self.battle_panel.potion_cooldown / 1000:.1f}秒")
        self.cooldown_value_label.grid(row=10, column=0, sticky=tk.W, pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=11, column=0, columnspan=2, pady=(10, 0))
        
        # 保存按钮
        save_button = ttk.Button(button_frame, text="保存设置", command=self.save_settings)
        save_button.grid(row=0, column=0, padx=(0, 10))
        
        # 重置按钮
        reset_button = ttk.Button(button_frame, text="重置默认", command=self.reset_settings)
        reset_button.grid(row=0, column=1, padx=(10, 0))
        
        # 关闭按钮
        close_button = ttk.Button(button_frame, text="关闭", command=self.close_window)
        close_button.grid(row=0, column=2, padx=(10, 0))
    
    def toggle_auto_potion(self):
        """
        切换自动吃药状态
        """
        self.battle_panel.auto_potion_enabled = self.auto_enabled_var.get()
        status = "开启" if self.battle_panel.auto_potion_enabled else "关闭"
        print(f"自动吃药已{status}")
    
    def update_hp_threshold(self, value):
        """
        更新HP阈值
        """
        self.battle_panel.hp_threshold = int(float(value))
        self.hp_value_label.config(text=f"{self.battle_panel.hp_threshold}%")
    
    def update_mp_threshold(self, value):
        """
        更新MP阈值
        """
        self.battle_panel.mp_threshold = int(float(value))
        self.mp_value_label.config(text=f"{self.battle_panel.mp_threshold}%")
    
    def update_cooldown(self, value):
        """
        更新冷却时间
        """
        self.battle_panel.potion_cooldown = int(float(value) * 1000)
        self.cooldown_value_label.config(text=f"{float(value):.1f}秒")
    
    def save_settings(self):
        """
        保存设置
        """
        # 这里可以将设置保存到文件
        print("设置已保存")
        print(f"HP阈值: {self.battle_panel.hp_threshold}%")
        print(f"MP阈值: {self.battle_panel.mp_threshold}%")
        print(f"冷却时间: {self.battle_panel.potion_cooldown / 1000:.1f}秒")
        print(f"自动吃药: {'启用' if self.battle_panel.auto_potion_enabled else '禁用'}")
    
    def reset_settings(self):
        """
        重置为默认设置
        """
        self.battle_panel.hp_threshold = 50
        self.battle_panel.mp_threshold = 30
        self.battle_panel.potion_cooldown = 2000
        self.battle_panel.auto_potion_enabled = False
        
        # 更新控件值
        self.hp_threshold_var.set(50)
        self.mp_threshold_var.set(30)
        self.cooldown_var.set(2.0)
        self.auto_enabled_var.set(False)
        
        # 更新标签
        self.hp_value_label.config(text="50%")
        self.mp_value_label.config(text="30%")
        self.cooldown_value_label.config(text="2.0秒")
        
        print("设置已重置为默认值")
    
    def close_window(self):
        """
        关闭窗口
        """
        if self.window:
            self.window.destroy()
            self.window = None