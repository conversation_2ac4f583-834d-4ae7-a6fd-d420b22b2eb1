#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
怪物图片管理器
负责加载、缓存和管理怪物图片资源
"""

import pygame
import os
from typing import Dict, Optional
from game.core.resource_manager import get_game_asset_path

class EnemyImageManager:
    """
    怪物图片管理器
    负责加载、缓存和提供怪物图片
    """
    
    def __init__(self):
        """初始化怪物图片管理器"""
        self.image_cache = {}  # 图片缓存 {怪物名称: pygame.Surface}
        self.default_size = (40, 40)  # 默认怪物图片大小
        self.images_path = get_game_asset_path("images/enemies")  # 使用资源管理器获取路径
        self.default_image = None  # 默认图片（当找不到怪物图片时使用）
        
        # 初始化默认图片
        self._create_default_image()
        
        # 🔧 修复：移除自动预加载，等待display初始化后再手动调用
        # self._preload_common_enemies()  # 注释掉自动预加载
        print("🔄 EnemyImageManager初始化完成，预加载将在pygame.display初始化后执行")
    
    def _create_default_image(self):
        """创建默认怪物图片"""
        try:
            # 创建一个默认的怪物图片（红色圆形）
            surface = pygame.Surface(self.default_size, pygame.SRCALPHA)
            center = (self.default_size[0] // 2, self.default_size[1] // 2)
            radius = min(self.default_size) // 3
            
            # 绘制红色圆形作为默认怪物
            pygame.draw.circle(surface, (255, 100, 100), center, radius)
            pygame.draw.circle(surface, (200, 50, 50), center, radius, 2)
            
            # 添加眼睛
            eye_size = 3
            left_eye = (center[0] - 6, center[1] - 4)
            right_eye = (center[0] + 6, center[1] - 4)
            pygame.draw.circle(surface, (255, 255, 255), left_eye, eye_size)
            pygame.draw.circle(surface, (255, 255, 255), right_eye, eye_size)
            pygame.draw.circle(surface, (0, 0, 0), left_eye, 2)
            pygame.draw.circle(surface, (0, 0, 0), right_eye, 2)
            
            self.default_image = surface
            print("✅ 创建默认怪物图片成功")
            
        except Exception as e:
            print(f"❌ 创建默认怪物图片失败: {e}")
            # 如果连默认图片都创建失败，创建一个简单的矩形
            surface = pygame.Surface(self.default_size)
            surface.fill((255, 0, 0))
            self.default_image = surface
    
    def _preload_common_enemies(self):
        """预加载常用怪物图片"""
        # 检查pygame.display是否已初始化
        if not pygame.get_init() or pygame.display.get_surface() is None:
            print("🔄 pygame.display未初始化，跳过预加载，将在首次使用时加载图片")
            return
            
        common_enemies = [
            "钉耙猫", "多钩猫", "毒蜘蛛", "森林雪人", "半兽人", 
            "半兽战士", "半兽勇士", "骷髅", "鸡", "鹿", "蛤蟆", 
            "稻草人", "食人花", "沃玛战士", "沃玛勇士", "触龙神"
        ]
        
        print("🔄 开始预加载常用怪物图片...")
        loaded_count = 0
        
        for enemy_name in common_enemies:
            if self.load_enemy_image(enemy_name):
                loaded_count += 1
        
        print(f"✅ 预加载完成，成功加载 {loaded_count}/{len(common_enemies)} 个怪物图片")
    
    def load_enemy_image(self, enemy_name: str, size: tuple = None) -> pygame.Surface:
        """
        加载怪物图片
        
        参数:
            enemy_name: 怪物名称
            size: 图片大小，默认使用default_size
            
        返回:
            pygame.Surface: 怪物图片表面
        """
        if size is None:
            size = self.default_size
        
        # 生成缓存键
        cache_key = f"{enemy_name}_{size[0]}x{size[1]}"
        
        # 检查缓存
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]
        
        # 尝试加载图片文件
        image_path = os.path.join(self.images_path, f"{enemy_name}.png")
        
        try:
            if os.path.exists(image_path):
                # 加载原始图片
                original_image = pygame.image.load(image_path)
                
                # 检查pygame.display是否已初始化，决定是否转换图片格式
                if pygame.get_init() and pygame.display.get_surface() is not None:
                    original_image = original_image.convert_alpha()
                
                # 缩放到指定大小
                scaled_image = pygame.transform.scale(original_image, size)
                
                # 缓存图片
                self.image_cache[cache_key] = scaled_image
                
                print(f"✅ 成功加载怪物图片: {enemy_name}")
                return scaled_image
            else:
                print(f"⚠️ 怪物图片文件不存在: {image_path}")
                
        except Exception as e:
            print(f"❌ 加载怪物图片失败 {enemy_name}: {e}")
        
        # 如果加载失败，返回默认图片
        if self.default_image:
            if size != self.default_size:
                # 如果需要不同尺寸，缩放默认图片
                scaled_default = pygame.transform.scale(self.default_image, size)
                self.image_cache[cache_key] = scaled_default
                return scaled_default
            else:
                return self.default_image
        
        # 如果连默认图片都没有，创建临时图片
        return self._create_temporary_image(size)
    
    def _create_temporary_image(self, size: tuple) -> pygame.Surface:
        """
        创建临时图片（当所有加载方式都失败时）
        
        参数:
            size: 图片大小
            
        返回:
            pygame.Surface: 临时图片
        """
        surface = pygame.Surface(size)
        surface.fill((128, 128, 128))  # 灰色背景
        
        # 添加一个简单的"?"标记
        if pygame.font.get_init():
            font = pygame.font.Font(None, min(size) // 2)
            text = font.render("?", True, (255, 255, 255))
            text_rect = text.get_rect(center=(size[0] // 2, size[1] // 2))
            surface.blit(text, text_rect)
        
        return surface
    
    def get_enemy_image(self, enemy_name: str, size: tuple = None) -> pygame.Surface:
        """
        获取怪物图片（主要接口）
        
        参数:
            enemy_name: 怪物名称
            size: 图片大小
            
        返回:
            pygame.Surface: 怪物图片
        """
        return self.load_enemy_image(enemy_name, size)
    
    def preload_enemy_image(self, enemy_name: str, size: tuple = None):
        """
        预加载怪物图片到缓存
        
        参数:
            enemy_name: 怪物名称
            size: 图片大小
        """
        self.load_enemy_image(enemy_name, size)
    
    def clear_cache(self):
        """清空图片缓存"""
        self.image_cache.clear()
        print("✅ 怪物图片缓存已清空")
    
    def get_cache_info(self) -> Dict:
        """
        获取缓存信息
        
        返回:
            缓存信息字典
        """
        return {
            "cached_images": len(self.image_cache),
            "cache_keys": list(self.image_cache.keys()),
            "images_path": self.images_path,
            "default_size": self.default_size
        }
    
    def batch_load_images(self, enemy_names: list, size: tuple = None):
        """
        批量加载怪物图片
        
        参数:
            enemy_names: 怪物名称列表
            size: 图片大小
        """
        print(f"🔄 开始批量加载 {len(enemy_names)} 个怪物图片...")
        loaded_count = 0
        
        for enemy_name in enemy_names:
            try:
                self.load_enemy_image(enemy_name, size)
                loaded_count += 1
            except Exception as e:
                print(f"❌ 批量加载失败 {enemy_name}: {e}")
        
        print(f"✅ 批量加载完成，成功 {loaded_count}/{len(enemy_names)} 个")
    
    def get_available_enemy_images(self) -> list:
        """
        获取可用的怪物图片列表
        
        返回:
            可用怪物图片名称列表
        """
        available_images = []
        
        if os.path.exists(self.images_path):
            for filename in os.listdir(self.images_path):
                if filename.endswith('.png'):
                    enemy_name = filename[:-4]  # 移除.png后缀
                    available_images.append(enemy_name)
        
        return sorted(available_images)
    
    def has_image(self, enemy_name: str) -> bool:
        """
        检查是否有指定怪物的图片
        
        参数:
            enemy_name: 怪物名称
            
        返回:
            是否有对应图片
        """
        image_path = os.path.join(self.images_path, f"{enemy_name}.png")
        return os.path.exists(image_path)

    def initialize_after_display(self):
        """
        在pygame.display初始化后调用的初始化方法
        用于执行需要display的操作，如图片预加载
        """
        if pygame.get_init() and pygame.display.get_surface() is not None:
            print("🔄 pygame.display已初始化，开始预加载怪物图片...")
            self._preload_common_enemies()
        else:
            print("⚠️ pygame.display仍未初始化，无法预加载图片")


# 创建全局怪物图片管理器实例
enemy_image_manager = EnemyImageManager() 