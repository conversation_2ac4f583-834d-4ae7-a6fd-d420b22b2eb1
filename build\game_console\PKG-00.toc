('E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\萝卜传奇_控制台版.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\main.py', 'PYSOURCE'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('SDL2_ttf.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_ttf.dll',
   'BINARY'),
  ('libopusfile-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libopusfile-0.dll',
   'BINARY'),
  ('libopus-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libopus-0.dll',
   'BINARY'),
  ('libogg-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libogg-0.dll',
   'BINARY'),
  ('libjpeg-62.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libjpeg-62.dll',
   'BINARY'),
  ('libmodplug-1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libmodplug-1.dll',
   'BINARY'),
  ('libtiff-5.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libtiff-5.dll',
   'BINARY'),
  ('libwebpdemux-2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwebpdemux-2.dll',
   'BINARY'),
  ('libxmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libxmp.dll',
   'BINARY'),
  ('libjpeg-9.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libjpeg-9.dll',
   'BINARY'),
  ('SDL2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2.dll',
   'BINARY'),
  ('libwavpack-1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwavpack-1.dll',
   'BINARY'),
  ('SDL2_image.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_image.dll',
   'BINARY'),
  ('SDL2_mixer.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2_mixer.dll',
   'BINARY'),
  ('zlib1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\zlib1.dll',
   'BINARY'),
  ('portmidi.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\portmidi.dll',
   'BINARY'),
  ('freetype.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\freetype.dll',
   'BINARY'),
  ('libwebp-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwebp-7.dll',
   'BINARY'),
  ('libpng16-16.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libpng16-16.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('pygame\\imageext.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\imageext.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\window.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\window.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\system.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\system.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\scrap.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\scrap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mixer.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mixer.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mixer_music.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mixer_music.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\font.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\font.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_freetype.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_freetype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\transform.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\transform.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\time.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\time.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\pixelarray.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pixelarray.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mask.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mask.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\surface.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surface.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\pixelcopy.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pixelcopy.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\mouse.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\mouse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\key.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\key.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\joystick.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\joystick.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\image.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\image.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\event.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\event.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\draw.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\draw.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\display.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\display.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\math.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\math.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\bufferproxy.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\bufferproxy.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\color.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\color.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\surflock.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\surflock.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\rwobject.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\rwobject.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\rect.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\rect.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\constants.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\constants.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\base.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\video.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\video.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\audio.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\audio.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\_sdl2\\sdl2.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\_sdl2\\sdl2.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pygame\\SDL2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\SDL2.dll',
   'BINARY'),
  ('pygame\\libopus-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libopus-0.dll',
   'BINARY'),
  ('pygame\\libogg-0.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libogg-0.dll',
   'BINARY'),
  ('pygame\\libwebp-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\libwebp-7.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\tk86t.dll',
   'BINARY'),
  ('game\\assets\\images\\background.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\background.png',
   'DATA'),
  ('game\\assets\\images\\characters\\战士_女.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\characters\\战士_女.png',
   'DATA'),
  ('game\\assets\\images\\characters\\战士_男.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\characters\\战士_男.png',
   'DATA'),
  ('game\\assets\\images\\characters\\法师_女.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\characters\\法师_女.png',
   'DATA'),
  ('game\\assets\\images\\characters\\法师_男.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\characters\\法师_男.png',
   'DATA'),
  ('game\\assets\\images\\characters\\道士_女.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\characters\\道士_女.png',
   'DATA'),
  ('game\\assets\\images\\characters\\道士_男.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\characters\\道士_男.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\0095.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\0095.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\0096.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\0096.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\0097.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\0097.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\万年树妖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\万年树妖.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\僵尸.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\僵尸.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\千年树妖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\千年树妖.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\半兽人.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\半兽人.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\半兽勇士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\半兽勇士.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\半兽战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\半兽战士.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\多钩猫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\多钩猫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\天怒魔王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\天怒魔王.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\尸王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\尸王.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\山洞蝙蝠.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\山洞蝙蝠.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\巨型蠕虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\巨型蠕虫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\掷斧骷髅.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\掷斧骷髅.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\断刃战魂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\断刃战魂.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\暗黑战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\暗黑战士.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\森林雪人.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\森林雪人.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\毒蜘蛛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\毒蜘蛛.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\沃玛勇士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\沃玛勇士.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\沃玛卫士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\沃玛卫士.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\沃玛战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\沃玛战士.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\沃玛战将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\沃玛战将.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\沃玛教主.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\沃玛教主.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\洞蛆.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\洞蛆.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\火焰沃玛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\火焰沃玛.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\焚炉恶卒.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\焚炉恶卒.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\牛头魔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\牛头魔.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\牛魔将军.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\牛魔将军.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\牛魔法师.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\牛魔法师.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\牛魔王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\牛魔王.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\牛魔祭司.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\牛魔祭司.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\玄铁邪灵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\玄铁邪灵.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\玉石魔怪.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\玉石魔怪.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\神匠魂主.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\神匠魂主.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\稻草人.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\稻草人.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\粪虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\粪虫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\红蛇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\红蛇.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\羊.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\羊.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\虎蛇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\虎蛇.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\虹魔教主.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\虹魔教主.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\虹魔猪卫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\虹魔猪卫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\虹魔蝎卫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\虹魔蝎卫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\蚀金魔将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\蚀金魔将.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\蛤蟆.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\蛤蟆.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\蜈蚣.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\蜈蚣.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\蝎子.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\蝎子.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\触龙神.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\触龙神.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\贪蟾魔将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\贪蟾魔将.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\赤铜邪灵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\赤铜邪灵.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\跳跳蜂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\跳跳蜂.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\邪恶钳虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\邪恶钳虫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\金甲护卫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\金甲护卫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\钉耙猫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\钉耙猫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\钳虫.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\钳虫.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\锻魔督军.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\锻魔督军.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\镇宝龙王.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\镇宝龙王.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\食人花.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\食人花.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\骷髅.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\骷髅.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\骷髅战士.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\骷髅战士.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\骷髅战将.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\骷髅战将.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\骷髅精灵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\骷髅精灵.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\鸡.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\鸡.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\鹿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\鹿.png',
   'DATA'),
  ('game\\assets\\images\\enemies\\黑色恶蛆.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\enemies\\黑色恶蛆.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\勋章\\新手勋章.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\勋章\\新手勋章.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\圣战头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\圣战头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\天尊头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\天尊头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\法神头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\法神头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\祈祷头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\祈祷头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\神秘头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\神秘头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\精灵头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\精灵头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\记忆头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\记忆头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\道士头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\道士头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\青铜头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\青铜头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\骷髅头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\骷髅头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\魔法头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\魔法头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\头盔\\黑铁头盔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\头盔\\黑铁头盔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\传送戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\传送戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\六角戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\六角戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\力量戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\力量戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\古铜戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\古铜戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\圣战戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\圣战戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\复活戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\复活戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\天尊戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\天尊戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\护身戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\护身戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\求婚戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\求婚戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\法神戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\法神戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\泰坦戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\泰坦戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\火焰戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\火焰戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\牛角戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\牛角戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\狂风戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\狂风戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\玻璃戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\玻璃戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\珊瑚戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\珊瑚戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\珍珠戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\珍珠戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\生铁戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\生铁戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\祈祷戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\祈祷戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\神秘戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\神秘戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\紫碧螺.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\紫碧螺.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\红宝石戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\红宝石戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\蓝色水晶戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\蓝色水晶戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\虹魔戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\虹魔戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\蛇眼戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\蛇眼戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\记忆戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\记忆戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\超负载戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\超负载戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\道德戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\道德戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\金戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\金戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\铂金戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\铂金戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\防御戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\防御戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\降妖除魔戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\降妖除魔戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\隐身戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\隐身戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\骷髅戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\骷髅戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\魅力戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\魅力戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\魔血戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\魔血戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\麻痹戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\麻痹戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\黑色水晶戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\黑色水晶戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\戒指\\龙之戒指.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\戒指\\龙之戒指.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\三眼手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\三眼手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\圣战手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\圣战手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\坚固手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\坚固手套.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\夏普儿手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\夏普儿手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\大手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\大手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\天尊手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\天尊手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\小手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\小手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\幽灵手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\幽灵手套.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\心灵手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\心灵手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\思贝儿手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\思贝儿手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\死神手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\死神手套.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\法神手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\法神手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\皮制手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\皮制手套.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\祈祷手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\祈祷手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\神秘腰带.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\神秘腰带.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\虹魔手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\虹魔手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\记忆手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\记忆手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\辟邪手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\辟邪手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\道士手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\道士手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\金手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\金手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\钢手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\钢手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\铁手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\铁手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\银手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\银手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\阎罗手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\阎罗手套.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\骑士手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\骑士手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\魔力手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\魔力手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\魔法手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\魔法手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\魔血手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\魔血手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\黑檀手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\黑檀手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\黑铁手套.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\黑铁手套.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\手镯\\龙之手镯.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\手镯\\龙之手镯.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\技能书\\技能书.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\技能书\\技能书.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\乌木剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\乌木剑.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\井中月.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\井中月.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\修罗.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\修罗.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\偃月.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\偃月.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\八荒.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\八荒.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\凌风.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\凌风.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\凝霜.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\凝霜.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\匕首.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\匕首.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\半月.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\半月.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\命运之刃.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\命运之刃.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\嗜魂法杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\嗜魂法杖.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\屠龙.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\屠龙.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\怒斩.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\怒斩.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\斩马刀.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\斩马刀.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\无极棍.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\无极棍.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\木剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\木剑.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\海魂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\海魂.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\炼狱.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\炼狱.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\短剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\短剑.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\破魂.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\破魂.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\祈祷之刃.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\祈祷之刃.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\罗刹.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\罗刹.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\落魄神兵.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\落魄神兵.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\血饮.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\血饮.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\裁决之杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\裁决之杖.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\赤血魔剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\赤血魔剑.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\逍遥扇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\逍遥扇.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\铁剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\铁剑.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\银蛇.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\银蛇.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\降魔.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\降魔.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\青铜剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\青铜剑.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\青铜斧.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\青铜斧.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\骨玉权杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\骨玉权杖.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\魔杖.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\魔杖.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\鹤嘴锄.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\鹤嘴锄.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\龙牙.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\龙牙.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\武器\\龙纹剑.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\武器\\龙纹剑.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\万年雪霜.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\万年雪霜.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\体力强效神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\体力强效神水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\修复油.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\修复油.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\地牢逃脱卷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\地牢逃脱卷.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\太阳水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\太阳水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\强效太阳水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\强效太阳水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\强效金创药.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\强效金创药.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\强效魔法药.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\强效魔法药.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\彩票.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\彩票.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\战神油.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\战神油.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\攻击神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\攻击神水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\特殊药水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\特殊药水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\疗伤药.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\疗伤药.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\疾风神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\疾风神水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\祝福油.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\祝福油.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\神水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\精神神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\精神神水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\苹果.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\苹果.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\行会回城卷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\行会回城卷.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\许中医的药1.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\许中医的药1.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\金创药(中量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\金创药(中量).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\金创药(小量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\金创药(小量).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\金矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\金矿.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\铁矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\铁矿.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\铜矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\铜矿.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\银矿.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\银矿.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\随机传送卷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\随机传送卷.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\魔力强效神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\魔力强效神水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\魔力神水.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\魔力神水.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\魔法药(中量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\魔法药(中量).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\魔法药(小量).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\魔法药(小量).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\鹿茸.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\鹿茸.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\鹿血.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\鹿血.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\消耗品\\黑铁矿石.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\消耗品\\黑铁矿石.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\中型盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\中型盔甲(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\中型盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\中型盔甲(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\圣战宝甲.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\圣战宝甲.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\天尊道袍.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\天尊道袍.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\天师长袍.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\天师长袍.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\天魔神甲.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\天魔神甲.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\布衣(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\布衣(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\布衣(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\布衣(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\幽灵战衣(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\幽灵战衣(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\幽灵战衣(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\幽灵战衣(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\恶魔长袍(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\恶魔长袍(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\恶魔长袍(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\恶魔长袍(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\战神盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\战神盔甲(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\战神盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\战神盔甲(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\法神披风.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\法神披风.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\灵魂战衣(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\灵魂战衣(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\灵魂战衣(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\灵魂战衣(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\轻型盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\轻型盔甲(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\轻型盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\轻型盔甲(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\重盔甲(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\重盔甲(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\重盔甲(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\重盔甲(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\霓裳羽衣.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\霓裳羽衣.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\魔法长袍(女).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\魔法长袍(女).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\防具\\魔法长袍(男).png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\防具\\魔法长袍(男).png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\传统项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\传统项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\凤凰明珠.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\凤凰明珠.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\圣战项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\圣战项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\天尊项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\天尊项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\天珠项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\天珠项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\幽灵项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\幽灵项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\恶魔铃铛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\恶魔铃铛.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\技巧项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\技巧项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\探测项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\探测项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\放大镜.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\放大镜.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\法神项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\法神项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\灯笼项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\灯笼项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\灵魂项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\灵魂项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\狂风项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\狂风项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\琥珀项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\琥珀项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\生命项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\生命项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\白色虎齿项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\白色虎齿项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\白金项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\白金项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\祈祷项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\祈祷项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\祈福项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\祈福项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\竹笛.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\竹笛.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\绿色项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\绿色项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\蓝翡翠项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\蓝翡翠项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\虹魔项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\虹魔项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\记忆项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\记忆项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\躲避手链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\躲避手链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\金项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\金项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\魔血项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\魔血项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\魔鬼项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\魔鬼项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\黄色水晶项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\黄色水晶项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\黑檀项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\黑檀项链.png',
   'DATA'),
  ('game\\assets\\images\\equipment\\项链\\黑色水晶项链.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\equipment\\项链\\黑色水晶项链.png',
   'DATA'),
  ('game\\assets\\images\\icon.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\icon.png',
   'DATA'),
  ('game\\assets\\images\\maps\\封魔谷.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\封魔谷.png',
   'DATA'),
  ('game\\assets\\images\\maps\\毒蛇山谷.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\毒蛇山谷.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\比奇省.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\比奇省.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\比奇矿区.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\比奇矿区.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\沃玛寺庙.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\沃玛寺庙.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\沃玛森林.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\沃玛森林.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\石墓.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\石墓.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\祖玛寺庙.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\祖玛寺庙.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\蜈蚣洞.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\蜈蚣洞.jpg',
   'DATA'),
  ('game\\assets\\images\\maps\\骷髅洞.jpg',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\maps\\骷髅洞.jpg',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\冰咆哮.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\冰咆哮.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\刺杀剑术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\刺杀剑术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\半月弯刀.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\半月弯刀.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\召唤神兽.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\召唤神兽.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\召唤骷髅.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\召唤骷髅.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\圣灵防御术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\圣灵防御术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\地狱雷光.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\地狱雷光.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\基本剑法.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\基本剑法.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\抗拒火环.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\抗拒火环.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\攻杀剑术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\攻杀剑术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\施毒术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\施毒术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\治愈术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\治愈术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\火墙.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\火墙.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\火球术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\火球术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\灵魂火符.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\灵魂火符.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\烈火剑法.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\烈火剑法.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\精神力战法.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\精神力战法.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\诱惑之光.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\诱惑之光.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\野蛮冲撞.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\野蛮冲撞.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\隐身术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\隐身术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\雷电术.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\雷电术.png',
   'DATA'),
  ('game\\assets\\images\\ui\\skill\\魔法盾.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\skill\\魔法盾.png',
   'DATA'),
  ('game\\assets\\images\\ui\\人物基底女.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\人物基底女.png',
   'DATA'),
  ('game\\assets\\images\\ui\\人物基底男.png',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets\\images\\ui\\人物基底男.png',
   'DATA'),
  ('game\\data\\__init__.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\__init__.py',
   'DATA'),
  ('game\\data\\__pycache__\\__init__.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('game\\data\\__pycache__\\equipment_loader.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\__pycache__\\equipment_loader.cpython-312.pyc',
   'DATA'),
  ('game\\data\\__pycache__\\inventory.cpython-312.pyc',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\__pycache__\\inventory.cpython-312.pyc',
   'DATA'),
  ('game\\data\\checkin_config.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\checkin_config.json',
   'DATA'),
  ('game\\data\\drop_rates.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\drop_rates.json',
   'DATA'),
  ('game\\data\\equipmengt.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\equipmengt.json',
   'DATA'),
  ('game\\data\\equipment_loader.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\equipment_loader.py',
   'DATA'),
  ('game\\data\\inventory.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\inventory.py',
   'DATA'),
  ('game\\data\\items_config.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\items_config.json',
   'DATA'),
  ('game\\data\\maps_config.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\maps_config.json',
   'DATA'),
  ('game\\data\\monster_distribution_config.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\monster_distribution_config.json',
   'DATA'),
  ('game\\data\\monsters.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\monsters.json',
   'DATA'),
  ('game\\data\\monsterskills.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\monsterskills.json',
   'DATA'),
  ('game\\data\\player.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\player.json',
   'DATA'),
  ('game\\data\\points_shop_config.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\points_shop_config.json',
   'DATA'),
  ('game\\data\\potion_config.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\potion_config.json',
   'DATA'),
  ('game\\data\\ranks.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\ranks.json',
   'DATA'),
  ('game\\data\\skills.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\skills.json',
   'DATA'),
  ('game\\data\\starter_equipment.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\starter_equipment.json',
   'DATA'),
  ('game\\data\\summons.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data\\summons.json',
   'DATA'),
  ('logs\\debug.log',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\logs\\debug.log',
   'DATA'),
  ('logs\\error.log',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\logs\\error.log',
   'DATA'),
  ('logs\\game.log',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\logs\\game.log',
   'DATA'),
  ('saves\\autosave.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\saves\\autosave.json',
   'DATA'),
  ('saves\\backups\\autosave_20250610_224233.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\saves\\backups\\autosave_20250610_224233.json',
   'DATA'),
  ('saves\\backups\\autosave_20250610_224236.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\saves\\backups\\autosave_20250610_224236.json',
   'DATA'),
  ('saves\\backups\\autosave_20250610_230208.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\saves\\backups\\autosave_20250610_230208.json',
   'DATA'),
  ('saves\\backups\\autosave_20250621_002926.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\saves\\backups\\autosave_20250621_002926.json',
   'DATA'),
  ('saves\\backups\\autosave_20250621_014314.json',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\saves\\backups\\autosave_20250621_014314.json',
   'DATA'),
  ('ui\\checkin_panel.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\ui\\checkin_panel.py',
   'DATA'),
  ('ui\\points_shop_panel.py',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\ui\\points_shop_panel.py',
   'DATA'),
  ('base_library.zip',
   'E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\build\\game_console\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\encoding\\cns11643.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\auto.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tk\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.5.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.5.tm',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tm.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('pygame\\freesansbold.ttf',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\freesansbold.ttf',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tk\\optMenu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tk\\palette.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tk\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tk\\safetk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tk\\msgbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\encoding\\iso8859-11.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tk\\console.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tk\\icons.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tk\\tk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\parray.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tk\\bgerror.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\clock.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tk\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('pygame\\pygame_icon.bmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pygame\\pygame_icon.bmp',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\safe.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('importlib_metadata-8.0.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tk\\focus.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tk\\listbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tk\\clrpick.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tk\\menu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tk\\comdlg.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\init.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tk\\text.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tk\\dialog.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tk\\tearoff.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\word.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\top_level.txt',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\LICENSE',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\METADATA',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\entry_points.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\REQUESTED',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\WHEEL',
   'DATA'),
  ('typeguard-4.3.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages\\setuptools\\_vendor\\typeguard-4.3.0.dist-info\\RECORD',
   'DATA')],
 False,
 False,
 False,
 [],
 None,
 None,
 None)
