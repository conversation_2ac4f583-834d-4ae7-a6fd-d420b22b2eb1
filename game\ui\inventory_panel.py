"""
背包面板
包含装备、道具、技能书等物品的管理界面
"""
import pygame
from game.ui.item_slot import ItemSlot
from game.utils.attribute_translator import convert_equipment_attributes_to_chinese
from game.systems.item_generator import Item
from game.data.equipment_converter import EquipmentConverter
from .ui_panel import UIPanel

class InventoryPanel(UIPanel):
    """背包面板类，用于显示和管理背包物品"""
    
    # 配置常量
    REPAIR_COST_PER_DURABILITY = 5  # 每点耐久度修理费用
    MAX_INPUT_LENGTH = 10  # 数量输入框最大长度
    GRID_COLS = 8  # 网格列数
    GRID_ROWS = 5  # 网格行数
    GRID_SPACING = 2  # 网格间距
    
    def __init__(self, screen, player, inventory_manager, position=(0, 0), size=(600, 500)):
        """
        初始化背包面板
        
        Args:
            screen: 屏幕对象
            player: 玩家对象
            inventory_manager: 背包管理器
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
        """
        super().__init__(screen, position, size)
        self.inventory_manager = inventory_manager
        self.player = player
        self.equipment_manager = self.player.equipment_manager
        
        # 面板可见性
        self.visible = False
        
        # 当前选中的标签页
        self.current_tab = "equipment"  # "equipment", "consumable", "skill_book"
        
        # 当前选中的物品格子索引
        self.selected_slot_idx = None
        
        # 标签页区域
        tab_height = 30
        self.tab_area = pygame.Rect(
            self.rect.left, 
            self.rect.top, 
            self.rect.width, 
            tab_height
        )
        
        # 标签页按钮
        tab_width = 80
        self.equipment_tab_rect = pygame.Rect(
            self.tab_area.left + 10, 
            self.tab_area.top + 5, 
            tab_width, 
            tab_height - 10
        )
        
        self.consumable_tab_rect = pygame.Rect(
            self.equipment_tab_rect.right + 10, 
            self.tab_area.top + 5, 
            tab_width, 
            tab_height - 10
        )
        
        self.skill_book_tab_rect = pygame.Rect(
            self.consumable_tab_rect.right + 10, 
            self.tab_area.top + 5, 
            tab_width, 
            tab_height - 10
        )
        
        # 页码显示区域
        self.page_display_rect = pygame.Rect(
            self.skill_book_tab_rect.right + 20, 
            self.tab_area.top + 5, 
            50, 
            tab_height - 10
        )
        
        # 关闭按钮
        close_button_size = 20
        self.close_button_rect = pygame.Rect(
            self.rect.right - close_button_size - 5, 
            self.rect.top + 5, 
            close_button_size, 
            close_button_size
        )
        
        # 计算网格格子大小和间距
        self.grid_cols = self.GRID_COLS
        self.grid_rows = self.GRID_ROWS
        self.grid_spacing = self.GRID_SPACING  # 减小间距让更多空间给格子
        
        # 计算可用的网格区域
        grid_margin_top = 10
        grid_margin_bottom = 80  # 为底部按钮留出更多空间
        available_width = self.rect.width - 20  # 左右各留10px边距
        available_height = self.rect.height - tab_height - grid_margin_top - grid_margin_bottom
        
        # 根据可用空间计算最大可能的格子大小
        max_width_per_slot = (available_width - (self.grid_cols - 1) * self.grid_spacing) // self.grid_cols
        max_height_per_slot = (available_height - (self.grid_rows - 1) * self.grid_spacing) // self.grid_rows
        
        # 选择较小的值确保格子是正方形，并且不会超出边界
        slot_size = min(max_width_per_slot, max_height_per_slot)
        # 确保有最小大小
        slot_size = max(slot_size, 40)  # 最小40像素
        
        self.slot_width = slot_size
        self.slot_height = slot_size
        
        # 重新计算网格区域以适应格子大小
        required_width = self.grid_cols * self.slot_width + (self.grid_cols - 1) * self.grid_spacing
        required_height = self.grid_rows * self.slot_height + (self.grid_rows - 1) * self.grid_spacing
        
        # 调整网格区域位置，使其居中
        grid_start_x = self.rect.left + (self.rect.width - required_width) // 2
        grid_start_y = self.tab_area.bottom + grid_margin_top
        
        self.grid_area = pygame.Rect(
            grid_start_x,
            grid_start_y,
            required_width,
            required_height
        )
        
        # 物品格子实例
        self.item_slots = {}
        
        # 底部按钮区域
        button_height = 30
        self.button_area = pygame.Rect(
            self.rect.left + 10,
            self.grid_area.bottom + 10,
            self.rect.width - 20,
            button_height
        )
        
        # 底部按钮
        button_width = 60
        button_spacing = 5
        button_y = self.button_area.top
        
        self.sell_button_rect = pygame.Rect(
            self.button_area.left,
            button_y,
            button_width,
            button_height
        )
        
        self.auction_button_rect = pygame.Rect(
            self.sell_button_rect.right + button_spacing,
            button_y,
            button_width,
            button_height
        )
        
        self.batch_sell_button_rect = pygame.Rect(
            self.auction_button_rect.right + button_spacing,
            button_y,
            button_width,
            button_height
        )
        
        self.store_button_rect = pygame.Rect(
            self.batch_sell_button_rect.right + button_spacing,
            button_y,
            button_width,
            button_height
        )
        
        self.view_button_rect = pygame.Rect(
            self.store_button_rect.right + button_spacing,
            button_y,
            button_width,
            button_height
        )
        
        self.lock_button_rect = pygame.Rect(
            self.view_button_rect.right + button_spacing,
            button_y,
            button_width,
            button_height
        )
        
        # 修理按钮
        self.repair_button_rect = pygame.Rect(
            self.lock_button_rect.right + button_spacing,
            button_y,
            button_width,
            button_height
        )
        
        # 翻页按钮位于右侧
        page_button_width = 25
        
        self.prev_page_button_rect = pygame.Rect(
            self.button_area.right - page_button_width * 2 - button_spacing,
            button_y,
            page_button_width,
            button_height
        )
        
        self.next_page_button_rect = pygame.Rect(
            self.button_area.right - page_button_width,
            button_y,
            page_button_width,
            button_height
        )
        
        # 当前页码
        self.current_page = 0
        
        # 加载字体
        self.title_font = pygame.font.SysFont("SimHei", 16)
        self.button_font = pygame.font.SysFont("SimHei", 14)
        self.page_font = pygame.font.SysFont("SimHei", 14)
        
        # 颜色
        self.background_color = (50, 50, 50)
        self.title_color = (220, 220, 220)
        self.tab_color = (70, 70, 70)
        self.tab_selected_color = (100, 100, 100)
        self.button_color = (70, 70, 70)
        self.button_hover_color = (90, 90, 90)
        self.button_text_color = (220, 220, 220)
        self.close_button_color = (180, 70, 70)
        
        # 输入框（用于批量使用道具）
        self.quantity_input_rect = pygame.Rect(
            self.page_display_rect.left,
            self.page_display_rect.top,
            80,
            self.page_display_rect.height
        )
        self.quantity_input_active = False
        self.quantity_input_text = ""
        self.quantity_input_visible = False
        
        # 仓库面板引用
        self.warehouse_panel = None
        
        # 确认对话框
        self.confirm_dialog = ConfirmDialog(screen, "")
    
    def set_warehouse_panel(self, warehouse_panel):
        """设置仓库面板引用"""
        self.warehouse_panel = warehouse_panel
    
    def _get_player(self):
        """统一的玩家对象获取方法"""
        if hasattr(self.inventory_manager, 'player_ref') and self.inventory_manager.player_ref:
            return self.inventory_manager.player_ref
        elif hasattr(self, 'player') and self.player:
            return self.player
        else:
            print("⚠️ 无法获取玩家对象")
            return None
    
    def toggle_visibility(self):
        """切换可见性"""
        self.visible = not self.visible
        if self.visible:
            # 初始化物品格子
            self._init_item_slots()
            # 加载当前页的物品
            self._load_current_page_items()
    
    def _init_item_slots(self):
        """初始化物品格子"""
        self.item_slots = {}
        
        for row in range(self.grid_rows):
            for col in range(self.grid_cols):
                # 计算格子位置
                x = self.grid_area.left + col * (self.slot_width + self.grid_spacing)
                y = self.grid_area.top + row * (self.slot_height + self.grid_spacing)
                
                # 计算格子索引
                slot_idx = row * self.grid_cols + col
                
                # 创建物品格子
                self.item_slots[slot_idx] = ItemSlot(
                    self.screen, 
                    (x, y), 
                    (self.slot_width, self.slot_height)
                )
    
    def _load_current_page_items(self):
        """加载当前页的物品到格子中"""
        # 获取当前容器类型
        container_type = self.current_tab
        
        # 获取当前页的物品
        page_items = self.inventory_manager.get_items_by_page(self.current_page, container_type)
        
        # 清空所有格子
        for slot in self.item_slots.values():
            slot.clear_item()
        
        # 填充物品 - 修复：正确处理字典数据
        slot_index = 0
        for item_data in page_items.values():  # 使用 .values() 获取物品数据
            if slot_index >= len(self.item_slots):
                break
                
            try:
                # 处理不同的数据格式
                if isinstance(item_data, tuple):
                    # 对于消耗品和技能书，数据格式为 (item, quantity)
                    actual_item, quantity = item_data
                    item_dict = actual_item if isinstance(actual_item, dict) else actual_item.to_dict()
                    item_dict['quantity'] = quantity
                else:
                    # 对于装备，直接是物品数据
                    item_dict = item_data if isinstance(item_data, dict) else item_data.to_dict()
                
                # 标准化字典
                standardized_dict = EquipmentConverter.standardize_item_dict(item_dict)
                
                # 从标准化后的字典创建Item对象
                item = Item.from_dict(standardized_dict)
                
                # 设置物品到格子
                self.item_slots[slot_index].set_item(item, item.quantity)
                slot_index += 1
                
            except Exception as e:
                print(f"❌ 加载物品失败: {e}, 物品数据: {item_data}")
                continue
    
    def _get_current_container_page(self):
        """获取当前容器的页码"""
        if self.current_tab == "equipment":
            return self.inventory_manager.current_equipment_page
        elif self.current_tab == "consumable":
            return self.inventory_manager.current_consumable_page
        elif self.current_tab == "skill_book":
            return self.inventory_manager.current_skill_book_page
        return 0
    
    def _set_current_container_page(self, page):
        """设置当前容器的页码"""
        if self.current_tab == "equipment":
            self.inventory_manager.current_equipment_page = page
        elif self.current_tab == "consumable":
            self.inventory_manager.current_consumable_page = page
        elif self.current_tab == "skill_book":
            self.inventory_manager.current_skill_book_page = page
    
    def _get_current_container_total_pages(self):
        """获取当前容器的总页数"""
        return self.inventory_manager.get_total_pages(self.current_tab)
    
    def _is_valid_page(self, page):
        """检查页码是否有效"""
        if page < 0:
            return False
        total_pages = self._get_current_container_total_pages()
        return page < total_pages
    
    def _draw_panel_background(self):
        """绘制面板背景和边框"""
        pygame.draw.rect(self.screen, self.background_color, self.rect)
        pygame.draw.rect(self.screen, (100, 100, 100), self.rect, 2)

    def _draw_tabs(self):
        """绘制标签页"""
        pygame.draw.rect(self.screen, (70, 70, 70), self.tab_area)
        
        # 装备标签
        tab_color = self.tab_selected_color if self.current_tab == "equipment" else self.tab_color
        pygame.draw.rect(self.screen, tab_color, self.equipment_tab_rect)
        equipment_text = self.title_font.render("装备", True, self.title_color)
        equipment_text_rect = equipment_text.get_rect(center=self.equipment_tab_rect.center)
        self.screen.blit(equipment_text, equipment_text_rect)

        # 道具标签
        tab_color = self.tab_selected_color if self.current_tab == "consumable" else self.tab_color
        pygame.draw.rect(self.screen, tab_color, self.consumable_tab_rect)
        consumable_text = self.title_font.render("道具", True, self.title_color)
        consumable_text_rect = consumable_text.get_rect(center=self.consumable_tab_rect.center)
        self.screen.blit(consumable_text, consumable_text_rect)
        
        # 技能书标签
        tab_color = self.tab_selected_color if self.current_tab == "skill_book" else self.tab_color
        pygame.draw.rect(self.screen, tab_color, self.skill_book_tab_rect)
        skill_book_text = self.title_font.render("技能书", True, self.title_color)
        skill_book_text_rect = skill_book_text.get_rect(center=self.skill_book_tab_rect.center)
        self.screen.blit(skill_book_text, skill_book_text_rect)

    def _draw_close_button(self):
        """绘制关闭按钮"""
        pygame.draw.rect(self.screen, self.close_button_color, self.close_button_rect)
        close_text = self.title_font.render("X", True, (255, 255, 255))
        close_text_rect = close_text.get_rect(center=self.close_button_rect.center)
        self.screen.blit(close_text, close_text_rect)

    def _draw_page_number(self):
        """绘制页码或数量输入框"""
        if not self.quantity_input_visible:
            page_text = f"{self.current_page + 1}"
            page_surface = self.page_font.render(page_text, True, self.title_color)
            page_rect = page_surface.get_rect(center=self.page_display_rect.center)
            self.screen.blit(page_surface, page_rect)
        else:
            pygame.draw.rect(self.screen, (30, 30, 30), self.quantity_input_rect)
            pygame.draw.rect(self.screen, (150, 150, 150), self.quantity_input_rect, 1)
            
            input_text = self.quantity_input_text or "数量"
            input_surface = self.page_font.render(input_text, True, (200, 200, 200))
            input_rect = input_surface.get_rect(center=self.quantity_input_rect.center)
            self.screen.blit(input_surface, input_rect)

    def _draw_buttons(self):
        """绘制底部所有按钮"""
        pygame.draw.rect(self.screen, (60, 60, 60), self.button_area)
        
        buttons_to_draw = {
            "出售": self.sell_button_rect, "拍卖": self.auction_button_rect,
            "批售": self.batch_sell_button_rect, "存仓": self.store_button_rect,
            "整理": self.view_button_rect, "锁定": self.lock_button_rect
        }

        for text, rect in buttons_to_draw.items():
            pygame.draw.rect(self.screen, self.button_color, rect)
            text_surf = self.button_font.render(text, True, self.button_text_color)
            text_rect = text_surf.get_rect(center=rect.center)
            self.screen.blit(text_surf, text_rect)

        if self.current_tab == "equipment":
            pygame.draw.rect(self.screen, self.button_color, self.repair_button_rect)
            repair_text = self.button_font.render("修理", True, self.button_text_color)
            repair_text_rect = repair_text.get_rect(center=self.repair_button_rect.center)
            self.screen.blit(repair_text, repair_text_rect)
            
        pygame.draw.rect(self.screen, self.button_color, self.prev_page_button_rect)
        prev_text = self.button_font.render("<<", True, self.button_text_color)
        prev_text_rect = prev_text.get_rect(center=self.prev_page_button_rect.center)
        self.screen.blit(prev_text, prev_text_rect)
        
        pygame.draw.rect(self.screen, self.button_color, self.next_page_button_rect)
        next_text = self.button_font.render(">>", True, self.button_text_color)
        next_text_rect = next_text.get_rect(center=self.next_page_button_rect.center)
        self.screen.blit(next_text, next_text_rect)

    def render(self):
        """渲染整个背包面板"""
        if not self.visible:
            return

        # 绘制面板背景和边框
        self._draw_panel_background()

        # 渲染标签页
        self._draw_tabs()

        # 渲染关闭按钮
        self._draw_close_button()
        
        # 渲染底部按钮
        self._draw_buttons()
        
        # 渲染页码
        self._draw_page_number()

        # 渲染物品格子
        pygame.draw.rect(self.screen, (60, 60, 60), self.grid_area)
        for idx, slot in self.item_slots.items():
            slot.render()

        # 浮窗渲染（在所有格子渲染完之后）
        mouse_pos = pygame.mouse.get_pos()
        for idx, slot in self.item_slots.items():
            # 如果鼠标悬停在格子上，渲染提示信息
            if slot.rect.collidepoint(mouse_pos) and slot.item:
                comparison_item = None
                item_category = getattr(slot.item, 'category', None)

                # 如果是装备，则查找身上穿的同类装备进行对比
                if item_category and self.equipment_manager:
                    slot_to_check = item_category
                    # 特殊处理可以佩戴多个的装备
                    if item_category == '手镯':
                        # 默认对比第一个手镯槽位
                        slot_to_check = '手镯1'
                    elif item_category == '戒指':
                        # 默认对比第一个戒指槽位
                        slot_to_check = '戒指1'

                    # 使用正确的方法名 get_equipped_item
                    comparison_item = self.equipment_manager.get_equipped_item(slot_to_check)

                slot.render_tooltip(mouse_pos, comparison_item)
                # 只渲染一个浮窗就够了
                break

        # 渲染确认对话框
        if self.confirm_dialog.visible:
            self.confirm_dialog.render()

    def handle_event(self, event):
        """处理用户输入事件"""
        if not self.visible:
            return False
        
        # 优先处理确认对话框事件
        if self.confirm_dialog.visible:
            return self.confirm_dialog.handle_event(event)

        if event.type == pygame.MOUSEBUTTONDOWN:
            # 先检查是否点击在面板区域内
            if self.rect.collidepoint(event.pos):
                # 在面板内部的点击
                if event.button == 1:  # 左键
                    # 关闭按钮
                    if self.close_button_rect.collidepoint(event.pos):
                        self.visible = False
                        return True
                        
                    # 标签页按钮
                    if self.equipment_tab_rect.collidepoint(event.pos):
                        self.current_tab = "equipment"
                        self.current_page = self._get_current_container_page()
                        self._load_current_page_items()
                        return True
                        
                    elif self.consumable_tab_rect.collidepoint(event.pos):
                        self.current_tab = "consumable"
                        self.current_page = self._get_current_container_page()
                        self._load_current_page_items()
                        return True
                        
                    elif self.skill_book_tab_rect.collidepoint(event.pos):
                        self.current_tab = "skill_book"
                        self.current_page = self._get_current_container_page()
                        self._load_current_page_items()
                        return True
                        
                    # 翻页按钮
                    elif self.prev_page_button_rect.collidepoint(event.pos):
                        # 上一页
                        new_page = self.current_page - 1
                        if self._is_valid_page(new_page):
                            self.current_page = new_page
                            self._set_current_container_page(self.current_page)
                            self._load_current_page_items()
                        return True
                        
                    elif self.next_page_button_rect.collidepoint(event.pos):
                        # 下一页
                        new_page = self.current_page + 1
                        if self._is_valid_page(new_page):
                            self.current_page = new_page
                            self._set_current_container_page(self.current_page)
                            self._load_current_page_items()
                        return True
                        
                    # 底部按钮
                    elif self.sell_button_rect.collidepoint(event.pos):
                        self._on_sell_button_click()
                        return True
                        
                    elif self.auction_button_rect.collidepoint(event.pos):
                        self._on_auction_button_click()
                        return True
                        
                    elif self.batch_sell_button_rect.collidepoint(event.pos):
                        self._on_batch_sell_button_click()
                        return True
                        
                    elif self.store_button_rect.collidepoint(event.pos):
                        self._on_store_button_click()
                        return True
                        
                    elif self.view_button_rect.collidepoint(event.pos):
                        self._on_sort_button_click()
                        return True
                        
                    elif self.lock_button_rect.collidepoint(event.pos):
                        self._on_lock_button_click()
                        return True
                        
                    elif self.repair_button_rect.collidepoint(event.pos):
                        self._on_repair_button_click()
                        return True
                        
                    # 数量输入框
                    elif self.quantity_input_visible and self.quantity_input_rect.collidepoint(event.pos):
                        self.quantity_input_active = True
                        return True
                    else:
                        self.quantity_input_active = False
                        
                    # 物品格子
                    for slot_idx, slot in self.item_slots.items():
                        if slot.rect.collidepoint(event.pos):
                            # 先清除所有其他槽的选中状态
                            for other_idx, other_slot in self.item_slots.items():
                                if other_idx != slot_idx:
                                    other_slot.selected = False

                            # 切换当前槽的选中状态
                            slot.toggle_selected()

                            # 记录选中的格子索引
                            if slot.selected:
                                self.selected_slot_idx = slot_idx
                            else:
                                self.selected_slot_idx = None
                            return True
                
                # 右键点击
                elif event.button == 3:
                    for slot_idx, slot in self.item_slots.items():
                        if slot.rect.collidepoint(event.pos) and slot.item:
                            # 先清除所有其他槽的选中状态
                            for other_idx, other_slot in self.item_slots.items():
                                if other_idx != slot_idx:
                                    other_slot.selected = False

                            # 选中当前槽
                            slot.selected = True
                            self.selected_slot_idx = slot_idx

                            if self.current_tab == "equipment" and hasattr(slot.item, 'type') and slot.item.type == "equipment":
                                # 右键装备
                                self._equip_item_directly(slot_idx)
                                return True
                            elif self.current_tab == "consumable" and hasattr(slot.item, 'type') and slot.item.type == "consumable":
                                # 显示数量输入框
                                self.quantity_input_visible = True
                                self.quantity_input_active = True
                                self.quantity_input_text = ""
                                return True
                
                # 在面板内的任何点击都应该被消费，防止穿透
                return True
            else:
                # 点击在面板外部，不处理但也不关闭面板（由UIManager处理）
                return False
                    
        # 处理键盘事件（用于数量输入）
        elif event.type == pygame.KEYDOWN and self.quantity_input_visible and self.quantity_input_active:
            if event.key == pygame.K_RETURN:
                # 确认输入
                self.quantity_input_active = False
                
            elif event.key == pygame.K_BACKSPACE:
                # 退格键
                self.quantity_input_text = self.quantity_input_text[:-1]
                
            else:
                # 只允许输入数字，并限制长度
                if event.unicode.isdigit() and len(self.quantity_input_text) < self.MAX_INPUT_LENGTH:
                    self.quantity_input_text += event.unicode
            return True
            
        return False
    
    def update(self):
        """更新背包面板状态"""
        if not self.visible:
            return
    
    def _on_sell_button_click(self):
        """处理出售按钮点击"""
        if self.selected_slot_idx is not None:
            slot = self.item_slots.get(self.selected_slot_idx)
            if slot and slot.item:
                if not slot.locked:  # 锁定的物品不能出售
                    # 计算出售价格
                    sell_price = self._calculate_sell_price(slot.item)
                    item_name = slot.item.name
                    
                    # 显示确认对话框
                    message = f"确认出售 {item_name} ？\n将获得 {sell_price} 金币"
                    self.confirm_dialog.message = message
                    self.confirm_dialog.on_confirm = lambda: self._confirm_sell_item(self.selected_slot_idx)
                    self.confirm_dialog.on_cancel = None
                    self.confirm_dialog.show()
                else:
                    print(f"物品已锁定，无法出售: {slot.item.name}")
        else:
            print("请先选择要出售的物品")
    
    def _confirm_sell_item(self, slot_idx):
        """确认出售物品"""
        slot = self.item_slots.get(slot_idx)
        if slot and slot.item:
            sell_price = self._calculate_sell_price(slot.item)
            item_name = slot.item.name
            
            # 🔧 修复：获取玩家对象
            player = self._get_player()
            if not player:
                print("❌ 无法获取玩家对象，出售失败")
                return
            
            # 🔧 修复：优先从Player的真实背包移除物品
            player_removed = False
            if hasattr(player, 'inventory_manager') and player.inventory_manager:
                if hasattr(player.inventory_manager, 'remove_item'):
                    player_removed = player.inventory_manager.remove_item(item_name, 1)
                    print(f"🎯 从Player背包移除物品: {item_name} - {'成功' if player_removed else '失败'}")
            
            # 🔧 修复：从UI背包管理器移除物品
            global_slot_idx = self.current_page * self.inventory_manager.slots_per_page + slot_idx
            if hasattr(self.inventory_manager, 'remove_item_ui'):
                ui_removed_item, ui_removed_quantity = self.inventory_manager.remove_item_ui(global_slot_idx, 1, self.current_tab)
            else:
                ui_removed_item, ui_removed_quantity = self.inventory_manager.remove_item_by_slot(global_slot_idx, 1, self.current_tab)
            
            # 🔧 新增：添加金币到玩家账户
            if hasattr(player, 'currencies') and 'gold' in player.currencies:
                player.currencies['gold'] += sell_price
                print(f"💰 金币增加 {sell_price}，当前金币: {player.currencies['gold']}")
            elif hasattr(player, 'gold'):
                player.gold += sell_price
                print(f"💰 金币增加 {sell_price}，当前金币: {player.gold}")
            
            # 🔧 新增：立即保存游戏到存档
            try:
                if hasattr(player, 'save_game'):
                    save_success = player.save_game("autosave")
                else:
                    # 使用SaveLoadManager保存
                    from game.managers.save_load_manager import SaveLoadManager
                    save_manager = SaveLoadManager()
                    save_success = save_manager.save_player_data(player, "autosave")
                
                if save_success:
                    print(f"✅ 成功出售 {item_name}，获得 {sell_price} 金币，游戏已自动保存")
                else:
                    print(f"⚠️ 物品出售成功但保存失败，请手动保存游戏")
            except Exception as e:
                print(f"⚠️ 自动保存失败: {e}")
            
            # 重新加载当前页
            self._load_current_page_items()
    
    def _calculate_sell_price(self, item):
        """计算物品出售价格"""
        try:
            base_price = 1  # 默认价格
            
            # 支持字典和Item对象两种格式
            def get_item_attr(attr_name, default=None):
                if hasattr(item, attr_name):
                    return getattr(item, attr_name)
                elif isinstance(item, dict):
                    return item.get(attr_name, default)
                return default
            
            # 根据物品属性计算价格
            sell_price = get_item_attr('sell_price')
            if sell_price and sell_price > 0:
                return sell_price
                
            price = get_item_attr('price')
            if price and price > 0:
                return max(1, price // 2)  # 出售价格为购买价格的一半
                
            tier = get_item_attr('tier')
            if tier:
                # 根据品质等级计算价格
                tier_multiplier = {
                    'common': 1,
                    'rare': 5,
                    'epic': 20,
                    'legendary': 100
                }
                return base_price * tier_multiplier.get(tier, 1)
            
            return base_price
        except Exception as e:
            print(f"⚠️ 计算物品价格时出错: {e}")
            return 1
    
    def _on_auction_button_click(self):
        """处理拍卖按钮点击"""
        if self.selected_slot_idx is not None:
            slot = self.item_slots.get(self.selected_slot_idx)
            if slot and slot.item:
                if not slot.locked:  # 锁定的物品不能拍卖
                    print(f"拍卖物品: {slot.item.name}")
                else:
                    print(f"物品已锁定，无法拍卖: {slot.item.name}")
    
    def _on_batch_sell_button_click(self):
        """处理批量出售按钮点击"""
        # 获取当前容器中所有物品
        sellable_items = []
        
        # 获取对应的容器
        if self.current_tab == "equipment":
            container = self.inventory_manager.equipment_items
        elif self.current_tab == "consumable":
            container = self.inventory_manager.consumable_items
        elif self.current_tab == "skill_book":
            container = self.inventory_manager.skill_book_items
        else:
            print("未知的容器类型")
            return
        
        # 遍历当前页的物品槽
        for local_slot_idx, slot in self.item_slots.items():
            if slot.item and not slot.locked:  # 只计算未锁定的物品
                # 计算全局索引
                global_slot_idx = self.current_page * self.inventory_manager.slots_per_page + local_slot_idx
                if global_slot_idx in container:
                    if self.current_tab == "equipment":
                        sellable_items.append((global_slot_idx, slot.item))
                    else:
                        item, quantity = container[global_slot_idx]
                        sellable_items.append((global_slot_idx, item))
        
        if not sellable_items:
            print("没有可出售的物品（可能都已锁定）")
            return
        
        # 计算总价值
        total_price = sum(self._calculate_sell_price(item) for _, item in sellable_items)
        total_count = len(sellable_items)
        
        # 显示确认对话框
        message = f"确认批量出售当前页 {total_count} 件物品？\n将获得 {total_price} 金币\n（已锁定的物品不会被出售）"
        self.confirm_dialog.message = message
        self.confirm_dialog.on_confirm = lambda: self._confirm_batch_sell(sellable_items)
        self.confirm_dialog.on_cancel = None
        self.confirm_dialog.show()
    
    def _confirm_batch_sell(self, sellable_items):
        """确认批量出售"""
        # 🔧 修复：获取玩家对象
        player = self._get_player()
        if not player:
            print("❌ 无法获取玩家对象，批量出售失败")
            return
            
        total_price = 0
        sold_count = 0
        
        # 从后往前删除，避免索引变化问题
        for global_slot_idx, item in reversed(sellable_items):
            sell_price = self._calculate_sell_price(item)
            total_price += sell_price
            sold_count += 1
            
            # 🔧 修复：从UI背包管理器移除物品
            if hasattr(self.inventory_manager, 'remove_item_ui'):
                ui_removed_item, ui_removed_quantity = self.inventory_manager.remove_item_ui(global_slot_idx, 1, self.current_tab)
            else:
                ui_removed_item, ui_removed_quantity = self.inventory_manager.remove_item_by_slot(global_slot_idx, 1, self.current_tab)
        
            # 🔧 修复：同时从Player的真实背包移除物品（如果存在）
            if hasattr(player, 'inventory_manager') and player.inventory_manager:
                # 安全获取物品名称，支持字典和Item对象两种格式
                if hasattr(item, 'name'):
                    item_name = item.name
                elif isinstance(item, dict) and 'name' in item:
                    item_name = item['name']
                else:
                    item_name = str(item)  # 兜底方案
                
                player.inventory_manager.remove_item(item_name, 1)
        
        # 🔧 新增：添加金币到玩家账户
        if hasattr(player, 'currencies') and 'gold' in player.currencies:
            player.currencies['gold'] += total_price
            print(f"💰 金币增加 {total_price}，当前金币: {player.currencies['gold']}")
        elif hasattr(player, 'gold'):
            player.gold += total_price
            print(f"💰 金币增加 {total_price}，当前金币: {player.gold}")
        
        # 🔧 新增：立即保存游戏到存档
        try:
            if hasattr(player, 'save_game'):
                save_success = player.save_game("autosave")
            else:
                # 使用SaveLoadManager保存
                from game.managers.save_load_manager import SaveLoadManager
                save_manager = SaveLoadManager()
                save_success = save_manager.save_player_data(player, "autosave")
            
            if save_success:
                print(f"✅ 批量出售成功：售出 {sold_count} 件物品，获得 {total_price} 金币，游戏已自动保存")
            else:
                print(f"⚠️ 物品出售成功但保存失败，请手动保存游戏")
        except Exception as e:
            print(f"⚠️ 自动保存失败: {e}")
        
        # 重新加载当前页
        self._load_current_page_items()
    
    def _on_store_button_click(self):
        """处理存仓按钮点击"""
        if self.selected_slot_idx is not None and self.warehouse_panel:
            slot = self.item_slots.get(self.selected_slot_idx)
            if slot and slot.item:
                if not slot.locked:  # 锁定的物品不能存仓
                    item_name = slot.item.name
                    
                    # 显示确认对话框
                    message = f"确认将 {item_name} 存入仓库？"
                    self.confirm_dialog.message = message
                    self.confirm_dialog.on_confirm = lambda: self._confirm_store_item(self.selected_slot_idx)
                    self.confirm_dialog.on_cancel = None
                    self.confirm_dialog.show()
                else:
                    print(f"物品已锁定，无法存仓: {slot.item.name}")
        else:
            if not self.warehouse_panel:
                print("仓库功能未启用")
            else:
                print("请先选择要存仓的物品")
    
    def _confirm_store_item(self, slot_idx):
        """确认存仓物品"""
        slot = self.item_slots.get(slot_idx)
        if slot and slot.item:
            item_name = slot.item.name
            
            # 移动物品到仓库
            global_slot_idx = self.current_page * self.inventory_manager.slots_per_page + slot_idx
            success = self.inventory_manager.move_item(
                global_slot_idx, 
                "warehouse", 
                self.current_tab
            )
            
            if success:
                print(f"物品 {item_name} 已成功存入仓库")
                # 重新加载当前页
                self._load_current_page_items()
                # 如果仓库面板可见，也重新加载仓库物品
                if self.warehouse_panel.visible:
                    self.warehouse_panel._load_current_page_items()
            else:
                print(f"无法存入仓库，仓库可能已满")
    
    def _on_sort_button_click(self):
        """处理整理按钮点击"""
        print("开始整理背包物品")
        
        # 显示确认对话框
        message = f"确认整理当前标签页的物品？\n将按类型和品质重新排序"
        self.confirm_dialog.message = message
        self.confirm_dialog.on_confirm = lambda: self._confirm_sort_inventory()
        self.confirm_dialog.on_cancel = None
        self.confirm_dialog.show()
    
    def _confirm_sort_inventory(self):
        """确认整理背包"""
        # 🔧 修复：直接操作主背包数据，而不是UI容器
        
        # 根据当前标签页过滤物品
        items_to_sort = []
        
        for item in self.inventory_manager.inventory:
            # 获取物品类型
            item_type = getattr(item, 'type', 'consumable') if hasattr(item, 'type') else item.get('type', 'consumable')
            
            # 根据当前标签页过滤
            should_include = False
            if self.current_tab == "equipment":
                should_include = item_type in ['weapon', 'armor', 'helmet', 'ring', 'necklace', 'equipment', '装备', '武器', '防具', '头盔', '戒指', '项链']
            elif self.current_tab == "consumable":
                should_include = item_type in ['consumable', 'potion', '消耗品', '道具']
            elif self.current_tab == "skill_book":
                should_include = item_type in ['skill_book', '技能书']
            
            if should_include:
                items_to_sort.append(item)
        
        if not items_to_sort:
            print(f"当前标签页({self.current_tab})没有物品需要整理")
            return
        
        # 定义物品类型的排序顺序
        type_order = {
            "消耗品": 1, "potion": 1, "consumable": 1, "道具": 1,
            "特殊物品": 2, "special": 2,
            "勋章": 3, "medal": 3,
            "武器": 4, "weapon": 4,
            "防具": 5, "armor": 5, "装备": 5,
            "头盔": 6, "helmet": 6,
            "手镯": 7, "bracelet": 7,
            "戒指": 8, "ring": 8,
            "项链": 9, "necklace": 9,
            "技能书": 10, "skill_book": 10
        }

        # 定义品质的排序顺序
        quality_order = {
            "传说": 1, "legendary": 1,
            "史诗": 2, "epic": 2,
            "稀有": 3, "rare": 3,
            "精良": 4, "uncommon": 4,
            "普通": 5, "common": 5
        }

        # 排序函数
        def sort_key(item):
            # 检查物品类型，支持字典和对象两种格式
            if isinstance(item, dict):
                item_type = item.get("type", "未知")
                item_name = item.get("name", "")
                # 获取物品品质，先尝试从quality属性读取，如果不存在则尝试从tier属性读取
                item_quality = item.get("quality", item.get("tier", "普通"))
            else:
                # 对象格式（Item对象）
                item_type = getattr(item, 'type', 'consumable')
                item_name = getattr(item, 'name', '')
                # Item对象使用rarity属性
                item_quality = getattr(item, 'rarity', 'common')

            # 获取物品类型的排序顺序，未知类型排在最后
            type_priority = type_order.get(item_type, 100)

            # 获取品质的排序顺序，未知品质排在最后
            quality_priority = quality_order.get(item_quality, 999)

            # 返回排序键：(类型优先级, 名称, 品质优先级, 品质名称)
            # 这样会先按类型排序，然后同类型的按名称排序，同名物品再按品质排序（品质高的排前面）
            return (type_priority, item_name, quality_priority, item_quality)
        
        # 排序当前标签页的物品
        items_to_sort.sort(key=sort_key)
        
        # 🔧 修复：从主背包中移除要排序的物品，然后按新顺序重新添加
        
        # 先移除所有要排序的物品
        for item in items_to_sort:
            item_name = getattr(item, 'name', '')
            if item_name:
                self.inventory_manager.remove_item(item_name, 1)
        
        # 按新顺序重新添加物品
        for item in items_to_sort:
            self.inventory_manager.add_item(item, 1)
        
        print(f"整理完成：重新排序了 {len(items_to_sort)} 件{self.current_tab}物品")
        
        # 重新加载当前页（这会触发UI容器同步）
        self._load_current_page_items()
    
    def _on_lock_button_click(self):
        """处理锁定按钮点击"""
        if self.selected_slot_idx is not None:
            slot = self.item_slots.get(self.selected_slot_idx)
            if slot and slot.item:
                # 切换锁定状态
                slot.locked = not slot.locked
                lock_status = "锁定" if slot.locked else "解锁"
                print(f"{lock_status}物品: {slot.item.name}")
            else:
                print("请选择要锁定的物品")
        else:
            print("请先选择要锁定的物品")
    
    def _equip_item_directly(self, slot_idx):
        """右键直接装备物品"""
        slot = self.item_slots.get(slot_idx)
        if slot and slot.item and self.current_tab == "equipment":
            if not slot.locked:  # 锁定的物品不能装备
                print(f"尝试装备物品: {slot.item.name}")
                
                # 获取玩家对象
                player = self._get_player()
                
                if player and hasattr(player, 'equipment_manager'):
                    # 保存物品名称（在处理之前）
                    item_name = slot.item.name
                    
                    # 获取原始属性并转换为中文格式
                    original_attributes = getattr(slot.item, 'attributes', {})
                    
                    # 使用属性翻译器将英文属性转换为装备管理器期望的中文格式
                    chinese_attributes = convert_equipment_attributes_to_chinese(original_attributes)
                    
                    # 将UI装备对象转换为装备管理器可以接受的格式
                    equipment_data = {
                        'id': getattr(slot.item, 'id', slot.item.name),
                        'name': slot.item.name,
                        'type': '装备',
                        'slot': self._get_equipment_slot_by_category(getattr(slot.item, 'category', '武器')),
                        'stats': chinese_attributes,  # 使用转换后的中文属性
                        'required_level': getattr(slot.item, 'level_requirement', 1),
                        'required_class': getattr(slot.item, 'required_class', []),
                        'value': getattr(slot.item, 'sell_price', 0),
                        'durability': getattr(slot.item, 'max_durability', 100),
                        'current_durability': getattr(slot.item, 'durability', 100),
                        'rarity': getattr(slot.item, 'rarity', '普通'),
                        'description': getattr(slot.item, 'description', ''),
                        'icon_path': self._fix_equipment_icon_path(getattr(slot.item, 'icon_path', None))
                    }
                    
                    # 🔧 修复：先尝试装备，只有成功后才从背包移除物品
                    result = player.equipment_manager.equip_item(equipment_data, player)
                    
                    # 🔧 新增：处理戒指选择的特殊情况
                    if result == "NEED_RING_CHOICE":
                        # 需要玩家选择戒指槽位
                        self._handle_ring_choice(equipment_data, slot_idx, item_name)
                        return
                    
                    if result is not False:  # 装备成功
                        # 装备成功，现在从背包中移除要装备的物品
                        global_slot_idx = self.current_page * self.inventory_manager.slots_per_page + slot_idx
                        removal_success = self.inventory_manager.remove_item_by_slot(global_slot_idx, 1, "equipment")
                        
                        if removal_success:
                            # 如果有被替换的装备，将其添加回背包
                            if result is not None:  # result是被替换的装备
                                replaced_equipment = result
                                self._add_replaced_equipment_to_inventory(replaced_equipment)
                                print(f"🔄 {replaced_equipment.get('name', '未知装备')} 被替换并放回背包")
                            
                            # 重新加载当前页
                            self._load_current_page_items()
                            
                            print(f"✅ 成功装备: {item_name}")
                        else:
                            # 🔧 如果从背包移除失败，需要撤销装备操作
                            print(f"❌ 从背包移除物品失败，装备操作被撤销: {item_name}")
                            # TODO: 这里可能需要撤销装备操作，但装备管理器没有提供撤销方法
                            # 目前先记录错误
                    else:
                        # 🔧 装备失败，物品保持在背包中不变
                        # 提供更详细的失败原因
                        level_req = getattr(slot.item, 'level_requirement', 1)
                        required_class = getattr(slot.item, 'required_class', [])
                        if hasattr(player, 'level') and player.level < level_req:
                            print(f"❌ 装备失败: {item_name}（需要等级 {level_req}，当前等级 {player.level}）")
                        elif required_class and hasattr(player, 'character_class') and player.character_class not in required_class:
                            print(f"❌ 装备失败: {item_name}（职业不符，需要: {required_class}）")
                        else:
                            print(f"❌ 装备失败: {item_name}（未知原因）")
                else:
                    print("❌ 无法获取玩家装备管理器")
            else:
                print(f"物品已锁定，无法装备: {slot.item.name}")
        else:
            print("只能装备装备类物品")
    
    def _get_equipment_slot_by_category(self, category):
        """根据装备分类获取装备槽位"""
        category_to_slot = {
            '武器': '武器',
            '头盔': '头盔',
            '防具': '胸甲',
            '护腿': '护腿',
            '靴子': '靴子',
            '项链': '护符',
            '戒指': '戒指',
            '手镯': '手镯'
        }
        return category_to_slot.get(category, '武器')
    
    def _fix_equipment_icon_path(self, icon_path):
        """
        修复装备图标路径，确保路径格式正确
        装备面板期望的路径格式是相对于 images/equipment/ 的路径
        """
        if not icon_path:
            return None
        
        # 如果路径以 game/assets/images/equipment/ 开头，则去除这部分
        prefix = "game/assets/images/equipment/"
        if icon_path.startswith(prefix):
            return icon_path[len(prefix):]
        
        # 如果路径以 images/equipment/ 开头，则去除这部分
        prefix2 = "images/equipment/"
        if icon_path.startswith(prefix2):
            return icon_path[len(prefix2):]
        
        # 否则直接返回原路径
        return icon_path
    
    def _add_replaced_equipment_to_inventory(self, equipment_data):
        """
        将被替换的装备添加回背包
        
        Args:
            equipment_data: 装备数据字典
        """
        if not equipment_data:
            return
        
        try:
            # 从装备数据创建Item对象
            # 将装备管理器的装备数据转换为Item数据格式
            item_data = {
                'id': equipment_data.get('id', equipment_data.get('name')),
                'name': equipment_data.get('name', '未知装备'),
                'type': 'equipment',
                'category': self._get_category_by_slot(equipment_data.get('slot', '武器')),
                'attributes': equipment_data.get('stats', {}),
                'level_requirement': equipment_data.get('required_level', 1),
                'required_class': equipment_data.get('required_class', []),
                'sell_price': equipment_data.get('value', 0),
                'max_durability': equipment_data.get('durability', 100),
                'durability': equipment_data.get('current_durability', equipment_data.get('durability', 100)),
                'rarity': equipment_data.get('rarity', '普通'),
                'description': equipment_data.get('description', ''),
                'icon_path': equipment_data.get('icon_path', None)
            }
            
            # 创建Item对象
            item = Item(item_data, 1)
            
            # 添加到背包 - 修复：使用正确的参数数量
            if hasattr(self, 'inventory_manager'):
                success = self.inventory_manager.add_item(item, 1)  # 🔧 移除第三个参数
                if not success:
                    print(f"⚠️ 背包已满，无法放回被替换的装备: {item.name}")
                    # TODO: 可以考虑将装备掉落到地面
            
        except Exception as e:
            print(f"❌ 添加被替换装备到背包失败: {e}")
    
    def _handle_ring_choice(self, ring_data, slot_idx, item_name):
        """
        处理戒指选择
        
        参数:
            ring_data: 戒指装备数据
            slot_idx: 背包槽位索引
            item_name: 物品名称
        """
        print(f"🎯 需要选择戒指槽位: {item_name}")
        
        try:
            # 获取玩家对象
            player = self._get_player()
            if not player or not hasattr(player, 'equipment_manager'):
                print("❌ 无法获取玩家装备管理器")
                return
            
            # 获取当前戒指装备
            ring1_item = player.equipment_manager.get_equipped_item("戒指1")
            ring2_item = player.equipment_manager.get_equipped_item("戒指2")
            
            # 导入戒指选择对话框
            from game.ui.ring_choice_dialog import show_ring_choice
            
            def on_ring_choice(chosen_slot):
                """戒指选择回调"""
                print(f"🎯 用户选择了槽位: {chosen_slot}")
                
                # 使用选择的槽位装备戒指
                result = player.equipment_manager.equip_item(ring_data, player, chosen_slot)
                
                if result is not False:  # 装备成功
                    # 从背包中移除戒指
                    global_slot_idx = self.current_page * self.inventory_manager.slots_per_page + slot_idx
                    removal_success = self.inventory_manager.remove_item_by_slot(global_slot_idx, 1, "equipment")
                    
                    if removal_success:
                        # 如果有被替换的装备，将其添加回背包
                        if result is not None:  # result是被替换的装备
                            replaced_equipment = result
                            self._add_replaced_equipment_to_inventory(replaced_equipment)
                            print(f"🔄 {replaced_equipment.get('name', '未知装备')} 被替换并放回背包")
                        
                        # 重新加载当前页
                        self._load_current_page_items()
                        
                        print(f"✅ 成功装备戒指: {item_name} 到 {chosen_slot}")
                    else:
                        print(f"❌ 从背包移除戒指失败: {item_name}")
                else:
                    print(f"❌ 装备戒指失败: {item_name}")
            
            # 在单独线程中显示对话框，避免阻塞主线程
            import threading
            dialog_thread = threading.Thread(
                target=show_ring_choice,
                args=(ring_data, ring1_item, ring2_item, on_ring_choice),
                daemon=True
            )
            dialog_thread.start()
            
        except Exception as e:
            print(f"❌ 处理戒指选择失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _get_category_by_slot(self, slot):
        """根据装备槽位获取分类"""
        slot_to_category = {
            '武器': '武器',
            '头盔': '头盔', 
            '胸甲': '防具',
            '护腿': '护腿',
            '靴子': '靴子',
            '护符': '项链',
            '戒指1': '戒指',   # 🔧 新增：支持双戒指槽位
            '戒指2': '戒指',   # 🔧 新增：支持双戒指槽位
            '手镯': '手镯'
        }
        return slot_to_category.get(slot, '武器')
    
    def _on_repair_button_click(self):
        """处理修理按钮点击"""
        if self.current_tab != "equipment":
            return
        
        # 获取玩家对象
        player = self._get_player()
        if not player or not hasattr(player, 'equipment_manager'):
            print("❌ 无法获取玩家装备管理器")
            return
        
        # 获取所有装备的耐久度信息
        durability_info = player.equipment_manager.get_equipment_durability_info()
        
        if not durability_info:
            print("⚠️ 没有装备需要修理")
            return
        
        # 计算修理费用和需要修理的装备
        repair_list = []
        total_repair_cost = 0
        
        for slot, info in durability_info.items():
            current = info['current']
            max_durability = info['max']
            
            if current < max_durability:
                # 需要修理
                repair_amount = max_durability - current
                # 修理费用：每点耐久度配置的金币
                repair_cost = repair_amount * self.REPAIR_COST_PER_DURABILITY
                repair_list.append({
                    'slot': slot,
                    'repair_amount': repair_amount,
                    'cost': repair_cost
                })
                total_repair_cost += repair_cost
        
        if not repair_list:
            print("✅ 所有装备都是满耐久度")
            return
        
        # 检查玩家金币是否足够
        player_gold = getattr(player, 'gold', 0)
        if player_gold < total_repair_cost:
            print(f"❌ 金币不足！需要 {total_repair_cost} 金币，当前只有 {player_gold} 金币")
            return
        
        # 显示确认对话框
        repair_count = len(repair_list)
        message = f"确认修理 {repair_count} 件装备？\n总费用: {total_repair_cost} 金币"
        self.confirm_dialog.message = message
        self.confirm_dialog.on_confirm = lambda: self._confirm_repair_equipment(repair_list, total_repair_cost)
        self.confirm_dialog.on_cancel = None
        self.confirm_dialog.show()
    
    def _confirm_repair_equipment(self, repair_list, total_cost):
        """确认修理装备"""
        player = self._get_player()
        if not player:
            return
        
        # 扣除金币
        player.gold = max(0, player.gold - total_cost)
        
        # 修理装备
        repaired_count = 0
        for repair_info in repair_list:
            slot = repair_info['slot']
            repair_amount = repair_info['repair_amount']
            
            success = player.equipment_manager.repair_equipment(slot, repair_amount)
            if success:
                repaired_count += 1
        
        print(f"✅ 成功修理 {repaired_count} 件装备，花费 {total_cost} 金币")

class ConfirmDialog:
    """确认对话框类"""
    
    def __init__(self, screen, message, on_confirm=None, on_cancel=None):
        """
        初始化确认对话框
        
        Args:
            screen: 屏幕对象
            message: 确认消息
            on_confirm: 确认回调函数
            on_cancel: 取消回调函数
        """
        self.screen = screen
        self.message = message
        self.on_confirm = on_confirm
        self.on_cancel = on_cancel
        self.visible = False
        
        # 对话框尺寸
        self.width = 300
        self.height = 150
        self.rect = pygame.Rect(
            (screen.get_width() - self.width) // 2,
            (screen.get_height() - self.height) // 2,
            self.width,
            self.height
        )
        
        # 按钮尺寸
        button_width = 80
        button_height = 30
        button_spacing = 20
        
        # 确认按钮
        self.confirm_button = pygame.Rect(
            self.rect.centerx - button_width - button_spacing // 2,
            self.rect.bottom - button_height - 20,
            button_width,
            button_height
        )
        
        # 取消按钮
        self.cancel_button = pygame.Rect(
            self.rect.centerx + button_spacing // 2,
            self.rect.bottom - button_height - 20,
            button_width,
            button_height
        )
        
        # 字体
        self.font = pygame.font.SysFont("SimHei", 14)
        self.title_font = pygame.font.SysFont("SimHei", 16)
    
    def show(self):
        """显示对话框"""
        self.visible = True
    
    def hide(self):
        """隐藏对话框"""
        self.visible = False
    
    def render(self):
        """渲染对话框"""
        if not self.visible:
            return
        
        # 绘制半透明背景遮罩
        overlay = pygame.Surface(self.screen.get_size(), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 128))
        self.screen.blit(overlay, (0, 0))
        
        # 绘制对话框背景
        pygame.draw.rect(self.screen, (60, 60, 70), self.rect)
        pygame.draw.rect(self.screen, (120, 120, 140), self.rect, 2)
        
        # 绘制标题
        title_surface = self.title_font.render("确认操作", True, (220, 220, 220))
        title_rect = title_surface.get_rect(center=(self.rect.centerx, self.rect.top + 30))
        self.screen.blit(title_surface, title_rect)
        
        # 绘制消息
        message_lines = self.message.split('\n')
        line_height = 20
        start_y = self.rect.centery - (len(message_lines) * line_height) // 2
        
        for i, line in enumerate(message_lines):
            line_surface = self.font.render(line, True, (200, 200, 200))
            line_rect = line_surface.get_rect(center=(self.rect.centerx, start_y + i * line_height))
            self.screen.blit(line_surface, line_rect)
        
        # 绘制确认按钮
        pygame.draw.rect(self.screen, (70, 120, 70), self.confirm_button)
        pygame.draw.rect(self.screen, (120, 180, 120), self.confirm_button, 2)
        confirm_text = self.font.render("确认", True, (255, 255, 255))
        confirm_text_rect = confirm_text.get_rect(center=self.confirm_button.center)
        self.screen.blit(confirm_text, confirm_text_rect)
        
        # 绘制取消按钮
        pygame.draw.rect(self.screen, (120, 70, 70), self.cancel_button)
        pygame.draw.rect(self.screen, (180, 120, 120), self.cancel_button, 2)
        cancel_text = self.font.render("取消", True, (255, 255, 255))
        cancel_text_rect = cancel_text.get_rect(center=self.cancel_button.center)
        self.screen.blit(cancel_text, cancel_text_rect)
    
    def handle_event(self, event):
        """处理事件"""
        if not self.visible:
            return False
        
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # 左键点击
                if self.confirm_button.collidepoint(event.pos):
                    # 确认按钮
                    self.hide()
                    if self.on_confirm:
                        self.on_confirm()
                    return True
                elif self.cancel_button.collidepoint(event.pos):
                    # 取消按钮
                    self.hide()
                    if self.on_cancel:
                        self.on_cancel()
                    return True
        
        return True  # 消费所有事件，防止穿透 