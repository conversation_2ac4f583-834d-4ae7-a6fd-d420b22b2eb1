{"monster_distribution": {"description": "怪物分布配置 - 控制怪物在地图上的分散程度", "min_distance_settings": {"normal_monsters": {"value": 3, "description": "普通怪物之间的最小距离（格子数）"}, "elite_monsters": {"value": 4, "description": "精英怪物之间的最小距离（格子数）"}, "boss_monsters": {"value": 8, "description": "BOSS与其他怪物的最小距离（格子数）"}, "player_spawn_safe_zone": {"value": 5, "description": "玩家出生点周围的安全区域半径"}}, "distribution_algorithm": {"grid_size": {"value": 8, "description": "网格化分布的网格大小"}, "max_attempts": {"value": 100, "description": "寻找合适位置的最大尝试次数"}, "grid_attempts_ratio": {"value": 0.7, "description": "使用网格化分布的尝试次数比例（0-1）"}}, "density_control": {"weight_divisor": {"value": 15, "description": "怪物权重除数，用于控制怪物数量密度"}, "max_monsters_per_type": {"value": 8, "description": "每种怪物类型的最大数量限制"}}, "special_zones": {"boss_spawn_regions": {"enabled": true, "description": "是否启用BOSS特殊生成区域", "regions": [{"name": "左上角", "x_ratio": [0.1, 0.33], "y_ratio": [0.1, 0.33]}, {"name": "右上角", "x_ratio": [0.67, 0.9], "y_ratio": [0.1, 0.33]}, {"name": "左下角", "x_ratio": [0.1, 0.33], "y_ratio": [0.67, 0.9]}, {"name": "右下角", "x_ratio": [0.67, 0.9], "y_ratio": [0.67, 0.9]}]}}, "debug_settings": {"show_distribution_info": {"value": false, "description": "是否显示怪物分布的调试信息（默认关闭以节省内存）"}, "log_position_conflicts": {"value": false, "description": "是否记录位置冲突的日志"}}}}