#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一日志管理器
负责管理游戏中的所有日志类型：错误日志、调试日志、用户消息
实现分层日志系统，提供开发者调试和用户体验的分离

Author: Game Development Team
Date: 2024
"""

import logging
import traceback
import time
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from datetime import datetime
from collections import deque
import os

# 导入用户消息管理器和错误转换器
from game.managers.user_message_manager import (
    UserMessageManager, UserMessageType, UserMessagePriority,
    get_user_message_manager
)
from .error_message_converter import (
    ErrorMessageConverter, ErrorCategory, ErrorSeverity,
    get_error_converter, convert_error_to_user_message
)
from .log_config import LogConfig, get_log_config
from .debug_config_manager import get_debug_config_manager


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "debug"        # 调试信息
    INFO = "info"          # 一般信息
    WARNING = "warning"    # 警告信息
    ERROR = "error"        # 错误信息
    CRITICAL = "critical"  # 严重错误
    BATTLE = "battle"      # 战斗日志
    USER = "user"          # 用户消息


class MessageType(Enum):
    """消息类型枚举"""
    SYSTEM = "system"      # 系统消息
    BATTLE = "battle"      # 战斗消息
    ERROR = "error"        # 错误消息
    DEBUG = "debug"        # 调试消息
    USER = "user"          # 用户消息


class LogManager:
    """
    统一日志管理器
    
    功能:
    1. 分层日志处理 - 区分开发者日志和用户消息
    2. 错误消息转换 - 将技术错误转换为用户友好消息
    3. 文件日志记录 - 保存完整的技术日志用于调试
    4. 内存日志缓存 - 提供实时日志查询
    5. 配置化管理 - 支持不同环境的日志级别
    """
    
    def __init__(self, 
                 log_dir: str = "logs",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 max_files: int = 5,
                 memory_buffer_size: int = 1000,
                 enable_file_logging: bool = True,
                 enable_console_logging: bool = True,
                 debug_mode: bool = False,
                 config: Optional[LogConfig] = None):
        """
        初始化日志管理器
        
        Args:
            log_dir: 日志文件目录
            max_file_size: 单个日志文件最大大小（字节）
            max_files: 保留的日志文件数量
            memory_buffer_size: 内存缓冲区大小
            enable_file_logging: 是否启用文件日志
            enable_console_logging: 是否启用控制台日志
            debug_mode: 是否启用调试模式
            config: 日志配置，如果为None则使用全局配置
        """
        # 使用配置或传入的参数
        self.config = config or get_log_config()
        self.debug_config = get_debug_config_manager()
        self.log_dir = log_dir
        self.max_file_size = max_file_size
        self.max_files = max_files
        self.memory_buffer_size = memory_buffer_size
        self.enable_file_logging = enable_file_logging
        # 使用全局调试配置控制控制台输出
        self.enable_console_logging = enable_console_logging and self.debug_config.is_console_output_enabled("log_manager")
        self.debug_mode = debug_mode
        
        # 创建日志目录
        if self.enable_file_logging:
            os.makedirs(self.log_dir, exist_ok=True)
        
        # 内存缓冲区
        self.memory_buffers = {
            LogLevel.DEBUG: deque(maxlen=memory_buffer_size),
            LogLevel.INFO: deque(maxlen=memory_buffer_size),
            LogLevel.WARNING: deque(maxlen=memory_buffer_size),
            LogLevel.ERROR: deque(maxlen=memory_buffer_size),
            LogLevel.CRITICAL: deque(maxlen=memory_buffer_size),
            LogLevel.BATTLE: deque(maxlen=memory_buffer_size),
            LogLevel.USER: deque(maxlen=memory_buffer_size)
        }
        
        # 兼容性：保持原有的缓存列表
        self.error_logs: List[Dict[str, Any]] = []        # 错误日志
        self.debug_logs: List[Dict[str, Any]] = []        # 调试日志
        self.user_messages: List[Dict[str, Any]] = []     # 用户消息
        self.battle_logs: List[Dict[str, Any]] = []       # 战斗日志
        
        # 日志缓存限制
        self.max_cache_size = memory_buffer_size
        
        # 日志统计
        self.stats = {
            "total_logs": 0,
            "logs_by_level": {},
            "errors_converted": 0,
            "user_messages_generated": 0
        }
        
        # 事件回调
        self.callbacks: Dict[str, List[Callable]] = {
            "on_error": [],
            "on_critical": [],
            "on_user_message": []
        }
        
        # 集成用户消息管理器和错误转换器
        try:
            self.user_message_manager = get_user_message_manager()
            self.error_converter = get_error_converter()
        except ImportError:
            # 如果模块不存在，使用None
            self.user_message_manager = None
            self.error_converter = None
        
        # 初始化文件日志记录器
        self._setup_file_loggers()
        
        # 错误消息转换映射
        self._setup_error_translations()
        
        # 记录管理器启动
        self.log_info("LogManager", "日志管理器已启动")
    
    def _setup_file_loggers(self):
        """
        设置文件日志记录器
        """
        # 创建不同类型的日志记录器
        self.error_logger = self._create_logger('error', 'error.log')
        self.debug_logger = self._create_logger('debug', 'debug.log')
        self.game_logger = self._create_logger('game', 'game.log')
        
    def _create_logger(self, name: str, filename: str) -> logging.Logger:
        """
        创建日志记录器
        
        Args:
            name: 记录器名称
            filename: 日志文件名
            
        Returns:
            配置好的日志记录器
        """
        logger = logging.getLogger(name)
        logger.setLevel(logging.DEBUG if self.debug_mode else logging.INFO)
        
        # 避免重复添加处理器
        if not logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(
                os.path.join(self.log_dir, filename), 
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            
            # 调试模式下添加控制台输出
            if self.debug_mode:
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.DEBUG)
                console_handler.setFormatter(formatter)
                logger.addHandler(console_handler)
        
        return logger
    
    def _setup_error_translations(self):
        """
        设置错误消息转换映射
        将技术错误转换为用户友好的消息
        """
        self.error_translations = {
            # 文件相关错误
            "FileNotFoundError": "游戏资源文件缺失，请检查游戏完整性",
            "PermissionError": "文件访问权限不足，请以管理员身份运行",
            "IOError": "文件读写错误，请检查磁盘空间",
            
            # 网络相关错误
            "ConnectionError": "网络连接失败，请检查网络设置",
            "TimeoutError": "网络请求超时，请稍后重试",
            
            # 数据相关错误
            "KeyError": "游戏数据异常，正在尝试修复",
            "ValueError": "数据格式错误，正在重新加载",
            "TypeError": "数据类型错误，正在修复",
            
            # 内存相关错误
            "MemoryError": "内存不足，请关闭其他程序后重试",
            "OverflowError": "数值溢出，正在重置相关数据",
            
            # 默认错误消息
            "default": "游戏遇到未知错误，正在尝试恢复"
        }
    
    def log_error(self, source: str, error: Exception, context: str = "", 
                 show_to_user: bool = True, auto_convert: bool = True):
        """
        记录错误日志
        
        Args:
            source: 错误来源
            error: 异常对象
            context: 错误上下文
            show_to_user: 是否显示给用户
            auto_convert: 是否自动转换为用户友好消息
        """
        # 获取错误详细信息
        error_type = type(error).__name__
        error_message = str(error)
        error_traceback = traceback.format_exc()
        
        # 创建错误日志条目
        error_entry = {
            "timestamp": datetime.now().isoformat(),
            "source": source,
            "error_type": error_type,
            "error_message": error_message,
            "context": context,
            "traceback": error_traceback,
            "level": LogLevel.ERROR.value
        }
        
        # 添加到内存缓存
        self.error_logs.append(error_entry)
        self._trim_cache(self.error_logs)
        
        # 记录到文件
        self.error_logger.error(
            f"[{source}] {error_type}: {error_message} | Context: {context}"
        )
        
        # 如果需要显示给用户，进行错误转换
        if show_to_user and auto_convert and self.error_converter:
            try:
                # 使用错误转换器转换消息
                conversion_result = self.error_converter.convert_error(
                    error_message=error_message,
                    exception_type=error_type,
                    traceback_info=error_traceback,
                    context={"source": source, "context": context}
                )
                
                # 添加用户消息
                user_message = conversion_result["user_message"]
                if self.user_message_manager:
                    priority = UserMessagePriority.HIGH if conversion_result["severity"].value >= 3 else UserMessagePriority.NORMAL
                    self.user_message_manager.add_error_message(user_message, priority)
                else:
                    self.add_user_message(user_message, MessageType.ERROR)
                
                # 更新统计
                self.stats["errors_converted"] += 1
                self.stats["user_messages_generated"] += 1
                
                # 如果支持自动恢复，记录恢复信息
                if conversion_result["auto_recovery"]:
                    self.log_info(source, f"自动恢复: {conversion_result['recovery_action']}")
                
            except Exception as conv_error:
                # 错误转换失败，使用原始消息
                user_message = self._translate_error_to_user_message(error_type, context)
                self.add_user_message(user_message, MessageType.ERROR)
                self.log_warning("LogManager", f"错误消息转换失败: {conv_error}")
        elif show_to_user and not auto_convert:
            # 直接显示原始错误消息或使用传统转换
            user_message = self._translate_error_to_user_message(error_type, context)
            self.add_user_message(user_message, MessageType.ERROR)
        
        # 调试模式下打印到控制台
        if self.debug_mode:
            print(f"ERROR [{source}]: {error_message}")
            print(f"Traceback: {error_traceback}")
    
    def log_debug(self, source: str, message: str, data: Any = None):
        """
        记录调试日志
        
        Args:
            source: 调试来源
            message: 调试消息
            data: 相关数据
        """
        debug_entry = {
            "timestamp": datetime.now().isoformat(),
            "source": source,
            "message": message,
            "data": str(data) if data is not None else None,
            "level": LogLevel.DEBUG.value
        }
        
        # 添加到内存缓存
        self.debug_logs.append(debug_entry)
        self._trim_cache(self.debug_logs)
        
        # 记录到文件
        self.debug_logger.debug(f"[{source}] {message} | Data: {data}")
        
        # 调试模式下打印到控制台（减少频率）
        if self.debug_mode and self.enable_console_logging:
            # 添加简单的频率控制，避免相同消息刷屏
            message_key = f"{source}:{message[:50]}"  # 使用前50个字符作为键
            current_time = time.time()

            if not hasattr(self, '_debug_message_times'):
                self._debug_message_times = {}

            # 相同消息5秒内只输出一次
            if message_key not in self._debug_message_times or (current_time - self._debug_message_times[message_key]) > 5.0:
                print(f"DEBUG [{source}]: {message}")
                self._debug_message_times[message_key] = current_time
    
    def log_info(self, source: str, message: str):
        """
        记录信息日志
        
        Args:
            source: 信息来源
            message: 信息内容
        """
        # 记录到游戏日志文件
        self.game_logger.info(f"[{source}] {message}")
        
        # 调试模式下打印到控制台（减少频率）
        if self.debug_mode and self.enable_console_logging:
            # 添加简单的频率控制
            message_key = f"{source}:{message[:50]}"
            current_time = time.time()

            if not hasattr(self, '_info_message_times'):
                self._info_message_times = {}

            # 相同消息3秒内只输出一次
            if message_key not in self._info_message_times or (current_time - self._info_message_times[message_key]) > 3.0:
                print(f"INFO [{source}]: {message}")
                self._info_message_times[message_key] = current_time
    
    def log_warning(self, source: str, message: str):
        """
        记录警告日志
        
        Args:
            source: 警告来源
            message: 警告内容
        """
        # 记录到游戏日志文件
        self.game_logger.warning(f"[{source}] {message}")
        
        # 生成用户提示消息
        user_message = f"⚠️ {message}"
        self.add_user_message(user_message, MessageType.SYSTEM)
        
        # 调试模式下打印到控制台（警告总是输出）
        if self.debug_mode and self.enable_console_logging:
            print(f"WARNING [{source}]: {message}")
    
    def add_battle_log(self, message: str, turn: int = 0):
        """
        添加战斗日志
        
        Args:
            message: 战斗消息
            turn: 回合数
        """
        battle_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "turn": turn,
            "type": MessageType.BATTLE.value
        }
        
        # 添加到内存缓存
        self.battle_logs.append(battle_entry)
        self._trim_cache(self.battle_logs)
        
        # 记录到游戏日志
        self.game_logger.info(f"[BATTLE] Turn {turn}: {message}")
    
    def add_user_message(self, message: str, msg_type: MessageType = MessageType.SYSTEM):
        """
        添加用户消息
        
        Args:
            message: 用户消息
            msg_type: 消息类型
        """
        user_entry = {
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "type": msg_type.value,
            "color": self._get_message_color(msg_type)
        }
        
        # 添加到内存缓存
        self.user_messages.append(user_entry)
        self._trim_cache(self.user_messages)
    
    def _translate_error_to_user_message(self, error_type: str, context: str) -> str:
        """
        将技术错误转换为用户友好消息
        
        Args:
            error_type: 错误类型
            context: 错误上下文
            
        Returns:
            用户友好的错误消息
        """
        # 获取基础翻译
        base_message = self.error_translations.get(error_type, self.error_translations["default"])
        
        # 根据上下文添加具体信息
        if "图片" in context or "image" in context.lower():
            return f"🖼️ {base_message}（图片资源相关）"
        elif "数据" in context or "data" in context.lower():
            return f"📊 {base_message}（数据相关）"
        elif "网络" in context or "network" in context.lower():
            return f"🌐 {base_message}（网络相关）"
        else:
            return f"⚠️ {base_message}"
    
    def _get_message_color(self, msg_type: MessageType) -> tuple:
        """
        获取消息颜色
        
        Args:
            msg_type: 消息类型
            
        Returns:
            RGB颜色元组
        """
        color_map = {
            MessageType.SYSTEM: (255, 255, 0),    # 黄色
            MessageType.BATTLE: (200, 255, 200),  # 浅绿色
            MessageType.ERROR: (255, 100, 100),   # 红色
            MessageType.DEBUG: (150, 150, 255),   # 蓝色
            MessageType.USER: (255, 255, 255)     # 白色
        }
        return color_map.get(msg_type, (255, 255, 255))
    
    def _trim_cache(self, cache_list: List[Dict]):
        """
        修剪缓存列表，保持在最大大小限制内
        
        Args:
            cache_list: 要修剪的缓存列表
        """
        if len(cache_list) > self.max_cache_size:
            # 保留最新的条目
            cache_list[:] = cache_list[-self.max_cache_size:]
    
    # 获取日志的公共接口
    def get_user_messages(self) -> List[Dict[str, Any]]:
        """获取用户消息列表 - 修复版，支持增量更新"""
        # 优先从用户消息管理器获取
        if self.user_message_manager:
            messages = self.user_message_manager.get_all_messages()
            return [msg.to_dict() for msg in messages]
        else:
            # 兼容性：使用原有的用户消息
            return self.user_messages.copy()
    
    def get_new_user_messages(self, last_read_index: int = 0) -> List[Dict[str, Any]]:
        """
        获取新的用户消息（增量更新）
        
        Args:
            last_read_index: 上次读取的消息索引
            
        Returns:
            新消息列表和当前最大索引
        """
        # 优先从用户消息管理器获取
        if self.user_message_manager:
            all_messages = self.user_message_manager.get_all_messages()
            new_messages = all_messages[last_read_index:] if last_read_index < len(all_messages) else []
            return [msg.to_dict() for msg in new_messages], len(all_messages)
        else:
            # 兼容性：使用原有的用户消息
            new_messages = self.user_messages[last_read_index:] if last_read_index < len(self.user_messages) else []
            return new_messages, len(self.user_messages)
    
    def get_battle_logs(self) -> List[Dict[str, Any]]:
        """获取战斗日志列表"""
        return self.battle_logs.copy()
    
    def get_battle_log(self) -> List[str]:
        """获取战斗日志 - 修复版，支持增量更新"""
        # 优先从用户消息管理器获取战斗消息
        if self.user_message_manager:
            battle_messages = self.user_message_manager.get_messages_by_type(UserMessageType.BATTLE)
            return [msg.message for msg in battle_messages]
        else:
            # 兼容性：使用原有的战斗日志
            return [log["message"] for log in self.battle_logs]
    
    def get_new_battle_logs(self, last_read_index: int = 0) -> tuple:
        """
        获取新的战斗日志（增量更新）
        
        Args:
            last_read_index: 上次读取的日志索引
            
        Returns:
            (新日志列表, 当前最大索引)
        """
        # 优先从用户消息管理器获取战斗消息
        if self.user_message_manager:
            battle_messages = self.user_message_manager.get_messages_by_type(UserMessageType.BATTLE)
            new_messages = battle_messages[last_read_index:] if last_read_index < len(battle_messages) else []
            return [msg.message for msg in new_messages], len(battle_messages)
        else:
            # 兼容性：使用原有的战斗日志
            new_logs = self.battle_logs[last_read_index:] if last_read_index < len(self.battle_logs) else []
            return [log["message"] for log in new_logs], len(self.battle_logs)
    
    def log_battle(self, message: str, context: Dict[str, Any] = None, show_to_user: bool = True):
        """记录战斗日志"""
        battle_info = {
            "message": message,
            "context": context or {},
            "timestamp": time.time(),
            "formatted_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 记录到开发者日志
        self.battle_logs.append(battle_info)
        self._trim_cache(self.battle_logs)
        
        # 记录到内存缓冲区
        if LogLevel.BATTLE in self.memory_buffers:
            self.memory_buffers[LogLevel.BATTLE].append(battle_info)
        
        # 如果需要显示给用户，添加到用户消息
        if show_to_user and self.user_message_manager:
            self.user_message_manager.add_battle_message(message)
            self.stats["user_messages_generated"] += 1
        
        # 更新统计
        self.stats["total_logs"] += 1
        if "battle" not in self.stats["logs_by_level"]:
            self.stats["logs_by_level"]["battle"] = 0
        self.stats["logs_by_level"]["battle"] += 1
        
        # 写入文件
        if self.enable_file_logging:
            self._write_to_file("battle", f"[BATTLE] {message}")
    
    def log_user_message(self, message: str, msg_type: UserMessageType = None,
                        priority: UserMessagePriority = None,
                        context: Dict[str, Any] = None):
        """记录用户消息日志"""
        # 设置默认值
        if msg_type is None:
            msg_type = UserMessageType.INFO
        if priority is None:
            priority = UserMessagePriority.NORMAL
        
        user_info = {
            "message": message,
            "type": msg_type.value if hasattr(msg_type, 'value') else str(msg_type),
            "priority": priority.value if hasattr(priority, 'value') else str(priority),
            "context": context or {},
            "timestamp": time.time(),
            "formatted_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 记录到开发者日志
        self.user_messages.append(user_info)
        self._trim_cache(self.user_messages)
        
        # 记录到内存缓冲区
        if LogLevel.USER in self.memory_buffers:
            self.memory_buffers[LogLevel.USER].append(user_info)
        
        # 添加到用户消息管理器
        if self.user_message_manager:
            self.user_message_manager.add_message(
                message=message,
                msg_type=msg_type,
                priority=priority,
                metadata=context
            )
        
        # 更新统计
        self.stats["user_messages_generated"] += 1
        self.stats["total_logs"] += 1
        if "user" not in self.stats["logs_by_level"]:
            self.stats["logs_by_level"]["user"] = 0
        self.stats["logs_by_level"]["user"] += 1
        
        # 触发用户消息回调
        self._trigger_callback("on_user_message", user_info)
        
        # 写入文件
        if self.enable_file_logging:
            self._write_to_file("user", f"[USER] {message}")
    
    def log_critical(self, source: str, error: Exception, context: str = "",
                    show_to_user: bool = True):
        """记录严重错误日志"""
        # 获取异常信息
        error_type = type(error).__name__
        error_message = str(error)
        error_traceback = traceback.format_exc()
        
        error_info = {
            "source": source,
            "error_type": error_type,
            "error_message": error_message,
            "traceback": error_traceback,
            "context": context,
            "timestamp": time.time(),
            "formatted_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 记录到错误日志
        self.error_logs.append(error_info)
        self._trim_cache(self.error_logs)
        
        # 记录到内存缓冲区
        if LogLevel.CRITICAL in self.memory_buffers:
            self.memory_buffers[LogLevel.CRITICAL].append(error_info)
        
        # 严重错误总是显示给用户
        if show_to_user and self.error_converter:
            try:
                # 使用错误转换器转换消息
                conversion_result = self.error_converter.convert_error(
                    error_message=error_message,
                    exception_type=error_type,
                    traceback_info=error_traceback,
                    context={"source": source, "context": context}
                )
                
                # 添加紧急用户消息
                user_message = f"🚨 严重错误: {conversion_result['user_message']}"
                if self.user_message_manager:
                    self.user_message_manager.add_error_message(user_message, UserMessagePriority.URGENT)
                else:
                    self.add_user_message(user_message, MessageType.ERROR)
                
                # 更新统计
                self.stats["errors_converted"] += 1
                self.stats["user_messages_generated"] += 1
                
            except Exception as conv_error:
                # 错误转换失败，使用原始消息
                user_message = f"🚨 严重系统错误: {error_message}"
                if self.user_message_manager:
                    self.user_message_manager.add_error_message(user_message, UserMessagePriority.URGENT)
                else:
                    self.add_user_message(user_message, MessageType.ERROR)
                self.log_warning("LogManager", f"严重错误消息转换失败: {conv_error}")
        
        # 更新统计
        self.stats["total_logs"] += 1
        if "critical" not in self.stats["logs_by_level"]:
            self.stats["logs_by_level"]["critical"] = 0
        self.stats["logs_by_level"]["critical"] += 1
        
        # 触发严重错误回调
        self._trigger_callback("on_critical", error_info)
        
        # 写入文件
        if self.enable_file_logging:
            self._write_to_file("error", f"[CRITICAL] {source}: {error_message}")
        
        # 调试模式下打印到控制台
        if self.debug_mode:
            print(f"[CRITICAL] {source}: {error_message}")
            print(f"Traceback: {error_traceback}")
    
    def add_callback(self, event: str, callback: Callable):
        """添加事件回调"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def remove_callback(self, event: str, callback: Callable):
        """移除事件回调"""
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)
    
    def _trigger_callback(self, event: str, data: Any):
        """触发事件回调"""
        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    callback(data)
                except Exception as e:
                    print(f"回调执行错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = self.stats.copy()
        
        # 添加当前缓存大小
        stats["current_cache_sizes"] = {
            "error_logs": len(self.error_logs),
            "debug_logs": len(self.debug_logs),
            "battle_logs": len(self.battle_logs),
            "user_messages": len(self.user_messages)
        }
        
        # 添加用户消息管理器统计
        if self.user_message_manager:
            stats["user_message_manager"] = self.user_message_manager.get_stats()
        
        # 添加错误转换器统计
        if self.error_converter:
            stats["error_converter"] = self.error_converter.get_stats()
        
        return stats
    
    def update(self):
        """更新日志管理器状态"""
        # 更新用户消息管理器
        if self.user_message_manager:
            self.user_message_manager.update()
    
    def _write_to_file(self, log_type: str, message: str):
        """写入日志到文件"""
        if not self.enable_file_logging:
            return
        
        # 检查配置是否允许文件输出
        if hasattr(self.config, 'file_output') and not self.config.file_output:
            return
            
        try:
            log_file = os.path.join(self.log_dir, f"{log_type}.log")
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 使用配置的时间戳格式
            if hasattr(self.config, 'timestamp_format'):
                timestamp = datetime.now().strftime(self.config.timestamp_format)
            
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {message}\n")
        except Exception as e:
            if self.enable_console_logging:
                print(f"写入日志文件失败: {e}")
    
    def get_error_logs(self) -> List[Dict[str, Any]]:
        """获取错误日志列表（仅调试模式）"""
        if self.debug_mode:
            return self.error_logs.copy()
        return []
    
    def get_debug_logs(self) -> List[Dict[str, Any]]:
        """获取调试日志列表（仅调试模式）"""
        if self.debug_mode:
            return self.debug_logs.copy()
        return []
    
    def clear_logs(self, log_type: str = "all"):
        """
        清空日志缓存
        
        Args:
            log_type: 要清空的日志类型 ("all", "user", "battle", "error", "debug")
        """
        if log_type == "all" or log_type == "user":
            self.user_messages.clear()
        if log_type == "all" or log_type == "battle":
            self.battle_logs.clear()
        if log_type == "all" or log_type == "error":
            self.error_logs.clear()
        if log_type == "all" or log_type == "debug":
            self.debug_logs.clear()
        
        self.log_info("LogManager", f"已清空 {log_type} 日志缓存")
    
    def get_log_stats(self) -> Dict[str, int]:
        """
        获取日志统计信息
        
        Returns:
            包含各类日志数量的字典
        """
        return {
            "user_messages": len(self.user_messages),
            "battle_logs": len(self.battle_logs),
            "error_logs": len(self.error_logs),
            "debug_logs": len(self.debug_logs)
        }
    
    def set_debug_mode(self, enabled: bool):
        """
        设置调试模式
        
        Args:
            enabled: 是否启用调试模式
        """
        self.debug_mode = enabled
        self.log_info("LogManager", f"调试模式已{'启用' if enabled else '禁用'}")


# 全局日志管理器实例
_global_log_manager: Optional[LogManager] = None


def get_log_manager() -> LogManager:
    """
    获取全局日志管理器实例
    
    Returns:
        全局日志管理器实例
    """
    global _global_log_manager
    if _global_log_manager is None:
        _global_log_manager = LogManager()
    return _global_log_manager


def init_log_manager(log_dir: str = "logs", debug_mode: bool = False) -> LogManager:
    """
    初始化全局日志管理器
    
    Args:
        log_dir: 日志文件目录
        debug_mode: 是否启用调试模式
        
    Returns:
        初始化的日志管理器实例
    """
    global _global_log_manager
    _global_log_manager = LogManager(log_dir, debug_mode)
    return _global_log_manager