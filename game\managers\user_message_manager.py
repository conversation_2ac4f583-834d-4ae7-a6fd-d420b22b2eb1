#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户消息管理器
专门管理显示给用户的消息，与技术日志分离
提供用户友好的消息展示和管理功能

Author: Game Development Team
Date: 2024
"""

import time
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from datetime import datetime


class UserMessageType(Enum):
    """用户消息类型枚举"""
    SYSTEM = "system"          # 系统消息
    BATTLE = "battle"          # 战斗消息
    ACHIEVEMENT = "achievement" # 成就消息
    WARNING = "warning"        # 警告消息
    INFO = "info"              # 信息消息
    SUCCESS = "success"        # 成功消息
    ERROR = "error"            # 错误消息（用户友好版本）


class UserMessagePriority(Enum):
    """用户消息优先级枚举"""
    LOW = 1      # 低优先级
    NORMAL = 2   # 普通优先级
    HIGH = 3     # 高优先级
    URGENT = 4   # 紧急优先级


class UserMessage:
    """
    用户消息类
    封装单个用户消息的所有属性
    """
    
    def __init__(self, 
                 message: str, 
                 msg_type: UserMessageType = UserMessageType.INFO,
                 priority: UserMessagePriority = UserMessagePriority.NORMAL,
                 color: tuple = None,
                 duration: float = 0,  # 0表示永久显示
                 auto_dismiss: bool = False,
                 metadata: Dict[str, Any] = None):
        """
        初始化用户消息
        
        Args:
            message: 消息内容
            msg_type: 消息类型
            priority: 消息优先级
            color: 消息颜色 (R, G, B)
            duration: 消息显示时长（秒），0表示永久显示
            auto_dismiss: 是否自动消失
            metadata: 附加元数据
        """
        self.id = self._generate_id()
        self.message = message
        self.type = msg_type
        self.priority = priority
        self.color = color or self._get_default_color(msg_type)
        self.duration = duration
        self.auto_dismiss = auto_dismiss
        self.metadata = metadata or {}
        
        # 时间戳
        self.created_at = datetime.now()
        self.timestamp = time.time()
        
        # 状态
        self.is_read = False
        self.is_dismissed = False
        self.is_pinned = False
    
    def _generate_id(self) -> str:
        """生成唯一消息ID"""
        return f"msg_{int(time.time() * 1000000)}"
    
    def _get_default_color(self, msg_type: UserMessageType) -> tuple:
        """
        获取消息类型的默认颜色
        
        Args:
            msg_type: 消息类型
            
        Returns:
            RGB颜色元组
        """
        color_map = {
            UserMessageType.SYSTEM: (255, 255, 0),      # 黄色
            UserMessageType.BATTLE: (200, 255, 200),    # 浅绿色
            UserMessageType.ACHIEVEMENT: (255, 215, 0), # 金色
            UserMessageType.WARNING: (255, 165, 0),     # 橙色
            UserMessageType.INFO: (200, 200, 255),      # 浅蓝色
            UserMessageType.SUCCESS: (0, 255, 0),       # 绿色
            UserMessageType.ERROR: (255, 100, 100)      # 红色
        }
        return color_map.get(msg_type, (255, 255, 255))
    
    def is_expired(self) -> bool:
        """
        检查消息是否已过期
        
        Returns:
            是否已过期
        """
        if self.duration <= 0 or self.is_pinned:
            return False
        return time.time() - self.timestamp > self.duration
    
    def mark_as_read(self):
        """标记消息为已读"""
        self.is_read = True
    
    def dismiss(self):
        """消除消息"""
        self.is_dismissed = True
    
    def pin(self):
        """置顶消息"""
        self.is_pinned = True
    
    def unpin(self):
        """取消置顶"""
        self.is_pinned = False
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            消息的字典表示
        """
        return {
            "id": self.id,
            "message": self.message,
            "type": self.type.value,
            "priority": self.priority.value,
            "color": self.color,
            "duration": self.duration,
            "auto_dismiss": self.auto_dismiss,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "timestamp": self.timestamp,
            "is_read": self.is_read,
            "is_dismissed": self.is_dismissed,
            "is_pinned": self.is_pinned,
            "is_expired": self.is_expired()
        }


class UserMessageManager:
    """
    用户消息管理器
    
    功能:
    1. 管理用户界面显示的所有消息
    2. 提供消息过滤、排序、分组功能
    3. 支持消息优先级和自动过期
    4. 提供消息回调和事件处理
    5. 与技术日志系统完全分离
    """
    
    def __init__(self, max_messages: int = 100):
        """
        初始化用户消息管理器
        
        Args:
            max_messages: 最大消息数量
        """
        self.max_messages = max_messages
        
        # 消息存储
        self.messages: List[UserMessage] = []
        self.pinned_messages: List[UserMessage] = []
        
        # 消息过滤器
        self.message_filters: Dict[str, Callable] = {}
        
        # 事件回调
        self.callbacks: Dict[str, List[Callable]] = {
            "on_message_added": [],
            "on_message_dismissed": [],
            "on_message_expired": [],
            "on_message_pinned": [],
            "on_message_unpinned": []
        }
        
        # 消息统计
        self.stats = {
            "total_messages": 0,
            "messages_by_type": {},
            "messages_by_priority": {}
        }
        
        # 初始化默认置顶消息
        self._init_default_pinned_messages()
    
    def _init_default_pinned_messages(self):
        """
        初始化默认置顶消息
        """
        default_messages = [
            {
                "message": "【系统公告】欢迎回家, 服务器已运行 198 天",
                "type": UserMessageType.SYSTEM,
                "priority": UserMessagePriority.HIGH,
                "color": (255, 215, 0)  # 金色
            },
            {
                "message": "【活动公告】主线任务: 右键锁定装备一次: 奖励->BOSS卷: 1",
                "type": UserMessageType.ACHIEVEMENT,
                "priority": UserMessagePriority.NORMAL,
                "color": (255, 165, 0)  # 橙色
            }
        ]
        
        for msg_data in default_messages:
            message = UserMessage(
                message=msg_data["message"],
                msg_type=msg_data["type"],
                priority=msg_data["priority"],
                color=msg_data["color"]
            )
            message.pin()
            self.pinned_messages.append(message)
    
    def add_message(self, 
                   message: str, 
                   msg_type: UserMessageType = UserMessageType.INFO,
                   priority: UserMessagePriority = UserMessagePriority.NORMAL,
                   color: tuple = None,
                   duration: float = 0,
                   auto_dismiss: bool = False,
                   metadata: Dict[str, Any] = None) -> UserMessage:
        """
        添加用户消息
        
        Args:
            message: 消息内容
            msg_type: 消息类型
            priority: 消息优先级
            color: 消息颜色
            duration: 显示时长
            auto_dismiss: 是否自动消失
            metadata: 附加元数据
            
        Returns:
            创建的消息对象
        """
        # 创建消息对象
        user_msg = UserMessage(
            message=message,
            msg_type=msg_type,
            priority=priority,
            color=color,
            duration=duration,
            auto_dismiss=auto_dismiss,
            metadata=metadata
        )
        
        # 添加到消息列表
        self.messages.append(user_msg)
        
        # 更新统计
        self._update_stats(user_msg)
        
        # 维护消息数量限制
        self._trim_messages()
        
        # 触发回调
        self._trigger_callback("on_message_added", user_msg)
        
        return user_msg
    
    def add_system_message(self, message: str, priority: UserMessagePriority = UserMessagePriority.NORMAL) -> UserMessage:
        """添加系统消息"""
        return self.add_message(
            message=f"【系统】{message}",
            msg_type=UserMessageType.SYSTEM,
            priority=priority,
            color=(255, 255, 0)
        )
    
    def add_battle_message(self, message: str) -> UserMessage:
        """添加战斗消息"""
        return self.add_message(
            message=message,
            msg_type=UserMessageType.BATTLE,
            priority=UserMessagePriority.NORMAL,
            color=(200, 255, 200)
        )
    
    def add_error_message(self, message: str, priority: UserMessagePriority = UserMessagePriority.HIGH) -> UserMessage:
        """添加错误消息（用户友好版本）"""
        return self.add_message(
            message=f"⚠️ {message}",
            msg_type=UserMessageType.ERROR,
            priority=priority,
            color=(255, 100, 100),
            duration=10.0,  # 错误消息显示10秒
            auto_dismiss=True
        )
    
    def add_success_message(self, message: str) -> UserMessage:
        """添加成功消息"""
        return self.add_message(
            message=f"✅ {message}",
            msg_type=UserMessageType.SUCCESS,
            priority=UserMessagePriority.NORMAL,
            color=(0, 255, 0),
            duration=5.0,
            auto_dismiss=True
        )
    
    def add_warning_message(self, message: str) -> UserMessage:
        """添加警告消息"""
        return self.add_message(
            message=f"⚠️ {message}",
            msg_type=UserMessageType.WARNING,
            priority=UserMessagePriority.HIGH,
            color=(255, 165, 0),
            duration=8.0,
            auto_dismiss=True
        )
    
    def pin_message(self, message_id: str) -> bool:
        """
        置顶消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            是否成功置顶
        """
        message = self.get_message_by_id(message_id)
        if message and not message.is_pinned:
            message.pin()
            self.pinned_messages.append(message)
            self.messages.remove(message)
            self._trigger_callback("on_message_pinned", message)
            return True
        return False
    
    def unpin_message(self, message_id: str) -> bool:
        """
        取消置顶消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            是否成功取消置顶
        """
        message = self.get_pinned_message_by_id(message_id)
        if message and message.is_pinned:
            message.unpin()
            self.pinned_messages.remove(message)
            self.messages.append(message)
            self._trigger_callback("on_message_unpinned", message)
            return True
        return False
    
    def dismiss_message(self, message_id: str) -> bool:
        """
        消除消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            是否成功消除
        """
        message = self.get_message_by_id(message_id)
        if message:
            message.dismiss()
            self._trigger_callback("on_message_dismissed", message)
            return True
        return False
    
    def get_message_by_id(self, message_id: str) -> Optional[UserMessage]:
        """
        根据ID获取消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            消息对象或None
        """
        for message in self.messages:
            if message.id == message_id:
                return message
        return None
    
    def get_pinned_message_by_id(self, message_id: str) -> Optional[UserMessage]:
        """
        根据ID获取置顶消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            置顶消息对象或None
        """
        for message in self.pinned_messages:
            if message.id == message_id:
                return message
        return None
    
    def get_all_messages(self, include_dismissed: bool = False) -> List[UserMessage]:
        """
        获取所有消息
        
        Args:
            include_dismissed: 是否包含已消除的消息
            
        Returns:
            消息列表
        """
        all_messages = self.pinned_messages + self.messages
        
        if not include_dismissed:
            all_messages = [msg for msg in all_messages if not msg.is_dismissed]
        
        # 按优先级和时间排序
        all_messages.sort(key=lambda x: (x.priority.value, x.timestamp), reverse=True)
        
        return all_messages
    
    def get_messages_by_type(self, msg_type: UserMessageType) -> List[UserMessage]:
        """
        根据类型获取消息
        
        Args:
            msg_type: 消息类型
            
        Returns:
            指定类型的消息列表
        """
        return [msg for msg in self.get_all_messages() if msg.type == msg_type]
    
    def get_messages_by_priority(self, priority: UserMessagePriority) -> List[UserMessage]:
        """
        根据优先级获取消息
        
        Args:
            priority: 消息优先级
            
        Returns:
            指定优先级的消息列表
        """
        return [msg for msg in self.get_all_messages() if msg.priority == priority]
    
    def cleanup_expired_messages(self) -> int:
        """
        清理过期消息
        
        Returns:
            清理的消息数量
        """
        expired_count = 0
        
        # 清理普通消息中的过期消息
        expired_messages = [msg for msg in self.messages if msg.is_expired()]
        for msg in expired_messages:
            self.messages.remove(msg)
            self._trigger_callback("on_message_expired", msg)
            expired_count += 1
        
        return expired_count
    
    def clear_messages(self, msg_type: UserMessageType = None):
        """
        清空消息
        
        Args:
            msg_type: 要清空的消息类型，None表示清空所有
        """
        if msg_type is None:
            self.messages.clear()
        else:
            self.messages = [msg for msg in self.messages if msg.type != msg_type]
    
    def add_callback(self, event: str, callback: Callable):
        """
        添加事件回调
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def remove_callback(self, event: str, callback: Callable):
        """
        移除事件回调
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)
    
    def _trigger_callback(self, event: str, message: UserMessage):
        """
        触发事件回调
        
        Args:
            event: 事件名称
            message: 相关消息
        """
        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    callback(message)
                except Exception as e:
                    print(f"回调执行错误: {e}")
    
    def _update_stats(self, message: UserMessage):
        """
        更新消息统计
        
        Args:
            message: 消息对象
        """
        self.stats["total_messages"] += 1
        
        # 按类型统计
        msg_type = message.type.value
        if msg_type not in self.stats["messages_by_type"]:
            self.stats["messages_by_type"][msg_type] = 0
        self.stats["messages_by_type"][msg_type] += 1
        
        # 按优先级统计
        priority = message.priority.value
        if priority not in self.stats["messages_by_priority"]:
            self.stats["messages_by_priority"][priority] = 0
        self.stats["messages_by_priority"][priority] += 1
    
    def _trim_messages(self):
        """
        修剪消息列表，保持在最大数量限制内
        """
        if len(self.messages) > self.max_messages:
            # 保留最新的消息
            self.messages = self.messages[-self.max_messages:]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取消息统计信息
        
        Returns:
            统计信息字典
        """
        current_stats = self.stats.copy()
        current_stats["current_messages"] = len(self.messages)
        current_stats["pinned_messages"] = len(self.pinned_messages)
        current_stats["total_current"] = len(self.messages) + len(self.pinned_messages)
        return current_stats
    
    def update(self):
        """
        更新消息管理器状态
        定期调用以处理过期消息等
        """
        # 清理过期消息
        self.cleanup_expired_messages()


# 全局用户消息管理器实例
_global_user_message_manager: Optional[UserMessageManager] = None


def get_user_message_manager() -> UserMessageManager:
    """
    获取全局用户消息管理器实例
    
    Returns:
        全局用户消息管理器实例
    """
    global _global_user_message_manager
    if _global_user_message_manager is None:
        _global_user_message_manager = UserMessageManager()
    return _global_user_message_manager


def init_user_message_manager(max_messages: int = 100) -> UserMessageManager:
    """
    初始化全局用户消息管理器
    
    Args:
        max_messages: 最大消息数量
        
    Returns:
        初始化的用户消息管理器实例
    """
    global _global_user_message_manager
    _global_user_message_manager = UserMessageManager(max_messages)
    return _global_user_message_manager