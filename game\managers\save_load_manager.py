# -*- coding: utf-8 -*-
"""
存档管理器
负责管理游戏数据的保存和加载功能
"""

import json
import os
import time
import shutil
from typing import Dict, List, Optional, Any
from datetime import datetime
from game.core.resource_manager import get_saves_path


class SaveLoadManager:
    """
    存档管理器类
    负责游戏数据的保存和加载
    """
    
    def __init__(self, save_directory: str = None):
        """
        初始化存档管理器
        
        参数:
            save_directory: 存档目录（如果不提供，会使用resource_manager获取合适的路径）
        """
        self.save_directory = save_directory if save_directory else get_saves_path()
        self.auto_save_interval = 300  # 自动保存间隔（秒）
        self.last_auto_save = 0
        self.max_backup_count = 5  # 最大备份数量
        
        # 确保存档目录存在
        self._ensure_save_directory()
    
    def save_player_data(self, player, save_name: str = "autosave") -> bool:
        """
        保存玩家数据
        
        参数:
            player: 玩家对象
            save_name: 存档名称
            
        返回:
            bool: 是否保存成功
        """
        try:
            # 收集玩家数据
            player_data = self._collect_player_data(player)
            
            # 添加保存时间戳
            player_data['save_info'] = {
                'save_time': time.time(),
                'save_date': datetime.now().isoformat(),
                'game_version': '1.0.0',  # 可以从配置文件读取
                'save_name': save_name
            }
            
            # 保存到文件
            save_path = os.path.join(self.save_directory, f"{save_name}.json")
            
            # 如果是自动保存，先备份现有文件
            if save_name == "autosave" and os.path.exists(save_path):
                self._backup_save_file(save_path)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(player_data, f, ensure_ascii=False, indent=2)
            
            print(f"游戏已保存到: {save_path}")
            return True
            
        except Exception as e:
            print(f"保存游戏失败: {e}")
            return False
    
    def load_player_data(self, save_name: str = "autosave") -> Optional[Dict[str, Any]]:
        """
        加载玩家数据
        
        参数:
            save_name: 存档名称
            
        返回:
            玩家数据字典或None
        """
        try:
            save_path = os.path.join(self.save_directory, f"{save_name}.json")
            
            if not os.path.exists(save_path):
                print(f"存档文件不存在: {save_path}")
                return None
            
            with open(save_path, 'r', encoding='utf-8') as f:
                player_data = json.load(f)
            
            # 验证存档数据
            if not self._validate_save_data(player_data):
                print(f"存档数据无效: {save_path}")
                return None
            
            print(f"游戏已从 {save_path} 加载")
            return player_data
            
        except Exception as e:
            print(f"加载游戏失败: {e}")
            return None
    
    def _collect_player_data(self, player) -> Dict[str, Any]:
        """
        收集玩家数据
        
        参数:
            player: 玩家对象
            
        返回:
            玩家数据字典
        """
        data = {
            # 基本信息
            'basic_info': {
                'name': getattr(player, 'name', ''),
                'character_class': getattr(player, 'character_class', ''),
                'gender': getattr(player, 'gender', ''),
                'level': getattr(player, 'level', 1),
                'exp': getattr(player, 'exp', 0),
                'exp_to_next_level': getattr(player, 'exp_to_next_level', 0)
            },
            
            # 属性数据
            'stats': {
                'hp': getattr(player, 'hp', 0),
                'mp': getattr(player, 'mp', 0),
                'current_hp': getattr(player, 'current_hp', 0),
                'current_mp': getattr(player, 'current_mp', 0),
                '攻击下限': getattr(player, '攻击下限', 0),
                '攻击上限': getattr(player, '攻击上限', 0),
                '防御下限': getattr(player, '防御下限', 0),
                '防御上限': getattr(player, '防御上限', 0),
                '魔法攻击下限': getattr(player, '魔法攻击下限', 0),
                '魔法攻击上限': getattr(player, '魔法攻击上限', 0),
                '道术攻击下限': getattr(player, '道术攻击下限', 0),
                '道术攻击上限': getattr(player, '道术攻击上限', 0),
                '魔抗': getattr(player, '魔抗', 0),
                '攻速': getattr(player, '攻速', 1.0)
            },
            
            # 资源
            'resources': {
                'gold': getattr(player, 'gold', 0),
                'currencies': getattr(player, 'currencies', {'gold': 0, 'silver': 0, 'yuanbao': 0, 'coin': 0})
            },
            
            # VIP信息
            'vip_info': {
                'vip_level': getattr(player, 'vip_level', 0),
                'vip_exp': getattr(player, 'vip_exp', 0)
            },
            
            # 击杀统计
            'kill_statistics': getattr(player, 'kill_statistics', {
                'total_monsters': 0,
                'total_bosses': 0,
                'map_kills': {},
                'monster_types': {}
            }),
            
            # 地图解锁进度
            'map_unlock_progress': getattr(player, 'map_unlock_progress', {}),
            
            # 位置信息
            'location': {
                'position': getattr(player, 'position', (0, 0)),
                'current_map': getattr(player, 'current_map', '新手村')
            },
            
            # 战斗状态
            'battle_state': {
                'is_alive': getattr(player, 'is_alive', True),
                'in_battle': getattr(player, 'in_battle', False),
                'is_hunting': getattr(player, 'is_hunting', False),
                'auto_hunt_enabled': getattr(player, 'auto_hunt_enabled', False)
            }
        }
        
        # 管理器数据
        if hasattr(player, 'inventory_manager'):
            data['inventory'] = player.inventory_manager.to_dict()
        
        if hasattr(player, 'equipment_manager'):
            data['equipment'] = player.equipment_manager.to_dict()
        
        if hasattr(player, 'skill_manager'):
            data['skills'] = player.skill_manager.to_dict()
        
        if hasattr(player, 'quest_manager'):
            data['quests'] = player.quest_manager.to_dict()
        
        # 兼容旧版本的数据结构
        if hasattr(player, 'inventory') and not hasattr(player, 'inventory_manager'):
            data['legacy_inventory'] = getattr(player, 'inventory', [])
        
        if hasattr(player, 'equipment') and not hasattr(player, 'equipment_manager'):
            data['legacy_equipment'] = getattr(player, 'equipment', {})
        
        if hasattr(player, 'skills') and not hasattr(player, 'skill_manager'):
            data['legacy_skills'] = getattr(player, 'skills', [])
        
        if hasattr(player, 'quests') and not hasattr(player, 'quest_manager'):
            data['legacy_quests'] = getattr(player, 'quests', [])
        
        if hasattr(player, 'completed_quests') and not hasattr(player, 'quest_manager'):
            data['legacy_completed_quests'] = getattr(player, 'completed_quests', [])
        
        # 签到数据
        if hasattr(player, 'daily_checkin'):
            data['daily_checkin'] = getattr(player, 'daily_checkin', {})
        
        return data
    
    def apply_player_data(self, player, data: Dict[str, Any]) -> bool:
        """
        将数据应用到玩家对象
        
        参数:
            player: 玩家对象
            data: 玩家数据字典
            
        返回:
            bool: 是否应用成功
        """
        try:
            # 基本信息
            basic_info = data.get('basic_info', {})
            for key, value in basic_info.items():
                if hasattr(player, key):
                    setattr(player, key, value)
            
            # 属性数据
            stats = data.get('stats', {})
            for key, value in stats.items():
                if hasattr(player, key):
                    setattr(player, key, value)
            
            # 资源
            resources = data.get('resources', {})
            for key, value in resources.items():
                if hasattr(player, key):
                    setattr(player, key, value)
            
            # 位置信息
            location = data.get('location', {})
            for key, value in location.items():
                if hasattr(player, key):
                    # 特殊处理位置数据，将列表转换为元组
                    if key == 'position' and isinstance(value, list):
                        value = tuple(value)
                    setattr(player, key, value)
            
            # 战斗状态
            battle_state = data.get('battle_state', {})
            for key, value in battle_state.items():
                if hasattr(player, key):
                    setattr(player, key, value)
            
            # VIP信息
            vip_info = data.get('vip_info', {})
            for key, value in vip_info.items():
                if hasattr(player, key):
                    setattr(player, key, value)
            
            # 处理currencies字典
            if 'currencies' in resources and hasattr(player, 'currencies'):
                player.currencies.update(resources['currencies'])
            
            # 击杀统计数据
            if 'kill_statistics' in data and hasattr(player, 'kill_statistics'):
                player.kill_statistics.update(data['kill_statistics'])
            
            # 地图解锁进度
            if 'map_unlock_progress' in data and hasattr(player, 'map_unlock_progress'):
                player.map_unlock_progress.update(data['map_unlock_progress'])
            
            # 管理器数据
            if 'inventory' in data and hasattr(player, 'inventory_manager'):
                player.inventory_manager.from_dict(data['inventory'])
            
            if 'equipment' in data and hasattr(player, 'equipment_manager'):
                player.equipment_manager.from_dict(data['equipment'])
            
            if 'skills' in data and hasattr(player, 'skill_manager'):
                player.skill_manager.from_dict(data['skills'])
            
            if 'quests' in data and hasattr(player, 'quest_manager'):
                player.quest_manager.from_dict(data['quests'])
            
            # 兼容旧版本数据
            if 'legacy_inventory' in data and hasattr(player, 'inventory'):
                player.inventory = data['legacy_inventory']
            
            if 'legacy_equipment' in data and hasattr(player, 'equipment'):
                player.equipment = data['legacy_equipment']
            
            if 'legacy_skills' in data and hasattr(player, 'skills'):
                player.skills = data['legacy_skills']
            
            if 'legacy_quests' in data and hasattr(player, 'quests'):
                player.quests = data['legacy_quests']
            
            if 'legacy_completed_quests' in data and hasattr(player, 'completed_quests'):
                player.completed_quests = data['legacy_completed_quests']
            
            # 签到数据
            if 'daily_checkin' in data:
                player.daily_checkin = data['daily_checkin']
            
            # 重新计算战力
            if hasattr(player, '_calculate_battle_power'):
                player.battle_power = player._calculate_battle_power()
                player.combat_value = player.battle_power
            
            return True
            
        except Exception as e:
            print(f"应用玩家数据失败: {e}")
            return False
    
    def _validate_save_data(self, data: Dict[str, Any]) -> bool:
        """
        验证存档数据的有效性
        
        参数:
            data: 存档数据
            
        返回:
            bool: 数据是否有效
        """
        # 检查必要的字段
        required_fields = ['basic_info', 'stats', 'save_info']
        for field in required_fields:
            if field not in data:
                return False
        
        # 检查基本信息
        basic_info = data.get('basic_info', {})
        if 'name' not in basic_info or 'character_class' not in basic_info:
            return False
        
        return True
    
    def _ensure_save_directory(self):
        """
        确保存档目录存在
        """
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
    
    def _backup_save_file(self, save_path: str):
        """
        备份存档文件
        
        参数:
            save_path: 存档文件路径
        """
        try:
            backup_dir = os.path.join(self.save_directory, "backups")
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.basename(save_path)
            name, ext = os.path.splitext(filename)
            backup_filename = f"{name}_{timestamp}{ext}"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            # 复制文件
            shutil.copy2(save_path, backup_path)
            
            # 清理旧备份
            self._cleanup_old_backups(backup_dir, name)
            
        except Exception as e:
            print(f"备份存档失败: {e}")
    
    def _cleanup_old_backups(self, backup_dir: str, save_name: str):
        """
        清理旧的备份文件
        
        参数:
            backup_dir: 备份目录
            save_name: 存档名称
        """
        try:
            # 获取所有相关备份文件
            backup_files = []
            for filename in os.listdir(backup_dir):
                if filename.startswith(f"{save_name}_") and filename.endswith(".json"):
                    file_path = os.path.join(backup_dir, filename)
                    backup_files.append((file_path, os.path.getmtime(file_path)))
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除超出数量限制的备份
            if len(backup_files) > self.max_backup_count:
                for file_path, _ in backup_files[self.max_backup_count:]:
                    os.remove(file_path)
                    
        except Exception as e:
            print(f"清理备份文件失败: {e}")
    
    def get_save_list(self) -> List[Dict[str, Any]]:
        """
        获取存档列表
        
        返回:
            存档信息列表
        """
        saves = []
        
        try:
            if not os.path.exists(self.save_directory):
                return saves
            
            for filename in os.listdir(self.save_directory):
                if filename.endswith(".json"):
                    file_path = os.path.join(self.save_directory, filename)
                    save_name = os.path.splitext(filename)[0]
                    
                    # 获取文件信息
                    stat = os.stat(file_path)
                    
                    save_info = {
                        'name': save_name,
                        'file_path': file_path,
                        'size': stat.st_size,
                        'modified_time': stat.st_mtime,
                        'modified_date': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    }
                    
                    # 尝试读取存档信息
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            
                        if 'save_info' in data:
                            save_info.update(data['save_info'])
                        
                        if 'basic_info' in data:
                            save_info['player_name'] = data['basic_info'].get('name', '未知')
                            save_info['player_level'] = data['basic_info'].get('level', 1)
                            save_info['player_class'] = data['basic_info'].get('character_class', '未知')
                            
                    except Exception:
                        # 如果读取失败，只保留基本文件信息
                        pass
                    
                    saves.append(save_info)
            
            # 按修改时间排序
            saves.sort(key=lambda x: x['modified_time'], reverse=True)
            
        except Exception as e:
            print(f"获取存档列表失败: {e}")
        
        return saves
    
    def delete_save(self, save_name: str) -> bool:
        """
        删除存档
        
        参数:
            save_name: 存档名称
            
        返回:
            bool: 是否删除成功
        """
        try:
            save_path = os.path.join(self.save_directory, f"{save_name}.json")
            
            if os.path.exists(save_path):
                os.remove(save_path)
                print(f"存档已删除: {save_path}")
                return True
            else:
                print(f"存档不存在: {save_path}")
                return False
                
        except Exception as e:
            print(f"删除存档失败: {e}")
            return False
    
    def check_auto_save(self, player) -> bool:
        """
        检查并执行自动保存
        
        参数:
            player: 玩家对象
            
        返回:
            bool: 是否执行了自动保存
        """
        current_time = time.time()
        
        if current_time - self.last_auto_save >= self.auto_save_interval:
            if self.save_player_data(player, "autosave"):
                self.last_auto_save = current_time
                return True
        
        return False
    
    def export_save(self, save_name: str, export_path: str) -> bool:
        """
        导出存档
        
        参数:
            save_name: 存档名称
            export_path: 导出路径
            
        返回:
            bool: 是否导出成功
        """
        try:
            save_path = os.path.join(self.save_directory, f"{save_name}.json")
            
            if not os.path.exists(save_path):
                return False
            
            shutil.copy2(save_path, export_path)
            return True
            
        except Exception as e:
            print(f"导出存档失败: {e}")
            return False
    
    def import_save(self, import_path: str, save_name: str) -> bool:
        """
        导入存档
        
        参数:
            import_path: 导入路径
            save_name: 存档名称
            
        返回:
            bool: 是否导入成功
        """
        try:
            if not os.path.exists(import_path):
                return False
            
            # 验证导入的文件
            with open(import_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not self._validate_save_data(data):
                return False
            
            # 复制到存档目录
            save_path = os.path.join(self.save_directory, f"{save_name}.json")
            shutil.copy2(import_path, save_path)
            
            return True
            
        except Exception as e:
            print(f"导入存档失败: {e}")
            return False