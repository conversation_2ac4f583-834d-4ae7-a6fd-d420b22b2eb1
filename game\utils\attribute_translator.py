#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
装备属性翻译器 - 将英文属性名翻译成中文
"""

class AttributeTranslator:
    """装备属性翻译器"""
    
    def __init__(self):
        # 英文到中文的属性翻译映射
        self.attribute_mapping = {
            # 基础攻击属性
            'attack': '物理攻击',
            'attack_min': '攻击下限',
            'attack_max': '攻击上限',
            'physical_attack': '物理攻击',
            
            # 魔法相关
            'magic': '魔法攻击',
            'magic_attack': '魔法攻击',
            'magic_attack_min': '魔法攻击下限',
            'magic_attack_max': '魔法攻击上限',
            'magic_defense': '魔法防御',
            'magic_resist': '魔法抗性',
            'mp': '魔法值',
            'mana': '魔法值',
            
            # 道术相关
            'taoism': '道术攻击',
            'tao_attack': '道术攻击',
            'taoism_attack': '道术攻击',
            'tao_attack_min': '道术攻击下限',
            'tao_attack_max': '道术攻击上限',
            
            # 防御属性
            'defense': '物理防御',
            'physical_defense': '物理防御',
            'defense_min': '防御下限',
            'defense_max': '防御上限',
            'armor': '护甲',
            
            # 生命值相关
            'hp': '生命值',
            'health': '生命值',
            'max_hp': '最大生命值',
            'max_health': '最大生命值',
            
            # 战斗属性
            'accuracy': '准确度',
            'hit_rate': '命中率',
            'critical_rate': '暴击率',
            'critical_chance': '暴击率',
            'crit_rate': '暴击率',
            'crit_chance': '暴击率',
            'attack_speed': '攻击速度',
            'speed': '速度',
            'agility': '敏捷',
            'dodge': '闪避',
            'block': '格挡',
            
            # 特殊属性
            'luck': '幸运',
            'lucky': '幸运',
            'strength': '力量',
            'intelligence': '智力',
            'wisdom': '智慧',
            'constitution': '体质',
            'dexterity': '敏捷',
            
            # 抗性属性
            'fire_resist': '火焰抗性',
            'ice_resist': '冰霜抗性',
            'lightning_resist': '雷电抗性',
            'poison_resist': '毒素抗性',
            'holy_resist': '神圣抗性',
            'dark_resist': '暗黑抗性',
            
            # 伤害类型
            'fire_damage': '火焰伤害',
            'ice_damage': '冰霜伤害',
            'lightning_damage': '雷电伤害',
            'poison_damage': '毒素伤害',
            'holy_damage': '神圣伤害',
            'dark_damage': '暗黑伤害',
            
            # 资源相关
            'mana_regen': '魔法回复',
            'hp_regen': '生命回复',
            'stamina': '体力',
            'energy': '能量',
            
            # 经验和掉落
            'exp_bonus': '经验加成',
            'gold_bonus': '金币加成',
            'drop_rate': '掉落率',
            'drop_rate_bonus': '掉落率加成',
            
            # 其他常见属性
            'level': '等级',
            'level_requirement': '等级需求',
            'durability': '耐久度',
            'max_durability': '最大耐久度',
            'weight': '重量',
            'value': '价值',
            'sell_price': '售价',
            'buy_price': '购买价格',
            
            # 职业相关
            'class': '职业',
            'required_class': '职业需求',
            'warrior': '战士',
            'mage': '法师',
            'taoist': '道士',
            'archer': '弓箭手',
            
            # 装备品质
            'quality': '品质',
            'rarity': '稀有度',
            'common': '普通',
            'uncommon': '精良',
            'rare': '稀有',
            'epic': '史诗',
            'legendary': '传说',
            
            # 装备类型
            'weapon': '武器',
            'armor': '护甲',
            'helmet': '头盔',
            'necklace': '项链',
            'ring': '戒指',
            'bracelet': '手镯',
            'boots': '靴子',
            'gloves': '手套',
            'belt': '腰带',
            'shield': '盾牌',
            
            # 特殊效果
            'special': '特殊效果',
            'description': '描述',
            'effect': '效果',
            'passive': '被动效果',
            'active': '主动效果',
            
            # 其他
            'random': '随机',
            'fixed': '固定',
            'range': '范围',
            'min': '最小值',
            'max': '最大值',
            
            # 特殊属性名（处理中文到英文的对应）
            '攻击下限': 'attack_min',
            '攻击上限': 'attack_max',
            '防御下限': 'defense_min',
            '防御上限': 'defense_max',
            '魔法攻击下限': 'magic_attack_min',
            '魔法攻击上限': 'magic_attack_max',
            '道术攻击下限': 'tao_attack_min',
            '道术攻击上限': 'tao_attack_max',
            '攻速': 'attack_speed',
            '暴击率': 'critical_rate',
            '魔抗': 'magic_resist',
            '幸运': 'luck',
            '准确': 'accuracy',
            '敏捷': 'agility',
        }
        
        # 反向映射：中文到英文
        self.reverse_mapping = {}
        for english, chinese in self.attribute_mapping.items():
            self.reverse_mapping[chinese] = english
            # 同时添加英文键对应自己（支持已经是英文的情况）
            self.reverse_mapping[english] = english
    
    def translate_to_chinese(self, english_attr: str) -> str:
        """
        将英文属性名翻译成中文

        参数:
            english_attr: 英文属性名

        返回:
            中文属性名，如果未找到对应翻译则返回原文
        """
        if not isinstance(english_attr, str):
            return str(english_attr)

        # 🔧 修复：如果输入已经是中文属性名，直接返回
        # 检查是否包含中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in english_attr):
            return english_attr

        # 先查找完全匹配
        chinese = self.attribute_mapping.get(english_attr.lower())
        if chinese:
            return chinese

        # 如果没有完全匹配，尝试模糊匹配
        english_lower = english_attr.lower()

        # 处理一些特殊情况
        if 'attack' in english_lower and 'magic' in english_lower:
            return '魔法攻击'
        elif 'attack' in english_lower and 'tao' in english_lower:
            return '道术攻击'
        elif 'attack' in english_lower:
            return '物理攻击'
        elif 'defense' in english_lower and 'magic' in english_lower:
            return '魔法防御'
        elif 'defense' in english_lower:
            return '物理防御'
        elif 'damage' in english_lower:
            return '伤害'
        elif 'resist' in english_lower:
            return '抗性'
        elif 'bonus' in english_lower:
            return '加成'
        elif 'rate' in english_lower:
            return '几率'
        elif 'speed' in english_lower:
            return '速度'
        elif 'regen' in english_lower:
            return '回复'

        # 如果都没有匹配，返回原文（可能已经是中文或者是未知属性）
        return english_attr
    
    def translate_to_english(self, chinese_attr: str) -> str:
        """
        将中文属性名翻译成英文
        
        参数:
            chinese_attr: 中文属性名
            
        返回:
            英文属性名，如果未找到对应翻译则返回原文
        """
        if not isinstance(chinese_attr, str):
            return str(chinese_attr)
        
        return self.reverse_mapping.get(chinese_attr, chinese_attr)
    
    def translate_attribute_dict(self, attributes: dict, to_chinese: bool = True) -> dict:
        """
        翻译属性字典中的所有键名
        
        参数:
            attributes: 属性字典
            to_chinese: True表示翻译成中文，False表示翻译成英文
            
        返回:
            翻译后的属性字典
        """
        if not isinstance(attributes, dict):
            return {}
        
        translated_dict = {}
        translator = self.translate_to_chinese if to_chinese else self.translate_to_english
        
        for key, value in attributes.items():
            translated_key = translator(key)
            translated_dict[translated_key] = value
        
        return translated_dict
    
    def format_attribute_display(self, attr_name: str, attr_value, to_chinese: bool = True) -> str:
        """
        格式化属性显示文本

        参数:
            attr_name: 属性名
            attr_value: 属性值
            to_chinese: 是否翻译成中文

        返回:
            格式化的属性显示文本
        """
        try:
            # 🔧 修复：处理 None 值和无效数据
            if attr_value is None:
                return f"{attr_name}: 无"

            if to_chinese:
                display_name = self.translate_to_chinese(attr_name)
            else:
                display_name = attr_name

            # 处理不同类型的属性值
            if isinstance(attr_value, (list, tuple)) and len(attr_value) >= 2:
                # 范围值 [最小值, 最大值]
                min_val, max_val = attr_value[0], attr_value[1]
                if min_val == max_val:
                    return f"{display_name}: +{min_val}"
                else:
                    return f"{display_name}: +{min_val}-{max_val}"
            elif isinstance(attr_value, (int, float)):
                # 数值
                if attr_name.lower() in ['critical_rate', 'crit_rate', 'hit_rate', 'dodge'] or '率' in display_name:
                    # 百分比属性
                    return f"{display_name}: +{attr_value * 100:.1f}%"
                elif attr_name.lower() in ['attack_speed', 'speed'] or '速度' in display_name:
                    # 速度属性，保留一位小数
                    return f"{display_name}: +{attr_value:.1f}"
                else:
                    # 普通数值属性
                    if isinstance(attr_value, float) and attr_value.is_integer():
                        return f"{display_name}: +{int(attr_value)}"
                    else:
                        return f"{display_name}: +{attr_value}"
            elif attr_value == 'random':
                # 随机属性
                return f"{display_name}: 随机"
            else:
                # 其他类型，直接显示
                return f"{display_name}: {attr_value}"
        except Exception as e:
            # 🔧 修复：如果格式化失败，返回安全的默认值
            print(f"❌ 格式化属性显示时出错: {e}, 属性名: {attr_name}, 属性值: {attr_value}")
            return f"{attr_name}: {attr_value}"
    
    def add_custom_mapping(self, english: str, chinese: str):
        """
        添加自定义的属性翻译映射
        
        参数:
            english: 英文属性名
            chinese: 中文属性名
        """
        self.attribute_mapping[english.lower()] = chinese
        self.reverse_mapping[chinese] = english.lower()
    
    def get_all_mappings(self) -> dict:
        """
        获取所有的翻译映射
        
        返回:
            完整的翻译映射字典
        """
        return self.attribute_mapping.copy()


# 创建一个全局翻译器实例，方便外部直接调用
_translator_instance = AttributeTranslator()

# 提供一个可直接导入的属性映射字典，用于UI显示
ATTRIBUTE_MAP = _translator_instance.get_all_mappings()


# 封装成独立的函数，方便在代码中直接调用
def translate_attr_to_chinese(attr_name: str) -> str:
    """翻译属性名到中文"""
    return _translator_instance.translate_to_chinese(attr_name)

def translate_attr_to_english(attr_name: str) -> str:
    """翻译属性名到英文"""
    return _translator_instance.translate_to_english(attr_name)

def translate_attributes_dict(attributes: dict, to_chinese: bool = True) -> dict:
    """翻译属性字典"""
    return _translator_instance.translate_attribute_dict(attributes, to_chinese)

def format_attribute_text(attr_name: str, attr_value, to_chinese: bool = True) -> str:
    """格式化属性显示文本"""
    return _translator_instance.format_attribute_display(attr_name, attr_value, to_chinese)

def convert_equipment_attributes_to_chinese(attributes: dict) -> dict:
    """
    将装备属性转换为装备管理器可以理解的中文格式
    特别处理范围属性，如 defense: [1, 3] -> 防御下限: 1, 防御上限: 3
    
    参数:
        attributes: 英文格式的属性字典
        
    返回:
        中文格式的属性字典
    """
    if not isinstance(attributes, dict):
        return {}
    
    result = {}
    
    for attr_name, attr_value in attributes.items():
        # 处理范围属性
        if isinstance(attr_value, list) and len(attr_value) == 2:
            min_val, max_val = attr_value
            
            # 根据属性类型获取对应的中文下限和上限属性名
            if attr_name.lower() == 'attack':
                result['攻击下限'] = min_val
                result['攻击上限'] = max_val
            elif attr_name.lower() == 'defense':
                result['防御下限'] = min_val
                result['防御上限'] = max_val
            elif attr_name.lower() == 'magic_attack':
                result['魔法攻击下限'] = min_val
                result['魔法攻击上限'] = max_val
            elif attr_name.lower() == 'magic_defense':
                result['魔抗'] = min_val  # 魔法防御通常是单值
            elif attr_name.lower() == 'tao_attack':
                result['道术攻击下限'] = min_val
                result['道术攻击上限'] = max_val
            else:
                # 对于未知的范围属性，使用通用翻译
                chinese_name = translate_attr_to_chinese(attr_name)
                result[f"{chinese_name}下限"] = min_val
                result[f"{chinese_name}上限"] = max_val
        else:
            # 处理单值属性
            chinese_name = translate_attr_to_chinese(attr_name)
            result[chinese_name] = attr_value
    
    return result 