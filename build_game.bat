@echo off
chcp 65001 >nul
title 萝卜传奇游戏打包工具

echo.
echo ============================================
echo      🎮 萝卜传奇游戏 - EXE打包工具 🎮
echo ============================================
echo.

REM 检查Python是否存在
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python3.8+
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查main.py是否存在
if not exist "main.py" (
    echo ❌ 错误: 未找到main.py，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 检查游戏目录是否存在
if not exist "game" (
    echo ❌ 错误: 未找到game目录，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo ✅ 环境检查通过，启动打包程序...
echo.

REM 运行Python打包脚本
python build_exe.py

REM 等待用户按键
echo.
echo 程序执行完成
pause 