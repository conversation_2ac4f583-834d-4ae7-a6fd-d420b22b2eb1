import pygame

class FontManager:
    """
    字体管理器类
    用于统一管理游戏中的字体资源
    """
    
    def __init__(self):
        """
        初始化字体管理器
        """
        # 确保pygame字体模块已初始化
        if not pygame.font.get_init():
            pygame.font.init()
        
        # 预定义字体缓存
        self.font_cache = {}
        
        # 默认字体配置
        self.default_font_name = "SimHei"
        self.fallback_font = None
    
    def get_font(self, font_type='default', size=16, bold=False):
        """
        获取指定类型和大小的字体
        
        Args:
            font_type (str): 字体类型，目前支持 'default'
            size (int): 字体大小
            bold (bool): 是否加粗
        
        Returns:
            pygame.font.Font: 字体对象
        """
        # 创建字体缓存键
        cache_key = f"{font_type}_{size}_{bold}"
        
        # 如果缓存中存在，直接返回
        if cache_key in self.font_cache:
            return self.font_cache[cache_key]
        
        # 创建新字体
        try:
            if font_type == 'default':
                font = pygame.font.SysFont(self.default_font_name, size, bold=bold)
            else:
                # 其他字体类型也使用默认字体
                font = pygame.font.SysFont(self.default_font_name, size, bold=bold)
        except:
            # 如果系统字体不可用，使用pygame默认字体
            font = pygame.font.Font(None, size)
        
        # 缓存字体
        self.font_cache[cache_key] = font
        
        return font
    
    def clear_cache(self):
        """
        清空字体缓存
        """
        self.font_cache.clear()