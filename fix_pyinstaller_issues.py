#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller问题修复工具
解决常见的PyInstaller打包错误
"""

import sys
import subprocess
import importlib.util

def check_python_environment():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    
    # 检查Python版本
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️  建议使用Python 3.8或更高版本")
    
    # 检查关键模块
    critical_modules = ['email', 'pkg_resources', 'setuptools', 'tkinter']
    missing_modules = []
    
    for module in critical_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}: 可用")
        except ImportError:
            print(f"❌ {module}: 缺失")
            missing_modules.append(module)
    
    return missing_modules

def fix_pyinstaller_version():
    """修复PyInstaller版本问题"""
    print("\n🔧 检查并修复PyInstaller版本...")
    
    try:
        import PyInstaller
        current_version = PyInstaller.__version__
        print(f"当前PyInstaller版本: {current_version}")
        
        # 如果是6.x版本，建议降级到5.x
        if current_version.startswith('6.'):
            print("⚠️  PyInstaller 6.x版本可能存在兼容性问题")
            
            choice = input("是否降级到稳定的5.13.2版本? (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                print("正在降级PyInstaller...")
                subprocess.run([
                    sys.executable, "-m", "pip", "uninstall", "pyinstaller", "-y"
                ], check=True)
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "pyinstaller==5.13.2"
                ], check=True)
                print("✅ PyInstaller已降级到5.13.2")
                return True
        else:
            print("✅ PyInstaller版本正常")
            
    except ImportError:
        print("❌ PyInstaller未安装")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "pyinstaller==5.13.2"
        ], check=True)
        print("✅ 已安装PyInstaller 5.13.2")
        return True
    
    return False

def install_missing_dependencies():
    """安装缺失的依赖"""
    print("\n🔧 检查游戏依赖...")
    
    # 检查requirements.txt
    try:
        with open('requirements.txt', 'r') as f:
            requirements = f.read().strip().split('\n')
        
        print("正在安装游戏依赖...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ 游戏依赖安装完成")
        
    except FileNotFoundError:
        print("⚠️  未找到requirements.txt文件")
        
        # 手动安装关键依赖
        key_deps = ['pygame-ce>=2.5.0', 'pyperclip==1.8.2', 'pillow']
        for dep in key_deps:
            print(f"正在安装 {dep}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], check=True)
        print("✅ 关键依赖安装完成")

def clean_build_cache():
    """清理构建缓存"""
    print("\n🧹 清理构建缓存...")
    
    import shutil
    from pathlib import Path
    
    cache_dirs = ['build', 'dist', '__pycache__', 'specs']
    
    for cache_dir in cache_dirs:
        path = Path(cache_dir)
        if path.exists():
            if path.is_dir():
                shutil.rmtree(path)
                print(f"✅ 清理目录: {cache_dir}")
            else:
                path.unlink()
                print(f"✅ 清理文件: {cache_dir}")
    
    # 清理Python缓存
    for pycache in Path('.').rglob('__pycache__'):
        shutil.rmtree(pycache)
        print(f"✅ 清理缓存: {pycache}")

def create_safe_spec():
    """创建安全的spec配置"""
    print("\n📝 创建安全的打包配置...")
    
    # 创建一个最小化的spec文件模板
    safe_spec = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 最小化的安全配置
datas = [
    ('game/assets', 'game/assets'),
    ('game/data', 'game/data'),
]

# 确保包含必要的标准库模块
hiddenimports = [
    'email',
    'email.mime',
    'email.mime.text', 
    'pkg_resources',
    'setuptools',
    'tkinter',
    'tkinter.ttk',
    'pygame',
    'pyperclip',
]

# 保守的排除策略 - 只排除明确不需要的大型库
excludes = [
    'numpy',
    'scipy', 
    'matplotlib',
    'pandas',
    'tensorflow',
    'torch',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='萝卜传奇_安全版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩避免问题
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 使用控制台模式便于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('safe_build.spec', 'w', encoding='utf-8') as f:
        f.write(safe_spec)
    
    print("✅ 已创建安全配置文件: safe_build.spec")
    return 'safe_build.spec'

def main():
    """主修复函数"""
    print("=" * 60)
    print("🔧 PyInstaller问题修复工具")
    print("=" * 60)
    
    # 检查环境
    missing = check_python_environment()
    
    # 修复PyInstaller版本
    pyi_fixed = fix_pyinstaller_version()
    
    # 安装依赖
    install_missing_dependencies()
    
    # 清理缓存
    clean_build_cache()
    
    # 创建安全配置
    safe_spec = create_safe_spec()
    
    print("\n" + "=" * 60)
    print("🎉 修复完成！")
    print("=" * 60)
    
    print("\n💡 接下来的步骤:")
    print("1. 使用安全配置进行测试打包:")
    print(f"   pyinstaller --clean --noconfirm {safe_spec}")
    print()
    print("2. 如果安全配置成功，再尝试使用完整的打包工具:")
    print("   python build_exe.py")
    print()
    print("3. 如果还有问题，请检查具体的错误信息")
    
    if missing:
        print(f"\n⚠️  注意：发现缺失模块: {', '.join(missing)}")
        print("   请手动安装这些模块")

if __name__ == "__main__":
    main() 