import pygame
from game.ui.info_panel import InfoPanel
from game.ui.map_panel import MapPanel
from game.ui.battle_panel import BattlePanel
from game.ui.announcement_panel import AnnouncementPanel
from game.ui.rank_panel import RankPanel
from game.ui.improved_equipment_panel import ImprovedEquipmentPanel as EquipmentPanel
from game.ui.inventory_panel import InventoryPanel
from game.ui.warehouse_panel import WarehousePanel
from game.ui.medicine_panel import MedicinePanel
from game.data.inventory import UIInventoryManager
from game.managers.user_message_manager import get_user_message_manager

class UIManager:
    """
    UI管理器，负责游戏界面的管理
    """
    def __init__(self, screen, player, map_manager, battle_manager, rank_manager, potion_effects_manager=None):
        # 屏幕引用
        self.screen = screen
        
        # 游戏组件引用
        self.player = player
        self.map_manager = map_manager
        self.battle_manager = battle_manager
        self.rank_manager = rank_manager
        self.potion_effects_manager = potion_effects_manager
        
        # 获取消息管理器
        self.user_message_manager = get_user_message_manager()
        
        # 将消息管理器添加到玩家对象，方便其他模块访问
        if hasattr(self.player, '__dict__'):
            self.player.user_message_manager = self.user_message_manager
        
        # 获取屏幕尺寸
        self.screen_width = screen.get_width()
        self.screen_height = screen.get_height()
        
        # 计算主界面面板的大小和位置，使其适应窗口大小
        # 左侧区域（信息区和地图区）宽度为窗口宽度的1/4
        left_width = int(self.screen_width * 0.25)
        # 中间区域（战斗区和日志区）宽度为窗口宽度的1/2
        center_width = int(self.screen_width * 0.5)
        # 右侧区域（排行区）宽度为窗口宽度的1/4
        right_width = self.screen_width - left_width - center_width
        
        # 信息区高度为窗口高度的1/3
        info_height = int(self.screen_height * 0.35)
        # 地图区高度为窗口高度的2/3
        map_height = self.screen_height - info_height
        # 战斗区高度为窗口高度的3/4
        battle_height = int(self.screen_height * 0.75)
        # 日志区高度为窗口高度的1/4
        log_height = self.screen_height - battle_height
        # 排行区高度为窗口高度
        rank_height = self.screen_height
        
        # 🔧 统一背包系统：直接使用Player的背包管理器
        if hasattr(player, 'inventory_manager') and player.inventory_manager:
            # 直接使用Player的统一背包管理器
            self.inventory_manager = player.inventory_manager
            print("✅ 使用Player的统一背包管理器")
            
            # 打印背包状态调试信息
            self.inventory_manager.debug_print_status()
            
        else:
            # 如果Player没有背包管理器，创建新的统一背包管理器
            from game.managers.inventory_manager import InventoryManager
            self.inventory_manager = InventoryManager(max_size=40)
            print("⚠️ Player没有背包管理器，创建新的统一背包管理器")
            
            # 尝试从存档加载背包数据
            try:
                import json
                import os
                save_path = "saves/autosave.json"
                if os.path.exists(save_path):
                    with open(save_path, 'r', encoding='utf-8') as f:
                        save_data = json.load(f)
                    
                    # 加载背包数据
                    inventory_data = save_data.get('inventory', {})
                    if inventory_data:
                        self.inventory_manager.from_dict(inventory_data)
                        print("✅ 从存档加载背包数据成功")
                    else:
                        print("⚠️ 存档中没有背包数据")
                else:
                    print("⚠️ 没有找到存档文件")
            except Exception as e:
                print(f"⚠️ 加载背包数据时出错: {e}")
            
            # 如果Player对象存在，设置背包管理器引用
            if hasattr(player, '__dict__'):
                player.inventory_manager = self.inventory_manager
        
        # UI面板 - 确保地图区的y坐标正确设置为信息区的底部
        self.info_panel = InfoPanel(screen, player, (0, 0), (left_width, info_height))
        self.map_panel = MapPanel(screen, map_manager, player, (0, info_height), (left_width, map_height), battle_manager)
        self.battle_panel = BattlePanel(screen, battle_manager, player, (left_width, 0), (center_width, battle_height), map_manager, self.potion_effects_manager)
        # 将背包管理器传递给战斗管理器，以便战斗面板可以访问
        self.battle_manager.inventory_manager = self.inventory_manager
        # 将地图管理器传递给战斗管理器，以便自动战斗功能可以访问
        self.battle_manager.map_manager = self.map_manager
        # 设置战斗管理器的地图管理器引用
        if hasattr(self.battle_manager, 'set_map_manager'):
            self.battle_manager.set_map_manager(self.map_manager)
        # 将玩家实例传递给战斗管理器
        if hasattr(self.battle_manager, 'set_player_instance'):
            self.battle_manager.set_player_instance(self.player)
        # 设置BattleManager的小地图面板引用
        if hasattr(self.battle_manager, 'set_mini_map_panel'):
            self.battle_manager.set_mini_map_panel(self.battle_panel)
        
        # 设置BattleManager的战斗面板引用，用于显示伤害文本
        if hasattr(self.battle_manager, 'set_battle_panel'):
            self.battle_manager.set_battle_panel(self.battle_panel)
        
        # 初始化小地图数据结构
        if not hasattr(self.battle_panel, 'minimap_data'):
            class MinimapData:
                def __init__(self):
                    self.enemies = []
                    self.player_pos = (0, 0)
            self.battle_panel.minimap_data = MinimapData()
        
        # 延迟加载小地图怪物数据 - 不在初始化时立即调用
        # 这个方法应该在用户开始寻怪时才调用，避免游戏启动时产生不必要的日志
        # if hasattr(self.battle_manager, '_load_minimap_monster_data'):
        #     self.battle_manager._load_minimap_monster_data()
        
        self.announcement_panel = AnnouncementPanel(screen, battle_manager, (left_width, battle_height), (center_width, log_height), player, self.user_message_manager)

        # 🔧 新增：将公告面板连接到战斗管理器，用于显示掉落信息
        if hasattr(battle_manager, 'set_announcement_panel'):
            battle_manager.set_announcement_panel(self.announcement_panel)

        self.rank_panel = RankPanel(screen, rank_manager, (left_width + center_width, 0), (right_width, rank_height))
        
        # 装备面板（初始不可见）
        equipment_panel_width = int(self.screen_width * 0.4)  # 进一步减小到40%
        equipment_panel_height = int(self.screen_height * 0.5)  # 进一步减小到50%
        equipment_panel_x = (self.screen_width - equipment_panel_width) // 2
        equipment_panel_y = (self.screen_height - equipment_panel_height) // 2
        self.equipment_panel = EquipmentPanel(screen, player, (equipment_panel_x, equipment_panel_y), (equipment_panel_width, equipment_panel_height))
        self.equipment_panel.visible = False
        
        # 背包面板（初始不可见）
        inventory_panel_width = int(self.screen_width * 0.5)  # 减小到50%
        inventory_panel_height = int(self.screen_height * 0.6)  # 减小到60%
        inventory_panel_x = (self.screen_width - inventory_panel_width) // 2
        inventory_panel_y = (self.screen_height - inventory_panel_height) // 2
        self.inventory_panel = InventoryPanel(screen, self.player, self.inventory_manager, (inventory_panel_x, inventory_panel_y), (inventory_panel_width, inventory_panel_height))
        self.inventory_panel.visible = False
        
        # 仓库面板（初始不可见）
        warehouse_panel_width = int(self.screen_width * 0.5)  # 减小到50%
        warehouse_panel_height = int(self.screen_height * 0.6)  # 减小到60%
        warehouse_panel_x = (self.screen_width - warehouse_panel_width) // 2
        warehouse_panel_y = (self.screen_height - warehouse_panel_height) // 2
        self.warehouse_panel = WarehousePanel(screen, self.inventory_manager, (warehouse_panel_x, warehouse_panel_y), (warehouse_panel_width, warehouse_panel_height))
        self.warehouse_panel.visible = False
        
        # 药水面板（初始不可见）
        medicine_panel_width = int(self.screen_width * 0.6)
        medicine_panel_height = int(self.screen_height * 0.8)
        medicine_panel_x = (self.screen_width - medicine_panel_width) // 2
        medicine_panel_y = (self.screen_height - medicine_panel_height) // 2
        self.medicine_panel = MedicinePanel(screen, (medicine_panel_x, medicine_panel_y), (medicine_panel_width, medicine_panel_height), 
                                         self.battle_panel, self.player, self.potion_effects_manager)
        self.medicine_panel.visible = False
        
        # 🔧 新增：建立战斗面板和药水面板的相互引用
        self.battle_panel.medicine_panel = self.medicine_panel
        
        # 签到面板（初始不可见）
        from game.ui.font_manager import FontManager
        self.font_manager = FontManager()
        # 导入签到面板和积分商城面板
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from ui.checkin_panel import CheckinPanel
        from ui.points_shop_panel import PointsShopPanel
        from game.ui.gold_shop_panel import GoldShopPanel
        self.checkin_panel = CheckinPanel(screen, self.font_manager, self, player)
        self.checkin_panel.visible = False
        self.points_shop_panel = PointsShopPanel(screen, self.font_manager, self, player)
        self.points_shop_panel.visible = False
        self.gold_shop_panel = GoldShopPanel(screen, self.font_manager, self, player)
        self.gold_shop_panel.visible = False
        
        # 设置背包和仓库面板互相引用
        self.inventory_panel.set_warehouse_panel(self.warehouse_panel)
        self.warehouse_panel.set_inventory_panel(self.inventory_panel)
        
        
        # 加载字体
        self.font = pygame.font.SysFont("SimHei", 14)
        
        # 回调函数
        self.skill_panel_callback = None
        
        # 当前活动的面板（用于处理事件）
        self.active_panels = [
            self.info_panel,
            self.map_panel,
            self.battle_panel,
            self.announcement_panel,
            self.rank_panel,
            self.equipment_panel,
            self.inventory_panel,
            self.warehouse_panel,
            self.medicine_panel,
            self.checkin_panel,
            self.points_shop_panel,
            self.gold_shop_panel
        ]
    
    def handle_event(self, event):
        """
        处理UI事件 - 修复版，正确处理弹出面板的层级和事件优先级
        """
        # 按照优先级顺序处理事件：弹出面板 > 基础面板
        # 1. 首先检查弹出面板（优先级最高）
        popup_panels = [
            self.gold_shop_panel,
            self.points_shop_panel,
            self.checkin_panel,
            self.medicine_panel,
            self.warehouse_panel,
            self.inventory_panel,
            self.equipment_panel
        ]
        
        for panel in popup_panels:
            if panel.visible:
                # 弹出面板可见时，优先处理事件
                if panel.handle_event(event):
                    return True  # 事件被弹出面板处理，停止传递
                
                # 如果是鼠标点击事件，检查是否点击在面板外部
                if event.type == pygame.MOUSEBUTTONDOWN:
                    # 如果点击在弹出面板的区域内，消费掉事件（防止穿透）
                    if hasattr(panel, 'rect') and panel.rect.collidepoint(event.pos):
                        return True  # 消费掉事件，防止穿透到后面的面板
                    else:
                        # 点击在弹出面板外部，关闭面板
                        panel.visible = False
                        print(f"点击外部区域，关闭{panel.__class__.__name__}")
                        return True  # 消费掉事件
        
        # 2. 如果没有弹出面板可见，或者事件未被处理，传递给基础面板
        base_panels = [
            self.info_panel,
            self.map_panel,
            self.battle_panel,
            self.announcement_panel,
            self.rank_panel
        ]
        
        for panel in base_panels:
            if panel.visible and panel.handle_event(event):
                return True  # 事件被基础面板处理，停止传递
        
        return False
    
    def update(self, current_time):
        """
        更新所有UI面板
        """
        # 更新消息管理器
        self.user_message_manager.update()

        # 更新所有可见的面板
        self.info_panel.update()
        self.map_panel.update(current_time)
        self.battle_panel.update()
        self.announcement_panel.update()
        self.rank_panel.update()
        
        # 计算时间增量（毫秒）
        if not hasattr(self, 'last_update_time'):
            self.last_update_time = current_time
        delta_time = current_time - self.last_update_time
        self.last_update_time = current_time
        
        # 更新弹出面板
        if self.equipment_panel.visible:
            self.equipment_panel.update()
        if self.inventory_panel.visible:
            self.inventory_panel.update()
        if self.warehouse_panel.visible:
            self.warehouse_panel.update()
        if self.medicine_panel.visible:
            self.medicine_panel.update()
        
        # 更新签到面板
        if self.checkin_panel.visible:
            self.checkin_panel.update(current_time)
        
        # 更新积分商城面板
        if self.points_shop_panel.visible:
            self.points_shop_panel.update(current_time)
            
        # 更新金币商城面板
        if self.gold_shop_panel.visible:
            self.gold_shop_panel.update(current_time)
        
        # 注释掉不存在的方法调用
        # 更新战斗面板的幸运轮盘 - 该方法不存在
        # self.battle_panel.update_luck_wheel(current_time)
    
    def render(self):
        """
        渲染所有UI面板
        """
        # 渲染基础面板
        self.info_panel.render()
        self.map_panel.render()
        self.battle_panel.render()
        self.announcement_panel.render()
        self.rank_panel.render()
        
        # 渲染弹出面板
        if self.equipment_panel.visible:
            self.equipment_panel.render()
        if self.inventory_panel.visible:
            self.inventory_panel.render()
        if self.warehouse_panel.visible:
            self.warehouse_panel.render()
        if self.medicine_panel.visible:
            self.medicine_panel.render()
        if self.checkin_panel.visible:
            self.checkin_panel.render()
        if self.points_shop_panel.visible:
            self.points_shop_panel.render()
        if self.gold_shop_panel.visible:
            self.gold_shop_panel.render()
        
        # 渲染调试信息
        # self._render_debug_info()
    
    def _render_debug_info(self):
        """
        渲染调试信息
        """
        # 渲染帧率
        fps_text = f"FPS: {int(pygame.time.Clock().get_fps())}"
        fps_surface = self.font.render(fps_text, True, (255, 255, 255))
        self.screen.blit(fps_surface, (self.screen_width - 80, 10))
    
    def show_panel(self, panel_name):
        """
        显示指定面板
        """
        if hasattr(self, panel_name):
            getattr(self, panel_name).visible = True
    
    def hide_panel(self, panel_name):
        """
        隐藏指定面板
        """
        if hasattr(self, panel_name):
            getattr(self, panel_name).visible = False
    
    def show_message(self, message, duration=3000):
        """
        显示一条临时消息
        """
        self.user_message_manager.add_message(message, duration=duration / 1000, auto_dismiss=True)
    
    def set_skill_panel_callback(self, callback):
        """
        设置技能面板回调函数
        
        Args:
            callback: 回调函数
        """
        self.skill_panel_callback = callback
        # 在信息面板的"技能"按钮上设置回调
        self.info_panel.set_on_skill_tab_click(self.skill_panel_callback)
    
    def set_equipment_panel_callback(self):
        """
        设置装备面板回调函数
        
        注：装备面板直接由UI管理器管理，不需要外部回调
        """
        # 在信息面板的"装备"按钮上设置回调
        self.info_panel.set_on_equipment_tab_click(self.toggle_equipment_panel)
    
    def set_inventory_panel_callback(self):
        """
        设置背包面板回调函数
        """
        # 在信息面板的"背包"按钮上设置回调
        self.info_panel.set_on_inventory_tab_click(self.toggle_inventory_panel)
    
    def set_warehouse_panel_callback(self):
        """
        设置仓库面板回调函数
        """
        # 在信息面板的"仓库"按钮上设置回调
        self.info_panel.set_on_warehouse_tab_click(self.toggle_warehouse_panel)
    
    def set_medicine_panel_callback(self):
        """
        设置药水面板回调函数
        """
        # 在信息面板的"药水"按钮上设置回调
        self.info_panel.set_on_medicine_tab_click(self.toggle_medicine_panel)
    
    def set_checkin_panel_callback(self):
        """
        设置签到面板回调函数
        """
        # 在信息面板的"签到"按钮上设置回调
        self.info_panel.set_on_checkin_tab_click(self.toggle_checkin_panel)
    
    def set_shop_panel_callback(self):
        """
        设置商城面板回调函数
        """
        # 在信息面板的"商城"按钮上设置回调
        self.info_panel.set_on_shop_tab_click(self.toggle_points_shop_panel)
    
    def toggle_equipment_panel(self):
        """
        切换装备面板的显示状态
        """
        # 切换可见性
        self.equipment_panel.visible = not self.equipment_panel.visible
        
        # 如果显示，则确保面板居中且尺寸正确
        if self.equipment_panel.visible:
            # 计算装备面板大小和位置
            equipment_panel_width = int(self.screen_width * 0.4)
            equipment_panel_height = int(self.screen_height * 0.5)
            equipment_panel_x = (self.screen_width - equipment_panel_width) // 2
            equipment_panel_y = (self.screen_height - equipment_panel_height) // 2
            
            # 设置面板位置和大小
            self.equipment_panel.set_position((equipment_panel_x, equipment_panel_y))
            
            # 🔧 新增：强制刷新装备面板，确保显示最新装备
            if hasattr(self.equipment_panel, 'equipment_changed'):
                self.equipment_panel.equipment_changed = True
            
            print(f"显示装备面板，尺寸: {equipment_panel_width}x{equipment_panel_height}")
        else:
            print("隐藏装备面板")
    
    def toggle_inventory_panel(self):
        """
        切换背包面板的显示状态
        """
        # 使用背包面板的toggle_visibility方法
        self.inventory_panel.toggle_visibility()
        
        if self.inventory_panel.visible:
            print("显示背包面板")
        else:
            print("隐藏背包面板")
    
    def toggle_warehouse_panel(self):
        """
        切换仓库面板的显示状态
        """
        # 使用仓库面板的toggle_visibility方法
        self.warehouse_panel.toggle_visibility()
        
        if self.warehouse_panel.visible:
            print("显示仓库面板")
        else:
            print("隐藏仓库面板")
    
    def toggle_medicine_panel(self):
        """
        切换药水面板的显示状态
        """
        # 切换可见性
        self.medicine_panel.visible = not self.medicine_panel.visible
        
        # 如果显示，则确保面板居中且尺寸正确
        if self.medicine_panel.visible:
            # 计算药水面板大小和位置
            medicine_panel_width = int(self.screen_width * 0.6)
            medicine_panel_height = int(self.screen_height * 0.8)
            medicine_panel_x = (self.screen_width - medicine_panel_width) // 2
            medicine_panel_y = (self.screen_height - medicine_panel_height) // 2
            
            # 设置面板位置和大小
            self.medicine_panel.set_position((medicine_panel_x, medicine_panel_y))
            
            print(f"显示药水面板，尺寸: {medicine_panel_width}x{medicine_panel_height}")
        else:
            print("隐藏药水面板")
    
    def toggle_checkin_panel(self):
        """
        切换签到面板的显示状态
        """
        # 切换可见性
        self.checkin_panel.visible = not self.checkin_panel.visible
        
        if self.checkin_panel.visible:
            print("显示签到面板")
        else:
            print("隐藏签到面板")
    
    def show_points_shop(self):
        """
        显示积分商城面板
        """
        print("显示积分商城面板")
        self.points_shop_panel.show()
    
    def toggle_points_shop_panel(self):
        """
        切换积分商城面板的显示状态
        """
        # 切换可见性
        self.points_shop_panel.visible = not self.points_shop_panel.visible
        
        if self.points_shop_panel.visible:
            print("显示积分商城面板")
        else:
            print("隐藏积分商城面板")
            
    def show_gold_shop(self):
        """
        显示金币商城面板
        """
        print("显示金币商城面板")
        self.gold_shop_panel.show()
    
    def toggle_gold_shop_panel(self):
        """
        切换金币商城面板的显示状态
        """
        # 切换可见性
        self.gold_shop_panel.visible = not self.gold_shop_panel.visible
        
        if self.gold_shop_panel.visible:
            print("显示金币商城面板")
        else:
            print("隐藏金币商城面板")
            
    def set_gold_shop_panel_callback(self):
        """
        设置金币商城面板回调函数
        """
        # 在信息面板的"金币商城"按钮上设置回调
        if hasattr(self.info_panel, 'set_on_gold_shop_tab_click'):
            self.info_panel.set_on_gold_shop_tab_click(self.toggle_gold_shop_panel)

    