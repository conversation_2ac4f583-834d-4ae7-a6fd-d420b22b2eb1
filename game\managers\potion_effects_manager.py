# -*- coding: utf-8 -*-
"""
药水效果管理器
统一管理所有药水的恢复效果和冷却时间，支持缓慢恢复和瞬间恢复机制
"""

import time
import threading
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass


@dataclass
class PotionEffect:
    """药水效果数据类"""
    name: str
    total_amount: int  # 总恢复量
    recovery_type: str  # "slow" 或 "instant"
    effect_type: str  # "hp" 或 "mp"
    remaining_amount: int  # 剩余恢复量
    duration: float  # 总持续时间
    tick_interval: float  # 每次恢复间隔
    start_time: float  # 开始时间
    last_tick_time: float  # 上次恢复时间


class PotionEffectsManager:
    """
    药水效果管理器
    负责管理所有药水的恢复效果和冷却时间
    """
    
    def __init__(self, player=None):
        """
        初始化药水效果管理器
        
        Args:
            player: 玩家对象引用
        """
        self.player = player
        
        # 当前激活的药水效果列表
        self.active_effects: List[PotionEffect] = []
        
        # 药水冷却时间字典 {药水名称: 冷却结束时间}
        self.potion_cooldowns: Dict[str, float] = {}
        
        # 效果更新线程
        self.update_thread = None
        self.running = False
        
        # 线程锁
        self.lock = threading.Lock()
        
        print("✅ 药水效果管理器初始化完成")
    
    def start(self):
        """启动效果更新线程"""
        if not self.running:
            self.running = True
            self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
            self.update_thread.start()
            print("🔄 药水效果更新线程已启动")
    
    def stop(self):
        """停止效果更新线程"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=1.0)
        print("⏹️ 药水效果更新线程已停止")
    
    def use_potion(self, potion_name: str, effects_config: Dict[str, Any]) -> bool:
        """
        使用药水
        
        Args:
            potion_name: 药水名称
            effects_config: 药水效果配置
            
        Returns:
            bool: 是否成功使用
        """
        try:
            with self.lock:
                # 检查冷却时间
                if not self._check_cooldown(potion_name):
                    remaining = self.get_cooldown_remaining(potion_name)
                    print(f"❄️ {potion_name} 冷却中，剩余 {remaining:.1f}秒")
                    return False
                
                # 获取药水配置
                hp_restore = effects_config.get('hp_restore', 0)
                mp_restore = effects_config.get('mp_restore', 0)
                recovery_type = effects_config.get('recovery_type', 'instant')
                cooldown = effects_config.get('cooldown', 3000) / 1000  # 转换为秒
                
                # 设置冷却时间
                self._set_cooldown(potion_name, cooldown)
                
                # 应用药水效果
                success = False
                
                if hp_restore > 0:
                    success = self._apply_effect(potion_name, hp_restore, recovery_type, 'hp') or success
                
                if mp_restore > 0:
                    success = self._apply_effect(potion_name, mp_restore, recovery_type, 'mp') or success
                
                if success:
                    print(f"💊 成功使用 {potion_name} - 恢复类型: {recovery_type}")
                
                return success
                
        except Exception as e:
            print(f"❌ 使用药水时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _apply_effect(self, potion_name: str, amount: int, recovery_type: str, effect_type: str) -> bool:
        """
        应用药水效果
        
        Args:
            potion_name: 药水名称
            amount: 恢复量
            recovery_type: 恢复类型 ("slow" 或 "instant")
            effect_type: 效果类型 ("hp" 或 "mp")
            
        Returns:
            bool: 是否成功应用
        """
        try:
            current_time = time.time()
            
            if recovery_type == "instant":
                # 瞬间恢复
                return self._instant_recovery(amount, effect_type, potion_name)
            
            elif recovery_type == "slow":
                # 缓慢恢复 - 创建效果对象
                duration = 3.0  # 3秒内恢复完成
                tick_interval = 0.5  # 每0.5秒恢复一次
                
                effect = PotionEffect(
                    name=f"{potion_name}_{effect_type}_{int(current_time)}",
                    total_amount=amount,
                    recovery_type=recovery_type,
                    effect_type=effect_type,
                    remaining_amount=amount,
                    duration=duration,
                    tick_interval=tick_interval,
                    start_time=current_time,
                    last_tick_time=current_time
                )
                
                self.active_effects.append(effect)
                print(f"🔄 开始缓慢恢复 {effect_type.upper()}: {amount} 点 (持续 {duration}秒，每{tick_interval}秒恢复一次)")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ 应用药水效果时出错: {e}")
            return False
    
    def _instant_recovery(self, amount: int, effect_type: str, potion_name: str) -> bool:
        """
        瞬间恢复
        
        Args:
            amount: 恢复量
            effect_type: 效果类型 ("hp" 或 "mp")
            potion_name: 药水名称
            
        Returns:
            bool: 是否成功恢复
        """
        try:
            if not self.player:
                return False
            
            if effect_type == "hp":
                return self._restore_hp(amount, potion_name)
            elif effect_type == "mp":
                return self._restore_mp(amount, potion_name)
            
            return False
            
        except Exception as e:
            print(f"❌ 瞬间恢复时出错: {e}")
            return False
    
    def _restore_hp(self, amount: int, source: str = "") -> bool:
        """恢复玩家HP"""
        try:
            if not self.player or not hasattr(self.player, 'max_hp'):
                return False
            
            # 优先使用current_hp属性
            if hasattr(self.player, 'current_hp'):
                old_hp = self.player.current_hp
                self.player.current_hp = min(self.player.current_hp + amount, self.player.max_hp)
                restored = self.player.current_hp - old_hp
                print(f"💚 恢复HP: +{restored} (当前: {self.player.current_hp}/{self.player.max_hp}) [{source}]")
            else:
                old_hp = self.player.hp
                self.player.hp = min(self.player.hp + amount, self.player.max_hp)
                restored = self.player.hp - old_hp
                print(f"💚 恢复HP: +{restored} (当前: {self.player.hp}/{self.player.max_hp}) [{source}]")
            
            return restored > 0
            
        except Exception as e:
            print(f"❌ 恢复HP时出错: {e}")
            return False
    
    def _restore_mp(self, amount: int, source: str = "") -> bool:
        """恢复玩家MP"""
        try:
            if not self.player or not hasattr(self.player, 'max_mp'):
                return False
            
            old_mp = self.player.mp
            self.player.mp = min(self.player.mp + amount, self.player.max_mp)
            restored = self.player.mp - old_mp
            print(f"💙 恢复MP: +{restored} (当前: {self.player.mp}/{self.player.max_mp}) [{source}]")
            
            return restored > 0
            
        except Exception as e:
            print(f"❌ 恢复MP时出错: {e}")
            return False
    
    def _check_cooldown(self, potion_name: str) -> bool:
        """检查药水冷却时间"""
        if potion_name not in self.potion_cooldowns:
            return True
        
        return time.time() >= self.potion_cooldowns[potion_name]
    
    def _set_cooldown(self, potion_name: str, cooldown_seconds: float):
        """设置药水冷却时间"""
        self.potion_cooldowns[potion_name] = time.time() + cooldown_seconds
    
    def get_cooldown_remaining(self, potion_name: str) -> float:
        """获取药水剩余冷却时间"""
        if potion_name not in self.potion_cooldowns:
            return 0.0
        
        remaining = self.potion_cooldowns[potion_name] - time.time()
        return max(0.0, remaining)
    
    def get_all_cooldowns(self) -> Dict[str, float]:
        """获取所有药水的冷却状态"""
        current_time = time.time()
        cooldowns = {}
        
        for potion_name, cooldown_end in self.potion_cooldowns.items():
            remaining = max(0.0, cooldown_end - current_time)
            if remaining > 0:
                cooldowns[potion_name] = remaining
        
        return cooldowns
    
    def get_active_effects(self) -> List[Dict[str, Any]]:
        """获取当前激活的药水效果"""
        with self.lock:
            effects = []
            for effect in self.active_effects:
                effects.append({
                    'name': effect.name,
                    'type': effect.effect_type,
                    'remaining': effect.remaining_amount,
                    'progress': (effect.total_amount - effect.remaining_amount) / effect.total_amount
                })
            return effects
    
    def _update_loop(self):
        """效果更新循环线程"""
        while self.running:
            try:
                with self.lock:
                    current_time = time.time()
                    effects_to_remove = []
                    
                    for effect in self.active_effects:
                        # 检查是否需要执行下一次恢复
                        if current_time - effect.last_tick_time >= effect.tick_interval:
                            # 计算本次恢复量 - 确保在持续时间内恢复完所有数值
                            ticks_total = int(effect.duration / effect.tick_interval)  # 总恢复次数
                            ticks_remaining = max(1, ticks_total - int((current_time - effect.start_time) / effect.tick_interval) + 1)
                            tick_amount = min(
                                effect.remaining_amount,
                                max(1, int(effect.remaining_amount / ticks_remaining))
                            )
                            
                            if tick_amount > 0:
                                # 执行恢复
                                if effect.effect_type == "hp":
                                    self._restore_hp(tick_amount, f"缓慢恢复({effect.name})")
                                elif effect.effect_type == "mp":
                                    self._restore_mp(tick_amount, f"缓慢恢复({effect.name})")
                                
                                # 更新状态
                                effect.remaining_amount -= tick_amount
                                effect.last_tick_time = current_time
                        
                        # 检查效果是否完成
                        if (effect.remaining_amount <= 0 or 
                            current_time - effect.start_time >= effect.duration):
                            effects_to_remove.append(effect)
                    
                    # 移除完成的效果
                    for effect in effects_to_remove:
                        if effect in self.active_effects:
                            self.active_effects.remove(effect)
                            print(f"✅ 药水效果完成: {effect.name}")
                
                # 休眠0.5秒
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ 药水效果更新时出错: {e}")
                time.sleep(1.0)
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        with self.lock:
            self.active_effects.clear()
            self.potion_cooldowns.clear()
        print("🧹 药水效果管理器已清理") 