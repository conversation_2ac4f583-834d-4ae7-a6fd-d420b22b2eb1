"""
改进的装备面板
使用和背包相同的浮窗实现方法
"""
import json
import pygame
from typing import Dict, Any, Optional

from game.ui.ui_panel import UIPanel
from game.ui.item_slot import ItemSlot
from game.systems.item_generator import Item
from game.ui.components.equipment_stats_formatter import equipment_stats_formatter
from game.ui.components.equipment_image_cache import equipment_image_cache
from game.core.resource_manager import get_game_data_path


class ImprovedEquipmentPanel(UIPanel):
    """改进的装备面板类 - 使用和背包相同的浮窗方法"""
    
    def __init__(self, screen, player, position, size):
        """
        初始化装备面板
        
        Args:
            screen: 屏幕对象
            player: 玩家对象
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
        """
        super().__init__(screen, position, size)
        
        # 玩家引用
        self.player = player
        
        # 加载配置
        self.config = self._load_config()
        
        # 设置面板属性
        self.title = "装备面板"
        self.background_color = tuple(self.config['theme']['colors']['background'])
        
        # 初始化组件
        self._init_layout()
        self._init_buttons()
        
        # 状态管理
        self.show_attributes = False
        self.equipment_changed = True
        self._cached_equipment_stats = None
        self._last_equipment_count = 0
        
        # 预加载当前装备图片
        self._preload_current_equipment()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载面板配置"""
        try:
            config_path = get_game_data_path("equipment_panel_config.json")
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载装备面板配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "layout": {
                "equipment_slots": {
                    "slot_size": 40,
                    "positions": {
                        "weapon": {"x": -60, "y": -100, "display_name": "武器"},
                        "armor": {"x": -120, "y": -20, "display_name": "胸甲"},
                        "hat": {"x": 0, "y": -140, "display_name": "头盔"},
                        "ring1": {"x": 120, "y": -20, "display_name": "戒指1"},
                        "ring2": {"x": 120, "y": 40, "display_name": "戒指2"},
                        "amulet": {"x": 0, "y": 100, "display_name": "护符"},
                        "shoes": {"x": 0, "y": 60, "display_name": "靴子"},
                        "leggings": {"x": -60, "y": 60, "display_name": "护腿"},
                        "bracelet1": {"x": -120, "y": 40, "display_name": "手镯1"},
                        "bracelet2": {"x": -120, "y": 80, "display_name": "手镯2"},
                        "offhand": {"x": 60, "y": -100, "display_name": "副手"},
                        "artifact": {"x": 0, "y": -80, "display_name": "圣物"},
                        "special1": {"x": -180, "y": -80, "display_name": "特殊1"},
                        "special2": {"x": -180, "y": 0, "display_name": "特殊2"},
                        "special3": {"x": -180, "y": 80, "display_name": "特殊3"}
                    }
                }
            },
            "theme": {
                "colors": {
                    "background": [20, 20, 20],
                    "slot_empty": [40, 40, 40],
                    "slot_equipped": [60, 80, 60]
                }
            }
        }
    
    def _init_layout(self):
        """初始化布局 - 使用ItemSlot"""
        layout_config = self.config['layout']
        
        # 装备槽位配置
        self.slot_size = layout_config['equipment_slots']['slot_size']
        self.equipment_slots = {}
        self.item_slots = {}  # ItemSlot对象字典
        
        center_x = self.rect.centerx
        center_y = self.rect.centery
        
        # 根据配置创建装备槽位
        slot_positions = layout_config['equipment_slots']['positions']
        for slot_name, slot_config in slot_positions.items():
            abs_x = center_x + slot_config['x']
            abs_y = center_y + slot_config['y']
            
            # 创建ItemSlot对象
            item_slot = ItemSlot(
                self.screen,
                (abs_x, abs_y),
                (self.slot_size, self.slot_size)
            )
            
            self.equipment_slots[slot_name] = {
                "position": (abs_x, abs_y),
                "equipped": None,
                "display_name": slot_config['display_name'],
                "rect": pygame.Rect(abs_x, abs_y, self.slot_size, self.slot_size)
            }
            
            self.item_slots[slot_name] = item_slot
    
    def _init_buttons(self):
        """初始化按钮"""
        # 属性按钮
        self.add_button(
            "属性",
            (10, self.rect.height - 35, 60, 25),
            self.toggle_attributes
        )
        
        # 关闭按钮
        self.add_button(
            "关闭",
            (self.rect.width - 70, 10, 60, 25),
            self.close_panel
        )
    
    def _preload_current_equipment(self):
        """预加载当前装备图片"""
        if hasattr(self.player, 'equipment_manager'):
            equipped_items = self.player.equipment_manager.get_all_equipped_items()
            equipment_list = [item for item in equipped_items.values() if item]
            
            # 预加载装备图片
            size = (self.slot_size - 4, self.slot_size - 4)
            equipment_image_cache.preload_equipment_images(equipment_list, size)
    
    def update(self):
        """更新面板状态"""
        if not self.visible:
            return
            
        # 检查装备是否变更
        if hasattr(self.player, 'equipment_manager'):
            equipment_manager = self.player.equipment_manager
            
            if equipment_manager.check_equipment_changed():
                self.equipment_changed = True
                equipment_manager.mark_equipment_synced()
                self._preload_current_equipment()
            
            equipped_items = equipment_manager.get_all_equipped_items()
            current_count = sum(1 for item in equipped_items.values() if item)
            
            if current_count != self._last_equipment_count:
                self.equipment_changed = True
                self._last_equipment_count = current_count
                self._preload_current_equipment()
            
            # 更新装备槽位数据
            if self.equipment_changed:
                self._update_equipment_slots(equipped_items)
                self._cached_equipment_stats = None
                self.equipment_changed = False
    
    def _update_equipment_slots(self, equipped_items: Dict[str, Any]):
        """更新装备槽位数据"""
        slot_mapping = {
            "weapon": "武器",
            "hat": "头盔",
            "armor": "胸甲",
            "leggings": "护腿",
            "shoes": "靴子",
            "amulet": "护符",
            "ring1": "戒指1",
            "ring2": "戒指2",
            "bracelet1": "手镯1",
            "bracelet2": "手镯2",
            "offhand": "副手",
            "artifact": "圣物",
            "special1": "特殊1",
            "special2": "特殊2",
            "special3": "特殊3"
        }
        
        # 重置所有槽位
        for slot_name in self.equipment_slots.keys():
            self.equipment_slots[slot_name]["equipped"] = None
            self.item_slots[slot_name].clear_item()
        
        # 更新装备信息
        for ui_slot_name in self.equipment_slots.keys():
            manager_slot_name = slot_mapping.get(ui_slot_name)
            if manager_slot_name:
                equipped_item = equipped_items.get(manager_slot_name)
                if equipped_item:
                    self.equipment_slots[ui_slot_name]["equipped"] = equipped_item
                    
                    # 将装备设置到ItemSlot
                    item_data = self._convert_equipment_to_item(equipped_item)
                    self.item_slots[ui_slot_name].set_item(item_data, 1)
    
    def _convert_equipment_to_item(self, equipment_data: Dict[str, Any]):
        """将装备数据转换为Item对象格式"""
        # 构建Item所需的数据
        item_dict = {
            'id': equipment_data.get('id', equipment_data.get('name', 'unknown')),
            'name': equipment_data.get('name', '未知装备'),
            'type': 'equipment',
            'category': equipment_data.get('slot', '武器'),
            'rarity': equipment_data.get('rarity', '普通'),
            'icon_path': equipment_data.get('icon_path'),
            'description': equipment_data.get('description', ''),
            'attributes': equipment_data.get('stats', {}),
            'level_requirement': equipment_data.get('required_level', 1),
            'required_class': equipment_data.get('required_class', []),
            'sell_price': equipment_data.get('value', 0),
            'max_durability': equipment_data.get('durability', 100),
            'durability': equipment_data.get('current_durability', equipment_data.get('durability', 100))
        }
        
        # 创建Item对象
        return Item(item_dict, 1)
    
    def handle_event(self, event):
        """处理事件"""
        if not self.visible:
            return False
        
        # 处理鼠标点击
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self.rect.collidepoint(event.pos):
                # 左键点击
                if event.button == 1:
                    # 检查装备槽位点击
                    for slot_name, item_slot in self.item_slots.items():
                        if item_slot.handle_event(event):
                            return True
                    
                    # 调用父类的事件处理（按钮等）
                    if super().handle_event(event):
                        return True
                
                # 右键点击卸下装备
                elif event.button == 3:
                    for slot_name, item_slot in self.item_slots.items():
                        if item_slot.rect.collidepoint(event.pos) and item_slot.item:
                            # 卸下装备
                            self.unequip_item_from_slot(slot_name)
                            return True

                return True
            else:
                return False
        
        return super().handle_event(event)
    
    def unequip_item_from_slot(self, slot_key: str):
        """
        从指定UI槽位卸下装备
        
        Args:
            slot_key (str): UI槽位的键名 (e.g., 'weapon', 'hat')
        """
        # 将UI槽位名转换为管理器使用的槽位名
        slot_mapping = {
            "weapon": "武器", "hat": "头盔", "armor": "胸甲", "leggings": "护腿",
            "shoes": "靴子", "amulet": "护符", "ring1": "戒指1", "ring2": "戒指2",
            "bracelet1": "手镯1", "bracelet2": "手镯2", "offhand": "副手",
            "artifact": "圣物", "special1": "特殊1", "special2": "特殊2",
            "special3": "特殊3"
        }
        manager_slot_name = slot_mapping.get(slot_key)
        
        if not manager_slot_name:
            print(f"无效的装备槽位键: {slot_key}")
            return

        # 调用装备管理器卸下装备
        success = self.player.equipment_manager.unequip_item(manager_slot_name, self.player)
        
        if not success:
            # 提示玩家背包已满
            print("背包已满，无法卸下装备！")
            if hasattr(self.player, 'user_message_manager'):
                self.player.user_message_manager.add_message("背包已满，无法卸下装备！")
    
    def render(self):
        """渲染面板"""
        if not self.visible:
            return
        
        # 渲染基础面板
        super().render()
        
        # 渲染装备槽位和浮窗
        self._render_equipment_slots_with_tooltips()
        
        # 渲染属性信息
        if self.show_attributes:
            self._render_detailed_attributes()
        else:
            self._render_equipment_summary()
    
    def _render_equipment_slots_with_tooltips(self):
        """渲染装备槽位（包含浮窗）- 使用背包相同的方法"""
        colors = self.config['theme']['colors']
        mouse_pos = pygame.mouse.get_pos()
        
        # 先渲染所有槽位
        for slot_name, item_slot in self.item_slots.items():
            slot_data = self.equipment_slots[slot_name]
            
            # 绘制槽位背景
            if slot_data["equipped"]:
                bg_color = colors['slot_equipped']
                border_color = (120, 180, 120)
            else:
                bg_color = colors['slot_empty']
                border_color = (100, 100, 100)
            
            pygame.draw.rect(self.screen, bg_color, slot_data["rect"])
            pygame.draw.rect(self.screen, border_color, slot_data["rect"], 2)
            
            # 使用ItemSlot渲染物品
            item_slot.render()
            
            # 如果没有装备，显示槽位名称
            if not slot_data["equipped"]:
                text = self.small_font.render(slot_data["display_name"][:4], True, (150, 150, 150))
                text_rect = text.get_rect(center=slot_data["rect"].center)
                self.screen.blit(text, text_rect)
        
        # 检查鼠标悬停并渲染浮窗（和背包一样的方法）
        for slot_name, item_slot in self.item_slots.items():
            if item_slot.rect.collidepoint(mouse_pos) and item_slot.item:
                # 渲染装备浮窗
                item_slot.render_tooltip(mouse_pos, None)  # 装备面板不需要对比
                break  # 只显示一个浮窗
    
    def _render_equipment_summary(self):
        """渲染装备属性摘要"""
        # 获取装备属性（使用缓存）
        equipment_stats = self._get_cached_equipment_stats()
        
        # 格式化摘要信息
        summary_lines = equipment_stats_formatter.format_equipment_summary(equipment_stats)
        
        if not summary_lines:
            summary_lines = ["无装备加成"]
        
        # 渲染背景
        summary_x = self.rect.left + 10
        summary_y = self.rect.top + 30
        bg_width = 120
        bg_height = len(summary_lines) * 16 + 10
        
        bg_rect = pygame.Rect(summary_x, summary_y, bg_width, bg_height)
        bg_surface = pygame.Surface((bg_width, bg_height))
        bg_surface.set_alpha(150)
        bg_surface.fill((20, 20, 30))
        self.screen.blit(bg_surface, (summary_x, summary_y))
        
        pygame.draw.rect(self.screen, (80, 80, 100), bg_rect, 1)
        
        # 渲染文本
        for i, line in enumerate(summary_lines):
            text = self.small_font.render(line, True, (200, 200, 200))
            self.screen.blit(text, (summary_x + 5, summary_y + 5 + i * 16))
    
    def _render_detailed_attributes(self):
        """渲染详细属性面板"""
        # 创建详细属性面板
        panel_width = 400
        panel_height = 350
        panel_rect = pygame.Rect(
            self.rect.centerx - panel_width // 2,
            self.rect.centery - panel_height // 2,
            panel_width, panel_height
        )
        
        # 绘制面板背景
        pygame.draw.rect(self.screen, (20, 20, 25), panel_rect)
        pygame.draw.rect(self.screen, (120, 120, 140), panel_rect, 2)
        
        # 标题
        title_text = "详细属性面板"
        title_surface = self.normal_font.render(title_text, True, (220, 220, 170))
        title_rect = title_surface.get_rect(center=(panel_rect.centerx, panel_rect.top + 20))
        self.screen.blit(title_surface, title_rect)
        
        # 分割线
        line_y = panel_rect.top + 35
        pygame.draw.line(self.screen, (100, 100, 120), 
                        (panel_rect.left + 10, line_y), 
                        (panel_rect.right - 10, line_y), 2)
        
        # 获取玩家数据
        if not hasattr(self.player, 'name'):
            # 如果没有玩家数据，显示提示
            no_data_text = "无玩家数据"
            no_data_surface = self.small_font.render(no_data_text, True, (180, 180, 180))
            no_data_rect = no_data_surface.get_rect(center=panel_rect.center)
            self.screen.blit(no_data_surface, no_data_rect)
            return
        
        # 开始渲染属性信息
        start_y = panel_rect.top + 50
        line_height = 20
        current_y = start_y
        left_col_x = panel_rect.left + 15
        right_col_x = panel_rect.left + 200
        
        # 基本信息
        basic_info = [
            ("角色名称", getattr(self.player, 'name', '未知')),
            ("职业", getattr(self.player, 'character_class', '未知')),
            ("等级", f"{getattr(self.player, 'level', 1)}"),
            ("性别", getattr(self.player, 'gender', '未知')),
        ]
        
        for label, value in basic_info:
            # 标签
            label_surface = self.small_font.render(f"{label}:", True, (180, 180, 180))
            self.screen.blit(label_surface, (left_col_x, current_y))
            
            # 值
            value_surface = self.small_font.render(str(value), True, (220, 220, 220))
            self.screen.blit(value_surface, (left_col_x + 80, current_y))
            
            current_y += line_height
        
        # 分割线
        current_y += 5
        pygame.draw.line(self.screen, (80, 80, 100), 
                        (panel_rect.left + 10, current_y), 
                        (panel_rect.right - 10, current_y), 1)
        current_y += 10
        
        # 战斗属性 - 分两列显示
        combat_stats = self._get_player_combat_stats()
        
        # 左列属性
        left_stats = [
            ("生命值", f"{getattr(self.player, 'hp', 0)}/{self.player.get_max_hp() if hasattr(self.player, 'get_max_hp') else 100}"),
            ("魔法值", f"{getattr(self.player, 'mp', 0)}/{self.player.get_max_mp() if hasattr(self.player, 'get_max_mp') else 50}"),
            ("物理攻击", f"{combat_stats.get('攻击下限', 0)}-{combat_stats.get('攻击上限', 0)}"),
            ("魔法攻击", f"{combat_stats.get('魔法攻击下限', 0)}-{combat_stats.get('魔法攻击上限', 0)}"),
            ("道术攻击", f"{combat_stats.get('道术攻击下限', 0)}-{combat_stats.get('道术攻击上限', 0)}"),
        ]
        
        # 右列属性
        right_stats = [
            ("防御力", f"{combat_stats.get('防御下限', 0)}-{combat_stats.get('防御上限', 0)}"),
            ("魔抗", f"{combat_stats.get('魔抗', 0)}"),
            ("准确", f"{combat_stats.get('准确', 0)}"),
            ("敏捷", f"{combat_stats.get('敏捷', 0)}"),
            ("攻速", f"{combat_stats.get('攻速', 1.0):.1f}"),
        ]
        
        # 渲染左列
        left_y = current_y
        for label, value in left_stats:
            label_surface = self.small_font.render(f"{label}:", True, (160, 200, 160))
            self.screen.blit(label_surface, (left_col_x, left_y))
            
            value_surface = self.small_font.render(str(value), True, (200, 255, 200))
            self.screen.blit(value_surface, (left_col_x + 80, left_y))
            
            left_y += line_height
        
        # 渲染右列
        right_y = current_y
        for label, value in right_stats:
            label_surface = self.small_font.render(f"{label}:", True, (160, 200, 160))
            self.screen.blit(label_surface, (right_col_x, right_y))
            
            value_surface = self.small_font.render(str(value), True, (200, 255, 200))
            self.screen.blit(value_surface, (right_col_x + 80, right_y))
            
            right_y += line_height
        
        # 底部信息区域
        footer_y = max(left_y, right_y) + 15
        
        # 显示总战力
        total_power = getattr(self.player, 'battle_power', 0)
        if total_power > 0:
            power_text = f"战斗力: {total_power}"
            power_surface = self.small_font.render(power_text, True, (255, 215, 0))
            power_rect = power_surface.get_rect(center=(panel_rect.centerx, footer_y))
            self.screen.blit(power_surface, power_rect)
    
    def _get_cached_equipment_stats(self) -> Dict[str, Any]:
        """获取缓存的装备属性"""
        if self._cached_equipment_stats is None and hasattr(self.player, 'equipment_manager'):
            self._cached_equipment_stats = self.player.equipment_manager.get_equipment_stats()
        
        return self._cached_equipment_stats or {}
    
    def _get_player_combat_stats(self) -> Dict[str, Any]:
        """获取玩家战斗属性"""
        combat_stats = {}
        
        # 基础属性
        if hasattr(self.player, 'stats'):
            player_stats = self.player.stats
            
            if hasattr(player_stats, 'get_stat'):
                # 使用CharacterStats对象的get_stat方法
                combat_stats.update({
                    "攻击下限": player_stats.get_stat('攻击力'),
                    "攻击上限": player_stats.get_stat('攻击力'),
                    "魔法攻击下限": player_stats.get_stat('魔法力'),
                    "魔法攻击上限": player_stats.get_stat('魔法力'),
                    "道术攻击下限": player_stats.get_stat('道术力'),
                    "道术攻击上限": player_stats.get_stat('道术力'),
                    "防御下限": player_stats.get_stat('防御力'),
                    "防御上限": player_stats.get_stat('防御力'),
                    "魔抗": player_stats.get_stat('魔法防御'),
                    "准确": player_stats.get_stat('准确'),
                    "敏捷": player_stats.get_stat('敏捷'),
                    "攻速": player_stats.get_stat('攻速') or 1.0,
                })
            elif hasattr(player_stats, 'get'):
                # 如果是字典类型
                combat_stats.update({
                    "攻击下限": player_stats.get('攻击力', 0),
                    "攻击上限": player_stats.get('攻击力', 0),
                    "魔法攻击下限": player_stats.get('魔法力', 0),
                    "魔法攻击上限": player_stats.get('魔法力', 0),
                    "道术攻击下限": player_stats.get('道术力', 0),
                    "道术攻击上限": player_stats.get('道术力', 0),
                    "防御下限": player_stats.get('防御力', 0),
                    "防御上限": player_stats.get('防御力', 0),
                    "魔抗": player_stats.get('魔法防御', 0),
                    "准确": player_stats.get('准确', 0),
                    "敏捷": player_stats.get('敏捷', 0),
                    "攻速": player_stats.get('攻速', 1.0),
                })
            else:
                # 其他情况，尝试直接访问属性
                combat_stats = {
                    "攻击下限": getattr(player_stats, 'attack_min', 0),
                    "攻击上限": getattr(player_stats, 'attack_max', 0),
                    "魔法攻击下限": getattr(player_stats, 'magic_min', 0),
                    "魔法攻击上限": getattr(player_stats, 'magic_max', 0),
                    "道术攻击下限": getattr(player_stats, 'tao_min', 0),
                    "道术攻击上限": getattr(player_stats, 'tao_max', 0),
                    "防御下限": getattr(player_stats, 'defense_min', 0),
                    "防御上限": getattr(player_stats, 'defense_max', 0),
                    "魔抗": getattr(player_stats, 'magic_defense', 0),
                    "准确": getattr(player_stats, 'accuracy', 0),
                    "敏捷": getattr(player_stats, 'agility', 0),
                    "攻速": getattr(player_stats, 'attack_speed', 1.0),
                }
        else:
            # 使用默认值或从玩家对象直接获取
            combat_stats = {
                "攻击下限": getattr(self.player, 'attack_min', 0),
                "攻击上限": getattr(self.player, 'attack_max', 0),
                "魔法攻击下限": getattr(self.player, 'magic_min', 0),
                "魔法攻击上限": getattr(self.player, 'magic_max', 0),
                "道术攻击下限": getattr(self.player, 'tao_min', 0),
                "道术攻击上限": getattr(self.player, 'tao_max', 0),
                "防御下限": getattr(self.player, 'defense_min', 0),
                "防御上限": getattr(self.player, 'defense_max', 0),
                "魔抗": getattr(self.player, 'magic_defense', 0),
                "准确": getattr(self.player, 'accuracy', 0),
                "敏捷": getattr(self.player, 'agility', 0),
                "攻速": getattr(self.player, 'attack_speed', 1.0),
            }
        
        # 叠加装备属性
        equipment_stats = self._get_cached_equipment_stats()
        for stat_name, equipment_bonus in equipment_stats.items():
            if stat_name in combat_stats:
                combat_stats[stat_name] += equipment_bonus
        
        return combat_stats
    
    def toggle_attributes(self):
        """切换属性显示"""
        self.show_attributes = not self.show_attributes
    
    def close_panel(self):
        """关闭面板"""
        self.visible = False
        self.show_attributes = False


# 如果原来有HoverManager类，可以删除它，因为不再需要
# class HoverManager: 已删除