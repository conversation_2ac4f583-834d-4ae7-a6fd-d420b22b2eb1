#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pygame
import sys
import os
from typing import Dict, Any, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from game.managers.points_shop_manager import PointsShopManager
except ImportError:
    # 如果无法导入，创建一个简单的占位符
    class PointsShopManager:
        def __init__(self):
            pass
        def get_categories(self):
            return {}
        def get_category_items(self, category):
            return []
        def can_purchase_item(self, player, item_id, category):
            return {'can_purchase': False, 'reason': '系统错误'}
        def purchase_item(self, player, item_id, category, quantity=1):
            return {'success': False, 'message': '系统错误'}

class PointsShopPanel:
    """
    积分商城面板UI组件
    显示商城分类、商品列表和购买功能
    """
    
    def __init__(self, screen, font_manager, ui_manager, player=None):
        """
        初始化积分商城面板
        
        Args:
            screen: pygame屏幕对象
            font_manager: 字体管理器
            ui_manager: UI管理器
            player: 玩家对象
        """
        self.screen = screen
        self.font_manager = font_manager
        self.ui_manager = ui_manager
        self.player = player
        self.shop_manager = PointsShopManager()
        
        # 面板尺寸和位置
        self.width = 800
        self.height = 600
        self.x = (screen.get_width() - self.width) // 2
        self.y = (screen.get_height() - self.height) // 2
        
        # 深色主题配色（与游戏风格一致）
        self.colors = {
            'background': (15, 15, 15),           # 深黑背景
            'panel_bg': (25, 25, 25),             # 面板背景
            'border': (60, 60, 60),               # 边框颜色
            'category_bg': (35, 35, 35),          # 分类背景
            'category_selected': (50, 50, 80),    # 选中分类
            'category_hover': (40, 40, 60),       # 分类悬停
            'item_bg': (30, 30, 30),              # 商品背景
            'item_hover': (45, 45, 45),           # 商品悬停
            'button_normal': (70, 70, 70),        # 按钮正常
            'button_hover': (90, 90, 90),         # 按钮悬停
            'button_disabled': (40, 40, 40),      # 按钮禁用
            'text_primary': (220, 220, 220),      # 主要文字
            'text_secondary': (180, 180, 180),    # 次要文字
            'text_disabled': (120, 120, 120),     # 禁用文字
            'points_color': (255, 215, 0),        # 积分颜色（金色）
            'success': (100, 200, 100),           # 成功颜色
            'error': (200, 100, 100),             # 错误颜色
            'warning': (200, 200, 100)            # 警告颜色
        }
        
        # 状态变量
        self.visible = False
        self.current_category = '道具'  # 默认选中道具分类
        self.selected_item = None
        self.message = ''
        self.message_color = self.colors['text_primary']
        self.message_timer = 0
        
        # 按钮状态
        self.button_states = {
            'close_button_rect': None,
            'close_hover': False,
            'category_buttons': {},
            'item_buttons': {},
            'purchase_button_rect': None,
            'purchase_hover': False
        }
        
        # 滚动相关
        self.scroll_offset = 0
        self.max_scroll = 0
        
        # 面板区域矩形（用于外部点击检测）
        self.rect = pygame.Rect(self.x, self.y, self.width, self.height)
        
    def show(self):
        """
        显示积分商城面板
        """
        self.visible = True
        self.current_category = '道具'  # 重置到默认分类
        self.selected_item = None
        self.scroll_offset = 0
        self._update_scroll_limits()
        
    def hide(self):
        """
        隐藏积分商城面板
        """
        self.visible = False
        
    def handle_event(self, event):
        """
        处理事件
        
        Args:
            event: pygame事件对象
        """
        if not self.visible:
            return
            
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # 左键点击
                mouse_pos = pygame.mouse.get_pos()
                
                # 检查关闭按钮
                if (self.button_states['close_button_rect'] and 
                    self.button_states['close_button_rect'].collidepoint(mouse_pos)):
                    self.hide()
                    return
                
                # 检查分类按钮
                for category, rect in self.button_states['category_buttons'].items():
                    if rect.collidepoint(mouse_pos):
                        self.current_category = category
                        self.selected_item = None
                        self.scroll_offset = 0
                        self._update_scroll_limits()
                        return
                
                # 检查商品按钮
                for item_id, rect in self.button_states['item_buttons'].items():
                    if rect.collidepoint(mouse_pos):
                        self.selected_item = item_id
                        return
                
                # 检查购买按钮
                if (self.button_states['purchase_button_rect'] and 
                    self.button_states['purchase_button_rect'].collidepoint(mouse_pos) and
                    self.selected_item):
                    self._purchase_selected_item()
                    return
            
            elif event.button == 4:  # 鼠标滚轮向上
                self.scroll_offset = max(0, self.scroll_offset - 30)
            elif event.button == 5:  # 鼠标滚轮向下
                self.scroll_offset = min(self.max_scroll, self.scroll_offset + 30)
        
        elif event.type == pygame.MOUSEMOTION:
            mouse_pos = pygame.mouse.get_pos()
            
            # 更新悬停状态
            self.button_states['close_hover'] = (
                self.button_states['close_button_rect'] and 
                self.button_states['close_button_rect'].collidepoint(mouse_pos)
            )
            
            self.button_states['purchase_hover'] = (
                self.button_states['purchase_button_rect'] and 
                self.button_states['purchase_button_rect'].collidepoint(mouse_pos)
            )
    
    def update(self, dt):
        """
        更新面板状态
        
        Args:
            dt: 时间增量
        """
        if not self.visible:
            return
            
        # 更新消息显示计时器
        if self.message_timer > 0:
            self.message_timer -= dt
            if self.message_timer <= 0:
                self.message = ''
    
    def render(self):
        """
        渲染积分商城面板
        """
        if not self.visible:
            return
            
        # 绘制半透明背景
        overlay = pygame.Surface((self.screen.get_width(), self.screen.get_height()))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))
        
        # 绘制主面板
        panel_rect = pygame.Rect(self.x, self.y, self.width, self.height)
        pygame.draw.rect(self.screen, self.colors['panel_bg'], panel_rect)
        pygame.draw.rect(self.screen, self.colors['border'], panel_rect, 2)
        
        # 更新面板区域矩形（用于外部点击检测）
        self.rect = panel_rect
        
        # 绘制标题
        self._render_title()
        
        # 绘制玩家积分信息
        self._render_player_points()
        
        # 绘制分类标签
        self._render_categories()
        
        # 绘制商品列表
        self._render_items()
        
        # 绘制商品详情
        self._render_item_details()
        
        # 绘制购买按钮
        self._render_purchase_button()
        
        # 绘制关闭按钮
        self._render_close_button()
        
        # 绘制消息
        self._render_message()
    
    def _render_title(self):
        """
        渲染标题
        """
        title_font = self.font_manager.get_font('default', 24)
        title_text = title_font.render('积分商城', True, self.colors['text_primary'])
        title_x = self.x + (self.width - title_text.get_width()) // 2
        title_y = self.y + 15
        self.screen.blit(title_text, (title_x, title_y))
    
    def _render_player_points(self):
        """
        渲染玩家积分信息
        """
        points = self.shop_manager._get_player_points(self.player) if self.player else 0
        points_font = self.font_manager.get_font('default', 18)
        points_text = points_font.render(f'当前积分: {points}', True, self.colors['points_color'])
        # 调整积分显示位置，避免被关闭按钮遮挡
        # 关闭按钮位于右上角，宽度30px + 10px边距 = 40px
        # 将积分显示向左移动更多距离，并向下移动一些
        points_x = self.x + self.width - points_text.get_width() - 60  # 从20增加到60，避开关闭按钮
        points_y = self.y + 45  # 从20增加到45，向下移动
        self.screen.blit(points_text, (points_x, points_y))
    
    def _render_categories(self):
        """
        渲染分类标签
        """
        categories = self.shop_manager.get_categories()
        category_y = self.y + 60
        category_width = 120
        category_height = 40
        start_x = self.x + 20
        
        self.button_states['category_buttons'] = {}
        
        for i, (category_name, category_info) in enumerate(categories.items()):
            category_x = start_x + i * (category_width + 10)
            category_rect = pygame.Rect(category_x, category_y, category_width, category_height)
            
            # 选择颜色
            if category_name == self.current_category:
                bg_color = self.colors['category_selected']
            else:
                bg_color = self.colors['category_bg']
            
            # 绘制分类按钮
            pygame.draw.rect(self.screen, bg_color, category_rect)
            pygame.draw.rect(self.screen, self.colors['border'], category_rect, 1)
            
            # 绘制分类文字
            category_font = self.font_manager.get_font('default', 16)
            text_color = self.colors['text_primary']
            category_text = category_font.render(category_name, True, text_color)
            text_x = category_x + (category_width - category_text.get_width()) // 2
            text_y = category_y + (category_height - category_text.get_height()) // 2
            self.screen.blit(category_text, (text_x, text_y))
            
            # 保存按钮区域
            self.button_states['category_buttons'][category_name] = category_rect
    
    def _render_items(self):
        """
        渲染商品列表
        """
        items = self.shop_manager.get_category_items(self.current_category)
        
        # 商品列表区域
        items_area_x = self.x + 20
        items_area_y = self.y + 120
        items_area_width = 350
        items_area_height = 400
        
        # 绘制商品列表背景
        items_bg_rect = pygame.Rect(items_area_x, items_area_y, items_area_width, items_area_height)
        pygame.draw.rect(self.screen, self.colors['background'], items_bg_rect)
        pygame.draw.rect(self.screen, self.colors['border'], items_bg_rect, 1)
        
        # 设置裁剪区域
        self.screen.set_clip(items_bg_rect)
        
        self.button_states['item_buttons'] = {}
        item_height = 60
        
        for i, item in enumerate(items):
            item_y = items_area_y + 10 + i * (item_height + 5) - self.scroll_offset
            
            # 跳过不在可见区域的商品
            if item_y + item_height < items_area_y or item_y > items_area_y + items_area_height:
                continue
            
            item_rect = pygame.Rect(items_area_x + 5, item_y, items_area_width - 10, item_height)
            
            # 选择颜色
            if item['item_id'] == self.selected_item:
                bg_color = self.colors['item_hover']
            else:
                bg_color = self.colors['item_bg']
            
            # 绘制商品背景
            pygame.draw.rect(self.screen, bg_color, item_rect)
            pygame.draw.rect(self.screen, self.colors['border'], item_rect, 1)
            
            # 绘制商品信息
            name_font = self.font_manager.get_font('default', 16)
            desc_font = self.font_manager.get_font('default', 12)
            cost_font = self.font_manager.get_font('default', 14)
            
            # 商品名称
            name_text = name_font.render(item['name'], True, self.colors['text_primary'])
            self.screen.blit(name_text, (item_rect.x + 10, item_rect.y + 5))
            
            # 商品描述
            desc_text = desc_font.render(item['description'], True, self.colors['text_secondary'])
            self.screen.blit(desc_text, (item_rect.x + 10, item_rect.y + 25))
            
            # 积分价格
            cost = item['points_cost']
            if self.player:
                cost = self.shop_manager._calculate_item_cost(self.player, item)
            cost_text = cost_font.render(f'{cost} 积分', True, self.colors['points_color'])
            cost_x = item_rect.right - cost_text.get_width() - 10
            self.screen.blit(cost_text, (cost_x, item_rect.y + 20))
            
            # 保存按钮区域
            self.button_states['item_buttons'][item['item_id']] = item_rect
        
        # 取消裁剪
        self.screen.set_clip(None)
        
        # 更新滚动限制
        self._update_scroll_limits()
    
    def _render_item_details(self):
        """
        渲染选中商品的详细信息
        """
        if not self.selected_item:
            return
        
        items = self.shop_manager.get_category_items(self.current_category)
        selected_item_info = None
        for item in items:
            if item['item_id'] == self.selected_item:
                selected_item_info = item
                break
        
        if not selected_item_info:
            return
        
        # 详情区域
        details_x = self.x + 390
        details_y = self.y + 120
        details_width = 380
        details_height = 300
        
        # 绘制详情背景
        details_rect = pygame.Rect(details_x, details_y, details_width, details_height)
        pygame.draw.rect(self.screen, self.colors['background'], details_rect)
        pygame.draw.rect(self.screen, self.colors['border'], details_rect, 1)
        
        # 绘制商品详细信息
        y_offset = details_y + 20
        
        # 商品名称
        name_font = self.font_manager.get_font('default', 20)
        name_text = name_font.render(selected_item_info['name'], True, self.colors['text_primary'])
        self.screen.blit(name_text, (details_x + 20, y_offset))
        y_offset += 40
        
        # 商品描述
        desc_font = self.font_manager.get_font('default', 14)
        desc_text = desc_font.render(selected_item_info['description'], True, self.colors['text_secondary'])
        self.screen.blit(desc_text, (details_x + 20, y_offset))
        y_offset += 30
        
        # 价格信息
        cost = selected_item_info['points_cost']
        if self.player:
            cost = self.shop_manager._calculate_item_cost(self.player, selected_item_info)
        
        cost_font = self.font_manager.get_font('default', 18)
        cost_text = cost_font.render(f'价格: {cost} 积分', True, self.colors['points_color'])
        self.screen.blit(cost_text, (details_x + 20, y_offset))
        y_offset += 30
        
        # 每日限购
        daily_limit = selected_item_info.get('daily_limit', -1)
        if daily_limit > 0:
            limit_text = desc_font.render(f'每日限购: {daily_limit} 次', True, self.colors['text_secondary'])
            self.screen.blit(limit_text, (details_x + 20, y_offset))
            y_offset += 25
        
        # 检查购买条件
        if self.player:
            check_result = self.shop_manager.can_purchase_item(
                self.player, self.selected_item, self.current_category
            )
            if not check_result['can_purchase']:
                error_text = desc_font.render(check_result['reason'], True, self.colors['error'])
                self.screen.blit(error_text, (details_x + 20, y_offset))
    
    def _render_purchase_button(self):
        """
        渲染购买按钮
        """
        if not self.selected_item:
            self.button_states['purchase_button_rect'] = None
            return
        
        # 检查是否可以购买
        can_purchase = False
        if self.player:
            check_result = self.shop_manager.can_purchase_item(
                self.player, self.selected_item, self.current_category
            )
            can_purchase = check_result['can_purchase']
        
        # 按钮位置
        button_width = 120
        button_height = 40
        button_x = self.x + 390 + (380 - button_width) // 2
        button_y = self.y + 440
        
        button_rect = pygame.Rect(button_x, button_y, button_width, button_height)
        self.button_states['purchase_button_rect'] = button_rect
        
        # 选择颜色
        if not can_purchase:
            bg_color = self.colors['button_disabled']
            text_color = self.colors['text_disabled']
        elif self.button_states['purchase_hover']:
            bg_color = self.colors['button_hover']
            text_color = self.colors['text_primary']
        else:
            bg_color = self.colors['button_normal']
            text_color = self.colors['text_primary']
        
        # 绘制按钮
        pygame.draw.rect(self.screen, bg_color, button_rect)
        pygame.draw.rect(self.screen, self.colors['border'], button_rect, 2)
        
        # 绘制按钮文字
        button_font = self.font_manager.get_font('default', 16)
        button_text = button_font.render('购买', True, text_color)
        text_x = button_x + (button_width - button_text.get_width()) // 2
        text_y = button_y + (button_height - button_text.get_height()) // 2
        self.screen.blit(button_text, (text_x, text_y))
    
    def _render_close_button(self):
        """
        渲染关闭按钮
        """
        button_size = 30
        button_x = self.x + self.width - button_size - 10
        button_y = self.y + 10
        
        button_rect = pygame.Rect(button_x, button_y, button_size, button_size)
        self.button_states['close_button_rect'] = button_rect
        
        # 选择颜色
        if self.button_states['close_hover']:
            bg_color = self.colors['button_hover']
        else:
            bg_color = self.colors['button_normal']
        
        # 绘制按钮
        pygame.draw.rect(self.screen, bg_color, button_rect)
        pygame.draw.rect(self.screen, self.colors['border'], button_rect, 2)
        
        # 绘制X符号
        close_font = self.font_manager.get_font('default', 18)
        close_text = close_font.render('×', True, self.colors['text_primary'])
        text_x = button_x + (button_size - close_text.get_width()) // 2
        text_y = button_y + (button_size - close_text.get_height()) // 2
        self.screen.blit(close_text, (text_x, text_y))
    
    def _render_message(self):
        """
        渲染消息提示
        """
        if not self.message:
            return
        
        message_font = self.font_manager.get_font('default', 16)
        message_text = message_font.render(self.message, True, self.message_color)
        message_x = self.x + (self.width - message_text.get_width()) // 2
        message_y = self.y + self.height - 40
        
        # 绘制消息背景
        padding = 10
        bg_rect = pygame.Rect(
            message_x - padding, message_y - padding,
            message_text.get_width() + padding * 2,
            message_text.get_height() + padding * 2
        )
        pygame.draw.rect(self.screen, self.colors['panel_bg'], bg_rect)
        pygame.draw.rect(self.screen, self.colors['border'], bg_rect, 1)
        
        self.screen.blit(message_text, (message_x, message_y))
    
    def _update_scroll_limits(self):
        """
        更新滚动限制
        """
        items = self.shop_manager.get_category_items(self.current_category)
        item_height = 65  # 商品高度 + 间距
        total_height = len(items) * item_height
        visible_height = 400  # 可见区域高度
        
        self.max_scroll = max(0, total_height - visible_height)
    
    def _purchase_selected_item(self):
        """
        购买选中的商品
        """
        if not self.selected_item or not self.player:
            return
        
        result = self.shop_manager.purchase_item(
            self.player, self.selected_item, self.current_category, 1
        )
        
        if result['success']:
            self.message = result['message']
            self.message_color = self.colors['success']
        else:
            self.message = result['message']
            self.message_color = self.colors['error']
        
        self.message_timer = 3000  # 显示3秒
    
    def get_player_points(self) -> int:
        """
        获取玩家当前积分
        
        Returns:
            int: 玩家积分
        """
        if self.player:
            return self.shop_manager._get_player_points(self.player)
        return 0