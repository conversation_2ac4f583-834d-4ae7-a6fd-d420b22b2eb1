# 玩家死亡恢复系统

## 概述

为了避免玩家陷入死亡循环，我们实现了一个智能的死亡恢复系统。当玩家死亡并复活后，系统会自动重新刷新地图怪物，并在一定时间内避开导致死亡的危险怪物。

## 主要功能

### 1. 自动地图怪物刷新

**触发条件：** 玩家复活时

**功能描述：**
- 清除当前地图上的所有怪物
- 重新生成新的怪物分布
- 确保怪物位置分散，避免扎堆
- 强制重新加载小地图数据

**实现位置：** `game/core/battle_manager.py` - `_refresh_map_monsters_on_revival()` 方法

### 2. 危险怪物记录系统

**触发条件：** 玩家死亡时

**功能描述：**
- 记录导致玩家死亡的怪物信息（名称、等级、位置）
- 保留最近5次死亡记录
- 记录包含时间戳，用于时效性判断

**实现位置：** `game/core/battle_manager.py` - `_record_death_causing_monster()` 方法

### 3. 智能怪物选择

**触发条件：** 自动寻怪时

**功能描述：**
- 优先寻找安全的怪物（非危险怪物）
- 在30秒内避开导致死亡的同名同等级怪物
- 如果没有安全怪物，会警告玩家但仍可选择危险怪物

**实现位置：** `game/core/battle_manager.py` - `_find_safe_enemy_from_map()` 方法

## 系统流程

```
玩家死亡 → 记录危险怪物 → 复活倒计时 → 玩家复活 → 刷新地图怪物 → 智能寻怪
```

### 详细流程

1. **玩家死亡处理**
   ```python
   # 记录导致死亡的怪物
   self._record_death_causing_monster(self.defender)
   
   # 设置复活倒计时
   self.revival_time = 10.0  # 10秒复活时间
   ```

2. **玩家复活处理**
   ```python
   # 恢复玩家血量
   self.player_instance.current_hp = max_hp
   
   # 重新刷新地图怪物
   self._refresh_map_monsters_on_revival()
   
   # 重置战斗状态
   self.hunt_state = "idle"
   ```

3. **智能寻怪**
   ```python
   # 优先寻找安全怪物
   safe_enemy = self._find_safe_enemy_from_map(current_map, player_pos)
   
   # 如果没有安全怪物，使用普通寻怪但给出警告
   if not safe_enemy:
       nearest_enemy = current_map.find_nearest_enemy(player_pos)
       if self._is_dangerous_monster(nearest_enemy):
           self._log_event("⚠️ 发现危险怪物，建议谨慎应对")
   ```

## 配置参数

### 危险怪物判断参数
- **时效性：** 30秒内的死亡记录才被认为是危险的
- **等级容差：** 等级相差2级以内的同名怪物被认为是同类
- **记录数量：** 最多保留5次死亡记录

### 复活参数
- **复活时间：** 10秒倒计时
- **血量恢复：** 复活时恢复到满血状态

### 怪物刷新参数
- **清除范围：** 清除当前地图所有怪物
- **重新生成：** 根据地图配置重新生成怪物
- **分布算法：** 使用分散分布算法避免怪物扎堆

## 用户体验改进

### 1. 避免死亡循环
- 玩家不会在同一个位置反复遭遇同一个强力怪物
- 复活后有新的环境和机会

### 2. 智能提示
- 系统会提示玩家发现了危险怪物
- 记录危险怪物信息供玩家参考

### 3. 渐进式挑战
- 30秒后危险怪物记录失效，鼓励玩家再次尝试
- 保持游戏挑战性的同时避免挫败感

## 技术实现细节

### 数据结构

```python
# 危险怪物记录
monster_info = {
    'name': '怪物名称',
    'level': 怪物等级,
    'position': (x, y),
    'death_time': 时间戳
}

# 危险怪物列表
self._death_causing_monsters = [monster_info, ...]
```

### 关键方法

1. **`_refresh_map_monsters_on_revival()`** - 复活时刷新怪物
2. **`_record_death_causing_monster()`** - 记录危险怪物
3. **`_is_dangerous_monster()`** - 判断是否为危险怪物
4. **`_find_safe_enemy_from_map()`** - 寻找安全怪物

## 错误处理

- 所有关键方法都包含异常处理
- 即使刷新失败也会尝试备用方案
- 记录详细的错误日志便于调试

## 性能考虑

- 危险怪物记录限制在5条以内
- 使用时间戳进行高效的时效性判断
- 地图刷新使用现有的高效分布算法

这个系统确保玩家在死亡后能够获得一个公平的重新开始机会，同时保持游戏的挑战性和趣味性。
