#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试配置管理器
负责管理全局调试设置，控制所有模块的调试输出
用于优化内存使用和减少不必要的控制台输出

Author: Game Development Team
Date: 2024
"""

import json
import os
from typing import Dict, Any, Optional


class DebugConfigManager:
    """
    调试配置管理器
    
    功能:
    1. 加载和管理全局调试配置
    2. 提供统一的调试开关接口
    3. 支持运行时配置修改
    4. 内存优化设置管理
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化调试配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        self.config_path = config_path or self._get_default_config_path()
        self.config = {}
        self.load_config()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        # 获取当前文件所在目录的上级目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        game_dir = os.path.dirname(current_dir)
        return os.path.join(game_dir, "config", "debug_config.json")
    
    def load_config(self):
        """加载调试配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                # 使用默认配置
                self.config = self._get_default_config()
                self.save_config()  # 保存默认配置到文件
        except Exception as e:
            print(f"加载调试配置失败: {e}")
            self.config = self._get_default_config()
    
    def save_config(self):
        """保存调试配置"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存调试配置失败: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "debug_settings": {
                "global_debug_enabled": {"value": False},
                "console_output": {
                    "battle_manager": {"value": False},
                    "log_manager": {"value": False},
                    "monster_distribution": {"value": False},
                    "initialization": {"value": True}
                },
                "memory_optimization": {
                    "reduce_battle_logs": {"value": True},
                    "log_throttle_interval": {"value": 5.0},
                    "max_log_entries": {"value": 100}
                },
                "performance": {
                    "disable_debug_prints": {"value": True},
                    "minimal_console_output": {"value": True}
                }
            }
        }
    
    def is_debug_enabled(self, module_name: str = None) -> bool:
        """
        检查调试是否启用
        
        Args:
            module_name: 模块名称，如果为None则检查全局设置
            
        Returns:
            是否启用调试
        """
        # 首先检查全局调试开关
        global_debug = self.config.get("debug_settings", {}).get("global_debug_enabled", {}).get("value", False)
        
        if not global_debug:
            return False
        
        if module_name is None:
            return global_debug
        
        # 检查特定模块的调试设置
        console_output = self.config.get("debug_settings", {}).get("console_output", {})
        return console_output.get(module_name, {}).get("value", False)
    
    def is_console_output_enabled(self, module_name: str) -> bool:
        """
        检查模块的控制台输出是否启用
        
        Args:
            module_name: 模块名称
            
        Returns:
            是否启用控制台输出
        """
        console_output = self.config.get("debug_settings", {}).get("console_output", {})
        return console_output.get(module_name, {}).get("value", False)
    
    def get_log_throttle_interval(self) -> float:
        """获取日志节流间隔"""
        return self.config.get("debug_settings", {}).get("memory_optimization", {}).get("log_throttle_interval", {}).get("value", 5.0)
    
    def get_max_log_entries(self) -> int:
        """获取最大日志条数"""
        return self.config.get("debug_settings", {}).get("memory_optimization", {}).get("max_log_entries", {}).get("value", 100)
    
    def is_memory_optimization_enabled(self) -> bool:
        """检查是否启用内存优化"""
        return self.config.get("debug_settings", {}).get("memory_optimization", {}).get("reduce_battle_logs", {}).get("value", True)
    
    def is_minimal_console_output(self) -> bool:
        """检查是否启用最小化控制台输出"""
        return self.config.get("debug_settings", {}).get("performance", {}).get("minimal_console_output", {}).get("value", True)
    
    def set_debug_enabled(self, enabled: bool, module_name: str = None):
        """
        设置调试开关
        
        Args:
            enabled: 是否启用
            module_name: 模块名称，如果为None则设置全局开关
        """
        if module_name is None:
            # 设置全局调试开关
            if "debug_settings" not in self.config:
                self.config["debug_settings"] = {}
            if "global_debug_enabled" not in self.config["debug_settings"]:
                self.config["debug_settings"]["global_debug_enabled"] = {}
            self.config["debug_settings"]["global_debug_enabled"]["value"] = enabled
        else:
            # 设置特定模块的调试开关
            if "debug_settings" not in self.config:
                self.config["debug_settings"] = {}
            if "console_output" not in self.config["debug_settings"]:
                self.config["debug_settings"]["console_output"] = {}
            if module_name not in self.config["debug_settings"]["console_output"]:
                self.config["debug_settings"]["console_output"][module_name] = {}
            self.config["debug_settings"]["console_output"][module_name]["value"] = enabled
        
        self.save_config()
    
    def get_config_value(self, path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            path: 配置路径，使用点号分隔，如 "debug_settings.console_output.battle_manager.value"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = path.split('.')
        current = self.config
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        
        return current


# 全局调试配置管理器实例
_debug_config_manager = None


def get_debug_config_manager() -> DebugConfigManager:
    """获取全局调试配置管理器实例"""
    global _debug_config_manager
    if _debug_config_manager is None:
        _debug_config_manager = DebugConfigManager()
    return _debug_config_manager


def is_debug_enabled(module_name: str = None) -> bool:
    """快捷函数：检查调试是否启用"""
    return get_debug_config_manager().is_debug_enabled(module_name)


def is_console_output_enabled(module_name: str) -> bool:
    """快捷函数：检查控制台输出是否启用"""
    return get_debug_config_manager().is_console_output_enabled(module_name)
