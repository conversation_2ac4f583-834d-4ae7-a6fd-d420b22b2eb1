"""
开始菜单面板
显示游戏标题和菜单选项
"""
import pygame
import sys
import os
from game.ui.ui_panel import UIPanel
from game.core.resource_manager import get_game_asset_path, resource_exists

class StartMenuPanel(UIPanel):
    """开始菜单面板类"""
    
    def __init__(self, screen, position=(0, 0), size=(1280, 720)):
        """
        初始化开始菜单面板
        
        Args:
            screen: 屏幕对象
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
        """
        super().__init__(screen, position, size)
        
        # 隐藏面板边框和标题（全屏菜单）
        self.show_border = False
        self.show_title = False
        
        # 设置深色背景
        self.background_color = (20, 20, 30)
        
        # 游戏标题
        self.game_title = "萝卜传奇"
        
        # 加载字体 - 与项目一致使用SimHei
        try:
            self.title_font = pygame.font.SysFont("SimHei", 72, bold=True)
            self.subtitle_font = pygame.font.SysFont("SimHei", 24)
            self.button_font = pygame.font.SysFont("SimHei", 20)
            self.version_font = pygame.font.SysFont("SimHei", 14)
        except:
            # 备用字体
            self.title_font = pygame.font.Font(None, 72)
            self.subtitle_font = pygame.font.Font(None, 24)
            self.button_font = pygame.font.Font(None, 20)
            self.version_font = pygame.font.Font(None, 14)
        
        # 菜单状态
        self.menu_active = True
        
        # 按钮悬停状态
        self.hovered_button = None
        
        # 创建菜单按钮
        self.create_menu_buttons()
        
        # 加载背景图片（可选）
        self.background_image = None
        self._load_background_image()
        
        # 动画相关
        self.title_alpha = 255
        self.title_fade_direction = -1
        self.fade_speed = 2
        
        # 游戏回调函数
        self.on_start_local_game = None
        self.on_start_online_game = None
        self.on_open_settings = None
        self.on_exit_game = None
    
    def _load_background_image(self):
        """加载背景图片"""
        try:
            # 使用资源管理器加载背景图片
            bg_files = [
                "images/background.png",
                "images/menu_bg.png",
                "background.png"
            ]
            
            for bg_file in bg_files:
                bg_path = get_game_asset_path(bg_file)
                if os.path.exists(bg_path):
                    print(f"✅ 找到背景图片: {bg_path}")
                    self.background_image = pygame.image.load(bg_path)
                    self.background_image = pygame.transform.scale(
                        self.background_image, 
                        (self.rect.width, self.rect.height)
                    )
                    print(f"✅ 背景图片加载成功，尺寸: {self.background_image.get_size()}")
                    return
            
            print("[警告] 未找到背景图片文件，将使用渐变背景")
            
        except Exception as e:
            print(f"[错误] 背景图片加载失败: {e}")
            self.background_image = None
    
    def create_menu_buttons(self):
        """创建菜单按钮"""
        # 按钮配置
        button_width = 280
        button_height = 60
        button_spacing = 20
        
        # 计算按钮位置（居中对齐）
        total_height = (button_height * 4) + (button_spacing * 3)
        start_y = (self.rect.height - total_height) // 2 + 100
        center_x = self.rect.width // 2
        
        # 按钮数据
        buttons_data = [
            ("本地游戏", self.start_local_game, (70, 130, 180)),
            ("联网游戏", self.start_online_game, (100, 160, 100)),
            ("游戏设置", self.open_settings, (160, 100, 160)),
            ("退出游戏", self.exit_game, (180, 70, 70))
        ]
        
        # 创建按钮
        for i, (text, callback, color) in enumerate(buttons_data):
            button_y = start_y + i * (button_height + button_spacing)
            button_x = center_x - button_width // 2
            
            self.add_button(
                text=text,
                rect=(button_x, button_y, button_width, button_height),
                callback=callback,
                bg_color=color,
                text_color=(255, 255, 255),
                border_color=(120, 120, 120)
            )
    
    def start_local_game(self):
        """开始本地游戏"""
        print("🎮 开始本地游戏...")
        self.menu_active = False
        if self.on_start_local_game:
            self.on_start_local_game()
    
    def start_online_game(self):
        """开始联网游戏"""
        print("🌐 开始联网游戏...")
        # 暂时显示提示
        self._show_message("联网功能正在开发中...")
        if self.on_start_online_game:
            self.on_start_online_game()
    
    def open_settings(self):
        """打开设置"""
        print("⚙️ 打开游戏设置...")
        self._show_message("设置面板正在开发中...")
        if self.on_open_settings:
            self.on_open_settings()
    
    def exit_game(self):
        """退出游戏"""
        print("👋 退出游戏...")
        if self.on_exit_game:
            self.on_exit_game()
        else:
            pygame.quit()
            sys.exit()
    
    def _show_message(self, message):
        """显示临时消息（简单实现）"""
        print(f"📢 {message}")
    
    def handle_event(self, event):
        """处理事件 - 增强版，支持悬停效果"""
        if not self.menu_active:
            return False
        
        # 处理鼠标移动（悬停效果）
        if event.type == pygame.MOUSEMOTION:
            self.hovered_button = None
            for button in self.buttons:
                if button["rect"].collidepoint(event.pos):
                    self.hovered_button = button["text"]
                    break
        
        # 处理键盘事件
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.exit_game()
                return True
            elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
                # 回车或空格键开始游戏
                self.start_local_game()
                return True
        
        # 调用父类事件处理
        return super().handle_event(event)
    
    def update(self):
        """更新菜单状态"""
        if not self.menu_active:
            return
        
        # 标题闪烁动画
        self.title_alpha += self.title_fade_direction * self.fade_speed
        if self.title_alpha <= 200:
            self.title_fade_direction = 1
        elif self.title_alpha >= 255:
            self.title_fade_direction = -1
        
        # 确保alpha值在有效范围内
        self.title_alpha = max(200, min(255, self.title_alpha))
    
    def render(self):
        """渲染开始菜单"""
        if not self.menu_active:
            return
        
        # 绘制背景
        self._render_background()
        
        # 绘制游戏标题
        self._render_title()
        
        # 绘制副标题
        self._render_subtitle()
        
        # 绘制菜单按钮
        self._render_menu_buttons()
        
        # 绘制版本信息
        self._render_version_info()
        
        # 绘制装饰元素
        self._render_decorations()
    
    def _render_background(self):
        """渲染背景"""
        if self.background_image:
            # 使用背景图片
            self.screen.blit(self.background_image, (0, 0))
            
            # 添加轻微的半透明叠层，增强文字可读性
            overlay = pygame.Surface((self.rect.width, self.rect.height), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 30))  # 轻微的黑色半透明叠层
            self.screen.blit(overlay, (0, 0))
        else:
            # 使用渐变背景
            self._render_gradient_background()
    
    def _render_gradient_background(self):
        """渲染渐变背景"""
        # 创建渐变效果
        for y in range(self.rect.height):
            # 从深蓝到黑色的渐变
            progress = y / self.rect.height
            color_r = int(20 * (1 - progress))
            color_g = int(20 * (1 - progress))
            color_b = int(50 * (1 - progress) + 20 * progress)
            
            color = (color_r, color_g, color_b)
            pygame.draw.line(self.screen, color, (0, y), (self.rect.width, y))
    
    def _render_title(self):
        """渲染游戏标题"""
        # 创建标题文本
        title_surface = self.title_font.render(self.game_title, True, (255, 255, 255))
        title_surface.set_alpha(self.title_alpha)
        
        # 计算位置（上方居中）
        title_rect = title_surface.get_rect(
            center=(self.rect.width // 2, self.rect.height // 4)
        )
        
        # 绘制标题阴影（描边效果）
        shadow_offsets = [(-3, -3), (3, -3), (-3, 3), (3, 3)]
        shadow_surface = self.title_font.render(self.game_title, True, (0, 0, 0))
        shadow_surface.set_alpha(self.title_alpha // 2)
        
        for dx, dy in shadow_offsets:
            shadow_rect = pygame.Rect(title_rect.x + dx, title_rect.y + dy, title_rect.width, title_rect.height)
            self.screen.blit(shadow_surface, shadow_rect)
        
        # 绘制标题
        self.screen.blit(title_surface, title_rect)
    
    def _render_subtitle(self):
        """渲染副标题"""
        subtitle_text = "一个充满冒险的RPG世界"
        subtitle_surface = self.subtitle_font.render(subtitle_text, True, (200, 200, 200))
        
        # 计算位置（标题下方）
        subtitle_rect = subtitle_surface.get_rect(
            center=(self.rect.width // 2, self.rect.height // 4 + 80)
        )
        
        self.screen.blit(subtitle_surface, subtitle_rect)
    
    def _render_menu_buttons(self):
        """渲染菜单按钮 - 增强版，支持悬停效果"""
        for button in self.buttons:
            # 判断是否悬停
            is_hovered = (self.hovered_button == button["text"])
            
            # 选择颜色
            if is_hovered:
                # 悬停时稍微亮一些
                bg_color = tuple(min(255, c + 30) for c in button["bg_color"])
                border_color = (255, 255, 255)
                border_width = 3
            else:
                bg_color = button["bg_color"]
                border_color = button["border_color"]
                border_width = 2
            
            # 绘制按钮背景（圆角矩形）
            self._draw_rounded_rect(button["rect"], bg_color, 10)
            
            # 绘制按钮边框
            self._draw_rounded_rect_border(button["rect"], border_color, border_width, 10)
            
            # 绘制按钮文本
            text_surface = self.button_font.render(button["text"], True, button["text_color"])
            text_rect = text_surface.get_rect(center=button["rect"].center)
            self.screen.blit(text_surface, text_rect)
    
    def _draw_rounded_rect(self, rect, color, radius):
        """绘制圆角矩形"""
        # 简化版圆角矩形实现
        pygame.draw.rect(self.screen, color, rect)
        
        # 绘制圆角（简单实现）
        corner_size = min(radius, rect.width // 2, rect.height // 2)
        
        # 四个角的圆形
        corners = [
            (rect.left + corner_size, rect.top + corner_size),  # 左上
            (rect.right - corner_size, rect.top + corner_size),  # 右上
            (rect.left + corner_size, rect.bottom - corner_size),  # 左下
            (rect.right - corner_size, rect.bottom - corner_size)  # 右下
        ]
        
        for corner in corners:
            pygame.draw.circle(self.screen, color, corner, corner_size)
    
    def _draw_rounded_rect_border(self, rect, color, width, radius):
        """绘制圆角矩形边框"""
        # 简化版实现
        pygame.draw.rect(self.screen, color, rect, width)
    
    def _render_version_info(self):
        """渲染版本信息"""
        version_text = "v1.4.3 - Alpha Build"
        version_surface = self.version_font.render(version_text, True, (120, 120, 120))
        
        # 位置：右下角
        version_rect = version_surface.get_rect(
            bottomright=(self.rect.width - 20, self.rect.height - 20)
        )
        
        self.screen.blit(version_surface, version_rect)
    
    def _render_decorations(self):
        """渲染装饰元素"""
        # 星星特效已移除
        pass
    
    def set_callbacks(self, on_start_local=None, on_start_online=None, 
                     on_settings=None, on_exit=None):
        """设置回调函数"""
        self.on_start_local_game = on_start_local
        self.on_start_online_game = on_start_online
        self.on_open_settings = on_settings
        self.on_exit_game = on_exit
    
    def show_menu(self):
        """显示菜单"""
        self.menu_active = True
    
    def hide_menu(self):
        """隐藏菜单"""
        self.menu_active = False 