import pygame
import time
from typing import List, Dict, Any
from game.ui.ui_panel import UIPanel

# 导入新的日志系统
from game.core.log_manager import get_log_manager
from game.managers.user_message_manager import UserMessageType, UserMessagePriority


class DropInfoManager:
    """掉落信息管理器"""

    def __init__(self, max_records=50):
        self.max_records = max_records
        self.drop_records = []
        self.important_drops = []  # 重要掉落（稀有物品等）

    def add_drop(self, item_name: str, item_type: str = "普通", rarity: str = "普通",
                 source: str = "怪物", location: str = "未知地图"):
        """添加掉落记录"""
        drop_record = {
            "item_name": item_name,
            "item_type": item_type,
            "rarity": rarity,
            "source": source,
            "location": location,
            "timestamp": time.time(),
            "time_str": time.strftime("%H:%M:%S")
        }

        self.drop_records.append(drop_record)

        # 如果是重要物品，也添加到重要掉落列表
        if rarity in ["稀有", "史诗", "传说"]:
            self.important_drops.append(drop_record)

        # 维护记录数量限制
        if len(self.drop_records) > self.max_records:
            self.drop_records.pop(0)

        if len(self.important_drops) > 20:  # 重要掉落保留20条
            self.important_drops.pop(0)

    def get_recent_drops(self, count=10):
        """获取最近的掉落记录"""
        return self.drop_records[-count:] if self.drop_records else []

    def get_important_drops(self):
        """获取重要掉落记录"""
        return self.important_drops.copy()

    def get_drop_summary(self):
        """获取掉落统计摘要"""
        if not self.drop_records:
            return "暂无掉落记录"

        total_drops = len(self.drop_records)
        important_count = len(self.important_drops)

        return f"总掉落: {total_drops} | 重要物品: {important_count}"


class ImportantInfoManager:
    """重要信息管理器"""

    def __init__(self, max_records=30):
        self.max_records = max_records
        self.important_messages = []

    def add_important_message(self, message: str, msg_type: str = "系统",
                            priority: str = "普通", color: tuple = (255, 255, 255)):
        """添加重要信息"""
        important_msg = {
            "message": message,
            "type": msg_type,
            "priority": priority,
            "color": color,
            "timestamp": time.time(),
            "time_str": time.strftime("%H:%M:%S")
        }

        self.important_messages.append(important_msg)

        # 维护记录数量限制
        if len(self.important_messages) > self.max_records:
            self.important_messages.pop(0)

    def get_recent_messages(self, count=10):
        """获取最近的重要信息"""
        return self.important_messages[-count:] if self.important_messages else []

    def add_level_up(self, old_level: int, new_level: int):
        """添加升级信息"""
        self.add_important_message(
            f"🎉 恭喜升级！{old_level} → {new_level}",
            "升级", "重要", (0, 255, 0)
        )

    def add_achievement(self, achievement_name: str):
        """添加成就信息"""
        self.add_important_message(
            f"🏆 获得成就：{achievement_name}",
            "成就", "重要", (255, 215, 0)
        )

    def add_death_info(self, killer: str, location: str):
        """添加死亡信息"""
        self.add_important_message(
            f"💀 被 {killer} 击败于 {location}",
            "死亡", "警告", (255, 100, 100)
        )

class AnnouncementPanel(UIPanel):
    """
    分页公告面板 - 支持多标签页的公告区域
    包含：系统公告、玩家掉落、重要信息等标签页
    支持分页导航和消息去重
    """
    def __init__(self, screen, battle_manager, position, size, player=None, user_message_manager=None):
        """
        初始化公告面板

        Args:
            screen: 屏幕对象
            battle_manager: 战斗管理器
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
            player: 玩家对象（可选）
            user_message_manager: 用户消息管理器（可选）
        """
        super().__init__(screen, position, size)

        # 管理器和玩家引用
        self.battle_manager = battle_manager
        self.player = player
        self.user_message_manager = user_message_manager
        self.log_manager = get_log_manager()

        # 面板基本属性
        self.title = "游戏信息"
        self.background_color = (25, 25, 25)
        self.title_font = pygame.font.SysFont("SimHei", 16, bold=True)
        self.normal_font = pygame.font.SysFont("SimHei", 12)
        self.small_font = pygame.font.SysFont("SimHei", 10)

        # 分页系统
        self.tabs = ["系统公告", "玩家掉落", "重要信息"]
        self.current_tab = 0
        self.tab_height = 30

        # 信息管理器
        self.drop_manager = DropInfoManager()
        self.important_manager = ImportantInfoManager()

        # 消息去重
        self.seen_messages = set()  # 用于去重的消息哈希集合

        # 系统公告消息（初始为空，等待真实消息）
        self.system_announcements = []

        # UI 布局计算
        self.tab_rect = pygame.Rect(self.rect.left, self.rect.top + 25, self.size[0], self.tab_height)

        # 分页导航设置（放在面板最底端）
        self.page_size = 12  # 增加每页显示的消息数量
        self.current_page = 0
        self.nav_height = 25
        self.nav_rect = pygame.Rect(
            self.rect.left + 5,
            self.rect.bottom - self.nav_height - 5,  # 放在面板最底端
            self.size[0] - 10,
            self.nav_height
        )

        # 内容区域（在标签页和导航栏之间）
        content_top = self.tab_rect.bottom + 5
        content_bottom = self.nav_rect.top - 5
        self.content_rect = pygame.Rect(
            self.rect.left + 5,
            content_top,
            self.size[0] - 10,
            content_bottom - content_top
        )

        # 消息显示属性
        self.message_line_height = 18  # 稍微增加行高，提高可读性
        self.visible_messages = self.content_rect.height // self.message_line_height

        # 调试信息：输出布局计算结果
        if hasattr(self, 'debug_console_output') and self.debug_console_output:
            print(f"📐 公告面板布局:")
            print(f"   - 面板总大小: {self.size}")
            print(f"   - 标签页区域: {self.tab_rect}")
            print(f"   - 内容区域: {self.content_rect}")
            print(f"   - 导航区域: {self.nav_rect}")
            print(f"   - 可见消息数: {self.visible_messages}")
            print(f"   - 每页消息数: {self.page_size}")

        # 玩家名称
        self.player_name = getattr(self.player, 'name', '玩家') if self.player else '玩家'

        # 添加初始欢迎信息（仅一次性信息，不是示例数据）
        self._add_welcome_message()

    def _add_welcome_message(self):
        """添加欢迎信息"""
        # 只添加一条欢迎信息，其他信息等待游戏过程中产生
        self.important_manager.add_important_message(
            "🎮 公告面板已启动，开始记录您的冒险历程！", "系统", "重要", (0, 255, 255)
        )

    def get_current_tab_messages(self):
        """获取当前标签页的消息"""
        if self.current_tab == 0:  # 系统公告
            messages = self.system_announcements.copy()

            # 从UserMessageManager获取系统消息
            if self.user_message_manager:
                system_messages = self.user_message_manager.get_messages_by_type(UserMessageType.SYSTEM)
                for msg in system_messages[-10:]:  # 只取最近10条
                    message_hash = hash(msg.message)
                    if message_hash not in self.seen_messages:
                        messages.append({
                            "message": msg.message,
                            "color": msg.color,
                            "time": msg.timestamp
                        })
                        self.seen_messages.add(message_hash)

            return messages

        elif self.current_tab == 1:  # 玩家掉落
            drops = self.drop_manager.get_recent_drops(20)
            return [{
                "message": f"🎁 {drop['item_name']} ({drop['rarity']}) - {drop['source']} @ {drop['location']}",
                "color": self._get_rarity_color(drop['rarity']),
                "time": drop['timestamp']
            } for drop in drops]

        elif self.current_tab == 2:  # 重要信息
            important_msgs = self.important_manager.get_recent_messages(15)
            return [{
                "message": msg['message'],
                "color": msg['color'],
                "time": msg['timestamp']
            } for msg in important_msgs]

        return []

    def _get_rarity_color(self, rarity):
        """根据稀有度获取颜色"""
        rarity_colors = {
            "普通": (200, 200, 200),
            "稀有": (0, 150, 255),
            "史诗": (150, 0, 255),
            "传说": (255, 165, 0)
        }
        return rarity_colors.get(rarity, (255, 255, 255))

    def add_drop_info(self, item_name, item_type="普通", rarity="普通", source="怪物", location="未知地图"):
        """添加掉落信息（带去重）"""
        # 创建消息哈希用于去重
        drop_key = f"drop_{item_name}_{source}_{location}"
        current_time = time.time()

        # 检查是否是重复消息（5秒内相同掉落）
        if hasattr(self, '_last_drop_times'):
            if drop_key in self._last_drop_times:
                if current_time - self._last_drop_times[drop_key] < 5.0:
                    return  # 跳过重复掉落
        else:
            self._last_drop_times = {}

        # 记录时间并添加掉落
        self._last_drop_times[drop_key] = current_time
        self.drop_manager.add_drop(item_name, item_type, rarity, source, location)

        # 清理旧的时间记录
        if len(self._last_drop_times) > 50:
            cutoff_time = current_time - 60.0  # 清理1分钟前的记录
            self._last_drop_times = {
                k: v for k, v in self._last_drop_times.items()
                if v > cutoff_time
            }

    def add_important_info(self, message, msg_type="系统", priority="普通", color=(255, 255, 255)):
        """添加重要信息（带去重）"""
        # 创建消息哈希用于去重
        message_hash = hash(f"{message}_{msg_type}")
        current_time = time.time()

        # 检查是否是重复消息（10秒内相同重要信息）
        if hasattr(self, '_last_important_times'):
            if message_hash in self._last_important_times:
                if current_time - self._last_important_times[message_hash] < 10.0:
                    return  # 跳过重复消息
        else:
            self._last_important_times = {}

        # 记录时间并添加消息
        self._last_important_times[message_hash] = current_time
        self.important_manager.add_important_message(message, msg_type, priority, color)

        # 清理旧的时间记录
        if len(self._last_important_times) > 30:
            cutoff_time = current_time - 300.0  # 清理5分钟前的记录
            self._last_important_times = {
                k: v for k, v in self._last_important_times.items()
                if v > cutoff_time
            }

    def add_system_announcement(self, message, color=(255, 255, 255)):
        """添加系统公告"""
        announcement = {
            "message": message,
            "color": color,
            "time": time.time()
        }
        self.system_announcements.append(announcement)

        # 限制系统公告数量
        if len(self.system_announcements) > 20:
            self.system_announcements.pop(0)

    def handle_event(self, event):
        """处理事件"""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # 左键
                # 检查标签页点击
                if self.tab_rect.collidepoint(event.pos):
                    self._handle_tab_click(event.pos)
                    return True

                # 检查分页导航点击
                if self.nav_rect.collidepoint(event.pos):
                    self._handle_nav_click(event.pos)
                    return True

            elif event.button == 4:  # 鼠标滚轮向上
                if self.content_rect.collidepoint(event.pos):
                    self.current_page = max(0, self.current_page - 1)
                    return True

            elif event.button == 5:  # 鼠标滚轮向下
                if self.content_rect.collidepoint(event.pos):
                    messages = self.get_current_tab_messages()
                    max_page = max(0, (len(messages) - 1) // self.page_size)
                    self.current_page = min(max_page, self.current_page + 1)
                    return True

        return False

    def _handle_tab_click(self, pos):
        """处理标签页点击"""
        tab_width = self.tab_rect.width // len(self.tabs)
        clicked_tab = (pos[0] - self.tab_rect.left) // tab_width

        if 0 <= clicked_tab < len(self.tabs):
            self.current_tab = clicked_tab
            self.current_page = 0  # 切换标签页时重置页码

    def _handle_nav_click(self, pos):
        """处理导航按钮点击"""
        button_width = 60
        prev_button_rect = pygame.Rect(self.nav_rect.left + 10, self.nav_rect.top + 2, button_width, 20)
        next_button_rect = pygame.Rect(self.nav_rect.right - button_width - 10, self.nav_rect.top + 2, button_width, 20)

        messages = self.get_current_tab_messages()
        max_page = max(0, (len(messages) - 1) // self.page_size)

        if prev_button_rect.collidepoint(pos):
            self.current_page = max(0, self.current_page - 1)
        elif next_button_rect.collidepoint(pos):
            self.current_page = min(max_page, self.current_page + 1)

    def update(self):
        """更新面板状态"""
        pass  # 简化更新逻辑
    
    def render(self):
        """渲染分页公告面板"""
        if not self.visible:
            return

        # 渲染背景
        pygame.draw.rect(self.screen, self.background_color, self.rect)
        pygame.draw.rect(self.screen, (100, 100, 100), self.rect, 2)

        # 渲染标题
        title_surface = self.title_font.render(self.title, True, (255, 255, 255))
        title_x = self.rect.left + (self.rect.width - title_surface.get_width()) // 2
        self.screen.blit(title_surface, (title_x, self.rect.top + 5))

        # 渲染标签页
        self.render_tabs()

        # 渲染内容区域
        self.render_content()

        # 渲染分页导航
        self.render_navigation()

    def render_tabs(self):
        """渲染标签页"""
        tab_width = self.tab_rect.width // len(self.tabs)

        for i, tab_name in enumerate(self.tabs):
            tab_x = self.tab_rect.left + i * tab_width
            tab_rect = pygame.Rect(tab_x, self.tab_rect.top, tab_width, self.tab_height)

            # 标签页背景
            if i == self.current_tab:
                pygame.draw.rect(self.screen, (60, 60, 60), tab_rect)
                pygame.draw.rect(self.screen, (150, 150, 150), tab_rect, 2)
            else:
                pygame.draw.rect(self.screen, (40, 40, 40), tab_rect)
                pygame.draw.rect(self.screen, (80, 80, 80), tab_rect, 1)

            # 标签页文字
            text_color = (255, 255, 255) if i == self.current_tab else (180, 180, 180)
            text_surface = self.normal_font.render(tab_name, True, text_color)
            text_x = tab_x + (tab_width - text_surface.get_width()) // 2
            text_y = self.tab_rect.top + (self.tab_height - text_surface.get_height()) // 2
            self.screen.blit(text_surface, (text_x, text_y))

    def render_content(self):
        """渲染内容区域"""
        # 内容区域背景
        pygame.draw.rect(self.screen, (15, 15, 15), self.content_rect)
        pygame.draw.rect(self.screen, (60, 60, 60), self.content_rect, 1)

        # 获取当前标签页的消息
        messages = self.get_current_tab_messages()

        if not messages:
            # 显示空状态
            empty_text = "暂无信息"
            text_surface = self.normal_font.render(empty_text, True, (128, 128, 128))
            text_x = self.content_rect.left + (self.content_rect.width - text_surface.get_width()) // 2
            text_y = self.content_rect.top + (self.content_rect.height - text_surface.get_height()) // 2
            self.screen.blit(text_surface, (text_x, text_y))
            return

        # 计算分页
        start_index = self.current_page * self.page_size
        end_index = min(start_index + self.page_size, len(messages))

        # 渲染消息 - 充分利用可用空间
        available_lines = (self.content_rect.height - 10) // self.message_line_height  # 减去上下边距
        display_count = min(len(messages[start_index:end_index]), available_lines)

        for i in range(display_count):
            message_data = messages[start_index + i]
            y_pos = self.content_rect.top + 5 + i * self.message_line_height

            # 渲染消息文本
            message_text = message_data.get("message", "")
            message_color = message_data.get("color", (255, 255, 255))

            # 处理长文本截断
            max_width = self.content_rect.width - 20
            if self.normal_font.size(message_text)[0] > max_width:
                # 截断文本
                while self.normal_font.size(message_text + "...")[0] > max_width and len(message_text) > 0:
                    message_text = message_text[:-1]
                message_text += "..."

            text_surface = self.normal_font.render(message_text, True, message_color)
            self.screen.blit(text_surface, (self.content_rect.left + 10, y_pos))

        # 在调试模式下显示布局信息
        if hasattr(self, 'debug_mode') and getattr(self, 'debug_mode', False):
            debug_text = f"显示: {display_count}/{len(messages)} | 页: {self.current_page + 1}"
            debug_surface = self.small_font.render(debug_text, True, (100, 100, 100))
            self.screen.blit(debug_surface, (self.content_rect.right - 150, self.content_rect.top + 2))

    def render_navigation(self):
        """渲染分页导航"""
        # 导航区域背景
        pygame.draw.rect(self.screen, (30, 30, 30), self.nav_rect)
        pygame.draw.rect(self.screen, (80, 80, 80), self.nav_rect, 1)

        messages = self.get_current_tab_messages()
        if not messages:
            return

        total_pages = max(1, (len(messages) + self.page_size - 1) // self.page_size)
        current_page_display = self.current_page + 1

        # 渲染页码信息
        page_info = f"{current_page_display} / {total_pages}"
        page_surface = self.small_font.render(page_info, True, (200, 200, 200))
        page_x = self.nav_rect.left + (self.nav_rect.width - page_surface.get_width()) // 2
        page_y = self.nav_rect.top + (self.nav_rect.height - page_surface.get_height()) // 2
        self.screen.blit(page_surface, (page_x, page_y))

        # 渲染导航按钮
        button_width = 50
        button_height = 18

        # 上一页按钮
        prev_rect = pygame.Rect(self.nav_rect.left + 10, self.nav_rect.top + 3, button_width, button_height)
        prev_color = (100, 100, 100) if self.current_page > 0 else (50, 50, 50)
        pygame.draw.rect(self.screen, prev_color, prev_rect)
        pygame.draw.rect(self.screen, (150, 150, 150), prev_rect, 1)

        prev_text = self.small_font.render("上一页", True, (255, 255, 255))
        prev_text_x = prev_rect.left + (prev_rect.width - prev_text.get_width()) // 2
        prev_text_y = prev_rect.top + (prev_rect.height - prev_text.get_height()) // 2
        self.screen.blit(prev_text, (prev_text_x, prev_text_y))

        # 下一页按钮
        next_rect = pygame.Rect(self.nav_rect.right - button_width - 10, self.nav_rect.top + 3, button_width, button_height)
        next_color = (100, 100, 100) if self.current_page < total_pages - 1 else (50, 50, 50)
        pygame.draw.rect(self.screen, next_color, next_rect)
        pygame.draw.rect(self.screen, (150, 150, 150), next_rect, 1)

        next_text = self.small_font.render("下一页", True, (255, 255, 255))
        next_text_x = next_rect.left + (next_rect.width - next_text.get_width()) // 2
        next_text_y = next_rect.top + (next_rect.height - next_text.get_height()) // 2
        self.screen.blit(next_text, (next_text_x, next_text_y))
