import pygame
import os
from game.ui.ui_panel import UIPanel
from game.core.resource_manager import get_game_asset_path

class InfoPanel(UIPanel):
    """
    信息面板，显示玩家信息和菜单
    """
    def __init__(self, screen, player, position, size):
        super().__init__(screen, position, size)
        
        # 玩家引用
        self.player = player
        
        # 设置面板标题
        self.title = "信息区"
        
        # 设置面板背景色
        self.background_color = (40, 40, 40)
        
        # 角色头像区域
        self.avatar_rect = pygame.Rect(
            self.rect.left + 10,
            self.rect.top + 30,
            60,
            60
        )
        
        # 主菜单标签页按钮
        self.main_tab_buttons = []
        self.create_main_tab_buttons()
        
        # 副菜单标签页按钮
        self.sub_tab_buttons = []
        self.create_sub_tab_buttons()
        
        # 当前选中的标签页
        self.active_main_tab = None
        self.active_sub_tab = None
    
        # 技能面板打开回调函数
        self.on_skill_tab_click = None
        
        # 装备面板打开回调函数
        self.on_equipment_tab_click = None
        
        # 背包面板打开回调函数
        self.on_inventory_tab_click = None
        
        # 仓库面板打开回调函数
        self.on_warehouse_tab_click = None
        
        # 加载玩家头像图片
        self.player_image = None
        self._load_player_avatar()
        
        # 药水面板打开回调函数
        self.on_medicine_tab_click = None
        
        # 签到面板打开回调函数
        self.on_checkin_tab_click = None
        
        # 金币商城面板打开回调函数
        self.on_gold_shop_tab_click = None
    
    def create_main_tab_buttons(self):
        """
        创建主菜单标签页按钮
        """
        main_tabs = ["装备", "背包", "仓库", "技能", "药水", "签到", "邮件"]
        tab_width = 40
        tab_height = 20
        start_x = 10
        start_y = 190
        
        for i, tab in enumerate(main_tabs):
            self.add_button(
                tab,
                (start_x + i * (tab_width + 2), start_y, tab_width, tab_height),
                lambda t=tab: self.set_active_main_tab(t)
            )
    
    def create_sub_tab_buttons(self):
        """
        创建副菜单标签页按钮
        """
        sub_tabs = ["查强", "活动", "积分", "金币", "推广", "行会"]
        tab_width = 40
        tab_height = 20
        start_x = 10
        start_y = 220
        
        for i, tab in enumerate(sub_tabs):
            self.add_button(
                tab,
                (start_x + i * (tab_width + 2), start_y, tab_width, tab_height),
                lambda t=tab: self.set_active_sub_tab(t)
            )
    
    def set_on_skill_tab_click(self, callback):
        """
        设置技能标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_skill_tab_click = callback
    
    def set_on_equipment_tab_click(self, callback):
        """
        设置装备标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_equipment_tab_click = callback
    
    def set_on_inventory_tab_click(self, callback):
        """
        设置背包标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_inventory_tab_click = callback
    
    def set_on_warehouse_tab_click(self, callback):
        """
        设置仓库标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_warehouse_tab_click = callback
    
    def set_on_medicine_tab_click(self, callback):
        """
        设置药水标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_medicine_tab_click = callback
    
    def set_on_checkin_tab_click(self, callback):
        """
        设置签到标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_checkin_tab_click = callback
    
    def set_on_shop_tab_click(self, callback):
        """
        设置积分商城标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_shop_tab_click = callback
        
    def set_on_gold_shop_tab_click(self, callback):
        """
        设置金币商城标签页点击的回调函数
        
        Args:
            callback: 回调函数
        """
        self.on_gold_shop_tab_click = callback
    
    def set_active_main_tab(self, tab):
        """
        设置当前活动的主菜单标签页
        
        Args:
            tab: 标签页名称
        """
        self.active_main_tab = tab
        print(f"选择了主菜单: {tab}")
        
        # 如果点击的是技能按钮，并且设置了回调函数，则调用回调函数
        if tab == "技能" and self.on_skill_tab_click is not None:
            self.on_skill_tab_click()
        
        # 如果点击的是装备按钮，并且设置了回调函数，则调用回调函数
        elif tab == "装备" and self.on_equipment_tab_click is not None:
            self.on_equipment_tab_click()
        
        # 如果点击的是背包按钮，并且设置了回调函数，则调用回调函数
        elif tab == "背包" and self.on_inventory_tab_click is not None:
            self.on_inventory_tab_click()
        
        # 如果点击的是仓库按钮，并且设置了回调函数，则调用回调函数
        elif tab == "仓库" and self.on_warehouse_tab_click is not None:
            self.on_warehouse_tab_click()
        
        # 如果点击的是药水按钮，并且设置了回调函数，则调用回调函数
        elif tab == "药水" and self.on_medicine_tab_click is not None:
            self.on_medicine_tab_click()
        
        # 如果点击的是签到按钮，并且设置了回调函数，则调用回调函数
        elif tab == "签到" and self.on_checkin_tab_click is not None:
            self.on_checkin_tab_click()
        
        # TODO: 根据选择的标签页显示不同的内容
    
    def set_active_sub_tab(self, tab):
        """
        设置当前活动的副菜单标签页
        """
        self.active_sub_tab = tab
        print(f"选择了副菜单: {tab}")
        
        # 如果点击的是积分按钮，并且设置了回调函数，则调用回调函数
        if tab == "积分" and hasattr(self, 'on_shop_tab_click') and self.on_shop_tab_click is not None:
            self.on_shop_tab_click()
        
        # 如果点击的是金币按钮，并且设置了回调函数，则调用回调函数
        elif tab == "金币" and hasattr(self, 'on_gold_shop_tab_click') and self.on_gold_shop_tab_click is not None:
            self.on_gold_shop_tab_click()
        
        # TODO: 根据选择的标签页显示不同的内容
    
    def render(self):
        """
        渲染信息面板
        """
        super().render()
        
        # 🔧 修复：检查玩家对象是否存在
        if not self.player:
            # 如果没有玩家对象，显示默认信息
            no_player_text = "请创建角色"
            no_player_surface = self.normal_font.render(no_player_text, True, (255, 255, 255))
            self.screen.blit(no_player_surface, (self.rect.left + 10, self.rect.top + 30))
            
            # 绘制默认头像框
            pygame.draw.rect(self.screen, (60, 60, 60), self.avatar_rect)
            pygame.draw.rect(self.screen, (100, 100, 100), self.avatar_rect, 1)
            return
        
        # 渲染角色头像
        if self.player_image:
            # 如果有头像图片，则显示图片
            self.screen.blit(self.player_image, self.avatar_rect)
        else:
            # 如果没有头像图片，则显示默认的灰色矩形
            pygame.draw.rect(self.screen, (60, 60, 60), self.avatar_rect)
        # 绘制头像边框
        pygame.draw.rect(self.screen, (100, 100, 100), self.avatar_rect, 1)
        
        # 向右偏移的基础值，使所有文本更靠右
        right_offset = 20
        
        # 渲染角色等级、职业和姓名
        level_text = f"Lv.{self.player.level} {self.player.character_class}"
        level_surface = self.normal_font.render(level_text, True, self.normal_color)
        self.screen.blit(level_surface, (self.avatar_rect.right + right_offset, self.avatar_rect.top))
        
        # 渲染玩家姓名
        name_text = self.player.name
        name_surface = self.normal_font.render(name_text, True, (255, 255, 255))
        self.screen.blit(name_surface, (self.avatar_rect.right + right_offset, self.avatar_rect.top + 20))
        
        # 渲染VIP状态
        vip_text = f"VIP{self.player.vip_level}"
        vip_surface = self.normal_font.render(vip_text, True, (255, 215, 0))
        self.screen.blit(vip_surface, (self.avatar_rect.right + right_offset, self.avatar_rect.top + 40))
        
        # 渲染生命值 - 向右移动，优化数字显示
        # 使用current_hp显示实际血量，如果没有则使用hp
        current_hp = getattr(self.player, 'current_hp', self.player.hp)
        hp_current = f"{current_hp:,}"
        hp_max = f"{self.player.max_hp:,}"
        hp_text = f"生命: {hp_current}/{hp_max}"
        hp_surface = self.normal_font.render(hp_text, True, (255, 120, 120))
        self.screen.blit(hp_surface, (self.avatar_rect.right + right_offset, self.avatar_rect.top + 60))
        
        # 渲染法力值 - 向右移动，优化数字显示
        mp_current = f"{self.player.mp:,}"
        mp_max = f"{self.player.max_mp:,}"
        mp_text = f"法力: {mp_current}/{mp_max}"
        mp_surface = self.normal_font.render(mp_text, True, (120, 120, 255))
        self.screen.blit(mp_surface, (self.avatar_rect.right + right_offset, self.avatar_rect.top + 80))
        
        # 渲染VIP经验
        vip_exp_text = f"VIP经验: {self.player.vip_exp}"
        vip_exp_surface = self.small_font.render(vip_exp_text, True, (200, 180, 50))
        self.screen.blit(vip_exp_surface, (self.rect.left + 10, self.avatar_rect.bottom + 5))
        
        # 渲染货币
        currencies_y = self.avatar_rect.bottom + 35
        
        # 元宝
        yuanbao_text = f"元宝: {self.player.currencies['yuanbao']}"
        yuanbao_surface = self.normal_font.render(yuanbao_text, True, (255, 215, 0))
        self.screen.blit(yuanbao_surface, (self.rect.left + 10, currencies_y))
        
        # 金币
        gold_text = f"金币: {self.player.currencies['gold']}"
        gold_surface = self.normal_font.render(gold_text, True, (212, 175, 55))
        self.screen.blit(gold_surface, (self.rect.left + 10, currencies_y + 20))
        
        # 银币
        coin_text = f"银币: {self.player.currencies['coin']}"
        coin_surface = self.normal_font.render(coin_text, True, (192, 192, 192))
        self.screen.blit(coin_surface, (self.rect.left + 10, currencies_y + 40))
        
        # 高亮当前选中的标签页按钮
        for button in self.buttons:
            if (self.active_main_tab and button["text"] == self.active_main_tab) or \
               (self.active_sub_tab and button["text"] == self.active_sub_tab):
                button["bg_color"] = (100, 100, 150)
            else:
                button["bg_color"] = (70, 70, 70)
    
    def _load_player_avatar(self):
        """
        根据玩家职业和性别加载头像图片
        """
        try:
            # 获取玩家职业和性别
            character_class = getattr(self.player, 'character_class', '战士')
            gender = getattr(self.player, 'gender', '男')  # 默认为男性
            
            # 构建图片文件名并使用资源管理器
            image_filename = f"{character_class}_{gender}.png"
            image_path = get_game_asset_path(f"images/characters/{image_filename}")
            
            # 加载图片
            if os.path.exists(image_path):
                self.player_image = pygame.image.load(image_path)
                # 缩放图片到头像区域大小（60x60像素）
                self.player_image = pygame.transform.scale(self.player_image, (60, 60))
            else:
                print(f"角色头像图片不存在: {image_path}")
                self.player_image = None
        except Exception as e:
            print(f"加载角色头像图片失败: {e}")
            self.player_image = None
    
    def update(self):
        """
        更新信息面板
        """
        pass