# 萝卜传奇 - 游戏功能详细说明

## 🎮 装备系统

### 装备面板
- 通过信息面板的"装备"按钮或快捷键打开装备面板
- 装备面板显示角色的所有装备槽位和当前装备
- 支持装备图片显示和属性预览

### ✨ 装备悬停提示功能（v1.11.0新增）

#### 功能概述
鼠标悬停在装备槽位上时，会显示详细的装备信息提示框，无需点击即可查看装备的完整属性。

#### 使用方法
1. 打开装备面板
2. 将鼠标指针悬停在任意有装备的槽位上
3. 等待0.5秒后，装备详情提示框会自动显示
4. 移开鼠标后，提示框自动隐藏

#### 提示框信息内容
**装备基本信息**：
- 装备名称（金色标题）
- 装备品质和类型（颜色区分品质）

**装备属性**：
- 攻击力、防御力、生命值、魔法值等
- 显示具体的数值加成
- 支持范围属性显示（如攻击力2-5）

**装备需求**：
- 等级需求
- 职业需求
- 满足需求显示绿色，不满足显示红色

**装备描述**：
- 装备的详细说明文字

#### 品质颜色系统
- **普通装备**：灰色
- **精良装备**：绿色
- **稀有装备**：蓝色
- **史诗装备**：紫色
- **传说装备**：橙色

#### 智能定位特性
- 提示框会自动避开屏幕边界
- 鼠标在屏幕右侧时，提示框显示在左侧
- 鼠标在屏幕底部时，提示框向上调整
- 确保提示框完全可见

## 🎒 背包系统

### 背包管理
- 通过信息面板的"背包"按钮打开背包面板
- 支持物品拖拽、整理、使用等操作
- 物品图标显示和数量标识

### 物品类型
- **装备类**：武器、防具、饰品等
- **消耗品**：药水、食物、卷轴等
- **任务道具**：特殊任务物品
- **技能书**：学习技能的道具

## ⚔️ 战斗系统

### 手动战斗
- 点击目标怪物进行攻击
- 支持技能释放和战术选择
- 实时显示战斗日志

### 自动战斗
- 开启自动战斗模式
- 智能选择攻击目标
- 自动使用技能和药水

### 寻怪功能
- 自动寻找并攻击附近怪物
- 小地图显示寻路轨迹
- 支持指定怪物类型寻找

## 🗺️ 地图系统

### 地图导航
- 小地图显示当前位置和周围环境
- 怪物位置标记和状态显示
- 寻路路径可视化

### 地图切换
- 通过传送点切换不同地图
- 地图解锁系统（击杀要求）
- 适合等级的地图推荐

## 💊 药水系统

### 手动使用
- 在背包中右键点击药水使用
- 支持生命药水和魔法药水
- 即时恢复效果

### 自动药水
- 设置生命值/魔法值阈值
- 自动购买和使用药水
- 智能药水选择算法

## 🎯 技能系统

### 技能学习
- 通过技能书学习新技能
- 职业专属技能树
- 技能等级提升

### 技能使用
- 快捷键释放技能
- 技能冷却时间管理
- 魔法值消耗计算

## 💰 经济系统

### 货币类型
- **金币**：主要货币，用于购买装备和药水
- **银币**：辅助货币
- **元宝**：高级货币
- **积分**：签到和活动获得

### 交易系统
- NPC商店购买物品
- 装备买卖系统
- 价格波动机制

## 🎁 活动系统

### 每日签到
- 每日登录签到奖励
- 连续签到额外奖励
- 月度签到成就

### 积分商城
- 使用积分购买特殊物品
- 限时商品和折扣活动
- VIP等级优惠

## 📊 排行榜系统

### 排行类型
- 等级排行榜
- 战力排行榜
- 财富排行榜
- 击杀排行榜

### 排名奖励
- 每周排名奖励
- 季度冠军奖励
- 成就徽章系统

## ⚙️ 系统设置

### 游戏设置
- 图像质量调整
- 音效开关控制
- 快捷键自定义

### 存档系统
- 自动存档功能
- 手动存档管理
- 存档备份恢复

## 🔧 快捷键说明

- **F1**: 打开装备面板
- **F2**: 打开背包面板
- **F3**: 打开技能面板
- **F4**: 打开任务面板
- **Space**: 开始/停止自动战斗
- **Ctrl+S**: 手动存档
- **ESC**: 关闭当前面板/退出游戏

## 💡 游戏技巧

1. **装备搭配**：根据职业特点选择合适的装备组合
2. **技能连招**：合理搭配技能释放顺序，提高战斗效率
3. **资源管理**：合理分配金币，优先购买关键装备
4. **地图选择**：选择适合当前等级的地图进行升级
5. **药水配置**：设置合理的自动药水阈值，提高生存能力

## 🐛 常见问题

**Q: 装备悬停提示不显示？**
A: 确保鼠标悬停在有装备的槽位上，并等待0.5秒延迟时间。

**Q: 自动战斗不工作？**
A: 检查是否开启了自动战斗开关，并确保附近有可攻击的怪物。

**Q: 药水系统异常？**
A: 检查金币是否充足，确保药水配置正确。

**Q: 存档丢失？**
A: 检查saves目录下的备份文件，可尝试加载最近的备份。

---

*最后更新：v1.11.0*
*更多功能持续开发中...* 