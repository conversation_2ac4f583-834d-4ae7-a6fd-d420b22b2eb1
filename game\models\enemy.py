import random
import json
import os
from typing import Dict, List, Tuple, Optional, Union, Any

# 导入怪物图片管理器
try:
    from game.managers.enemy_image_manager import enemy_image_manager
except ImportError:
    print("⚠️ 无法导入怪物图片管理器，图片功能将不可用")
    enemy_image_manager = None

# 怪物类型定义
ENEMY_TYPES = {
    "普通": {
        "战力倍率": 1.0,
        "经验倍率": 1.0,
        "金币倍率": 1.0,
        "掉落倍率": 1.0,
        "颜色": (200, 200, 200)  # 灰色
    },
    "精英": {
        "战力倍率": 2.0,
        "经验倍率": 2.0,
        "金币倍率": 2.0,
        "掉落倍率": 1.5,
        "颜色": (0, 200, 0)  # 绿色
    },
    "首领": {
        "战力倍率": 5.0,
        "经验倍率": 5.0,
        "金币倍率": 5.0,
        "掉落倍率": 3.0,
        "颜色": (200, 0, 0)  # 红色
    },
    "世界BOSS": {
        "战力倍率": 20.0,
        "经验倍率": 20.0,
        "金币倍率": 20.0,
        "掉落倍率": 10.0,
        "颜色": (200, 100, 0)  # 橙色
    }
}


class Enemy:
    """
    怪物基类，包含怪物的基本属性和方法
    """
    def __init__(self, data: Dict = None, skills: Optional[List[Dict]] = None):
        """
        初始化怪物实例
        
        参数:
            data: 怪物数据字典
            skills: 怪物技能列表 (可选, 会覆盖data中的skills)
        """
        # 如果没有提供数据，使用默认值
        if data is None:
            data = {}
        
        # 基本信息
        self.id = data.get("id", 0)
        self.name = data.get("name", "未知怪物")
        self.level = data.get("level", 1)
        self.type = data.get("type", "普通")
        self.description = data.get("description", "")
        
        # 图片相关属性
        self.image = None  # 怪物图片 pygame.Surface
        self.image_size = data.get("image_size", (40, 40))  # 图片大小
        self.image_loaded = False  # 图片是否已加载
        
        # 属性
        self.hp = data.get("hp", 20)
        self.mp = data.get("mp", 0)
        
        # 确保max_hp和max_mp正确设置
        if "max_hp" in data:
            self.max_hp = data["max_hp"]
        else:
            self.max_hp = self.hp  # 如果没有指定max_hp，使用hp作为max_hp
            
        if "max_mp" in data:
            self.max_mp = data["max_mp"]
        else:
            self.max_mp = self.mp  # 如果没有指定max_mp，使用mp作为max_mp
        
        # 当前生命值和法力值
        self.current_hp = self.hp
        self.current_mp = self.mp
        
        # 战斗属性 - 处理字典格式和数值格式
        attack_data = data.get("attack", {})
        if isinstance(attack_data, dict):
            self.attack_min = attack_data.get("min", 1)
            self.attack_max = attack_data.get("max", 2)
        else:
            # 兼容旧格式
            self.attack_min = data.get("attack_min", attack_data if isinstance(attack_data, (int, float)) else 1)
            self.attack_max = data.get("attack_max", attack_data if isinstance(attack_data, (int, float)) else 2)
        
        defense_data = data.get("defense", {})
        if isinstance(defense_data, dict):
            self.defense_min = defense_data.get("min", 0)
            self.defense_max = defense_data.get("max", 0)
        else:
            # 兼容旧格式
            self.defense_min = data.get("defense_min", defense_data if isinstance(defense_data, (int, float)) else 0)
            self.defense_max = data.get("defense_max", defense_data if isinstance(defense_data, (int, float)) else 0)
        
        magic_attack_data = data.get("magic_attack", {})
        if isinstance(magic_attack_data, dict):
            self.magic_attack_min = magic_attack_data.get("min", 0)
            self.magic_attack_max = magic_attack_data.get("max", 0)
        else:
            # 兼容旧格式
            self.magic_attack_min = data.get("magic_attack_min", magic_attack_data if isinstance(magic_attack_data, (int, float)) else 0)
            self.magic_attack_max = data.get("magic_attack_max", magic_attack_data if isinstance(magic_attack_data, (int, float)) else 0)
        
        magic_defense_data = data.get("magic_defense", {})
        if isinstance(magic_defense_data, dict):
            self.magic_defense = magic_defense_data.get("min", 0)  # 使用min值作为魔防
        else:
            # 兼容旧格式
            self.magic_defense = magic_defense_data if isinstance(magic_defense_data, (int, float)) else 0
        self.attack_speed = data.get("attack_speed", 1.0)
        
        # 行为相关
        self.attack_range = data.get("attack_range", 1)  # 攻击范围
        self.sight_range = data.get("sight_range", 5)    # 视野范围
        self.move_speed = data.get("move_speed", 1.0)    # 移动速度
        
        # 位置信息
        self.position = data.get("position", (0, 0))       # 怪物在地图上的位置 (x, y)
        # 为了方便访问，提供x和y属性
        if self.position:
            self.x, self.y = self.position
        else:
            self.x, self.y = 0, 0
        
        # 掉落相关
        self.exp = data.get("exp", data.get("experience", 5))  # 经验值，兼容exp和experience字段
        self.gold = data.get("gold", 1)         # 金币
        self.drops = data.get("drops", [])      # 掉落物品列表
        
        # 技能相关
        if skills is not None:
            self.skills = skills
        else:
            self.skills = data.get("skills", [])    # 技能列表
        self.skill_chance = data.get("skill_chance", 0.2)  # 使用技能的概率
        
        # 应用怪物类型倍率
        self._apply_type_multipliers()
        
        # 战斗状态
        self.is_alive = True
        self.is_attacking = False
        self.target = None
        self.last_attack_time = 0
        
        # 战力计算
        self.battle_power = self._calculate_battle_power()
        
        # 为了兼容战斗管理器，创建stats属性
        self.stats = self
        
        # 添加战斗系统需要的属性
        self.combat_value = self.battle_power
        
        # 确保enemy_type属性存在
        self.enemy_type = self.type
        
        # 尝试加载怪物图片
        self.load_image()
    
    def load_image(self, size: tuple = None):
        """
        加载怪物图片
        
        参数:
            size: 图片大小，如果为None则使用self.image_size
        """
        if enemy_image_manager is None:
            print(f"⚠️ 图片管理器不可用，无法加载 {self.name} 的图片")
            return
        
        if size is None:
            size = self.image_size
        
        try:
            # 使用图片管理器加载图片
            self.image = enemy_image_manager.get_enemy_image(self.name, size)
            self.image_loaded = True
            
            # 更新图片大小
            if self.image:
                self.image_size = self.image.get_size()
                
        except Exception as e:
            print(f"❌ 加载怪物图片失败 {self.name}: {e}")
            self.image = None
            self.image_loaded = False
    
    def get_image(self, size: tuple = None):
        """
        获取怪物图片
        
        参数:
            size: 图片大小
            
        返回:
            pygame.Surface: 怪物图片
        """
        # 如果没有加载图片或需要不同尺寸，重新加载
        if not self.image_loaded or (size and size != self.image_size):
            self.load_image(size)
        
        return self.image
    
    def has_image(self) -> bool:
        """
        检查是否有可用图片
        
        返回:
            bool: 是否有图片
        """
        if enemy_image_manager:
            return enemy_image_manager.has_image(self.name)
        return False
    
    def _apply_type_multipliers(self):
        """应用怪物类型倍率"""
        if self.type in ENEMY_TYPES:
            type_data = ENEMY_TYPES[self.type]
            
            # 应用战力倍率
            power_mult = type_data["战力倍率"]
            
            # 同步更新hp和max_hp
            self.hp = int(self.hp * power_mult)
            self.max_hp = int(self.max_hp * power_mult)
            
            # 同步更新mp和max_mp
            self.mp = int(self.mp * power_mult)
            self.max_mp = int(self.max_mp * power_mult)
            
            # 更新其他属性
            self.attack_min = int(self.attack_min * power_mult)
            self.attack_max = int(self.attack_max * power_mult)
            self.defense_min = int(self.defense_min * power_mult)
            self.defense_max = int(self.defense_max * power_mult)
            self.magic_attack_min = int(self.magic_attack_min * power_mult)
            self.magic_attack_max = int(self.magic_attack_max * power_mult)
            self.magic_defense = int(self.magic_defense * power_mult)
            
            # 应用经验和金币倍率
            self.exp = int(self.exp * type_data["经验倍率"])
            self.gold = int(self.gold * type_data["金币倍率"])
            
            # 确保current_hp和current_mp与max值同步
            self.current_hp = self.hp
            self.current_mp = self.mp
    
    def _calculate_battle_power(self):
        """计算怪物战斗力"""
        # 简单的战斗力计算公式
        battle_power = (
            self.max_hp * 0.5 +
            self.max_mp * 0.3 +
            (self.attack_min + self.attack_max) * 2 +
            (self.defense_min + self.defense_max) * 1.5 +
            (self.magic_attack_min + self.magic_attack_max) * 2 +
            self.magic_defense * 1.5 +
            self.level * 10
        )
        return int(battle_power)
    
    def take_damage(self, damage, damage_type="physical", is_critical=False):
        """
        受到伤害
        
        参数:
            damage: 伤害值
            damage_type: 伤害类型 (physical/magic/tao/true)
            is_critical: 是否暴击
            
        返回:
            实际造成的伤害
        """
        # 应用防御减伤（如果不是真实伤害）
        if damage_type != "true":
            if damage_type == "physical":
                # 物理伤害应用物理防御
                defense = random.randint(self.defense_min, self.defense_max)
                damage = max(1, damage - defense)
            elif damage_type in ["magic", "tao"]:
                # 魔法/道术伤害应用魔法防御
                damage = max(1, damage - self.magic_defense)
        
        # 确保伤害至少为1
        actual_damage = max(1, damage)
        
        # 扣减生命值（使用current_hp）
        self.current_hp -= actual_damage
        
        # 检查是否死亡
        if self.current_hp <= 0:
            self.current_hp = 0
            self.is_alive = False
        
        return actual_damage
    
    def heal(self, amount):
        """
        恢复生命值
        
        参数:
            amount: 恢复量
            
        返回:
            实际恢复量
        """
        before_hp = self.current_hp
        self.current_hp = min(self.max_hp, self.current_hp + amount)
        
        # 如果之前已死亡，现在恢复了生命，则重新设置为活着
        if before_hp <= 0 and self.current_hp > 0:
            self.is_alive = True
        
        return self.current_hp - before_hp
    
    def calculate_attack(self, is_critical=False):
        """
        计算攻击伤害（兼容战斗系统）
        
        参数:
            is_critical: 是否暴击
            
        返回:
            攻击伤害值
        """
        # 随机生成伤害值
        damage = random.randint(self.attack_min, self.attack_max)
        
        # 如果暴击，增加伤害
        if is_critical:
            damage = int(damage * self.critical_damage)
            
        return damage
    
    def calculate_magic_attack(self, magic_power=1.0, is_critical=False):
        """
        计算魔法攻击伤害（兼容战斗系统）
        
        参数:
            magic_power: 技能魔法强度
            is_critical: 是否暴击
            
        返回:
            魔法攻击伤害值
        """
        # 随机生成魔法伤害值
        base_damage = random.randint(self.magic_attack_min, self.magic_attack_max)
        damage = base_damage * magic_power
        
        # 如果暴击，增加伤害
        if is_critical:
            damage = int(damage * self.critical_damage)
            
        return int(damage)
    
    def calculate_tao_attack(self, tao_power=1.0, is_critical=False):
        """
        计算道术攻击伤害（兼容战斗系统）
        
        参数:
            tao_power: 技能道术强度
            is_critical: 是否暴击
            
        返回:
            道术攻击伤害值
        """
        # 随机生成道术伤害值  
        base_damage = random.randint(self.magic_attack_min, self.magic_attack_max)  # 使用魔法攻击作为道术
        damage = base_damage * tao_power
        
        # 如果暴击，增加伤害
        if is_critical:
            damage = int(damage * self.critical_damage)
            
        return int(damage)
    
    def attack(self, target):
        """
        攻击目标
        
        参数:
            target: 攻击目标
            
        返回:
            实际造成的伤害
        """
        # 计算攻击伤害
        base_damage = random.randint(self.attack_min, self.attack_max)
        
        # 应用目标防御
        if hasattr(target, 'defense_min') and hasattr(target, 'defense_max'):
            target_defense = random.randint(target.defense_min, target.defense_max)
            damage = max(1, base_damage - target_defense)
        else:
            damage = base_damage
        
        # 应用伤害
        if hasattr(target, 'take_damage'):
            return target.take_damage(damage)
        
        return damage
    
    def use_skill(self, target, skill_index=None):
        """
        使用技能
        
        参数:
            target: 技能目标
            skill_index: 技能索引，如果为None则随机选择
            
        返回:
            技能效果描述
        """
        if not self.skills:
            return f"{self.name}没有可用的技能"
        
        # 如果没有指定技能，随机选择一个
        if skill_index is None or skill_index >= len(self.skills):
            skill_index = random.randint(0, len(self.skills) - 1)
        
        skill = self.skills[skill_index]
        skill_name = skill.get("name", "未知技能")
        
        # 检查MP是否足够
        mp_cost = skill.get("mp_cost", 0)
        if self.mp < mp_cost:
            return f"{self.name}的魔法值不足，无法使用{skill_name}"
        
        # 消耗MP
        self.mp -= mp_cost
        
        # 获取技能伤害倍率
        damage_multiplier = skill.get("damage_multiplier", 1.0)
        
        # 计算技能伤害
        if damage_multiplier > 0:
            base_damage = random.randint(self.attack_min, self.attack_max)
            skill_damage = int(base_damage * damage_multiplier)
            
            # 处理范围技能
            if skill.get("area_effect", False):
                area_radius = skill.get("area_radius", 1)
                return f"{self.name}使用了{skill_name}，对半径{area_radius}范围内的敌人造成了{skill_damage}点伤害"
            
            # 单体技能
            if hasattr(target, 'take_damage'):
                actual_damage = target.take_damage(skill_damage)
                return f"{self.name}使用了{skill_name}，对{target.name}造成了{actual_damage}点伤害"
            
            return f"{self.name}使用了{skill_name}，造成了{skill_damage}点伤害"
        
        # 处理增益技能
        if "defense_boost" in skill:
            defense_boost = skill.get("defense_boost", 0)
            duration = skill.get("duration", 5)
            # 简化处理，直接增加防御值
            self.defense_min += defense_boost
            self.defense_max += defense_boost
            return f"{self.name}使用了{skill_name}，防御力提高了{defense_boost}点，持续{duration}秒"
        
        # 处理召唤技能
        if "summon_type" in skill:
            summon_type = skill.get("summon_type", "")
            summon_count = skill.get("summon_count", {"min": 1, "max": 1})
            count = random.randint(summon_count.get("min", 1), summon_count.get("max", 1))
            return f"{self.name}使用了{skill_name}，召唤了{count}个{summon_type}"
        
        return f"{self.name}使用了{skill_name}"
    
    def get_drops(self):
        """
        获取掉落物品
        
        返回:
            掉落物品列表
        """
        drops = []
        
        # 计算金币掉落
        gold_drop = random.randint(int(self.gold * 0.8), int(self.gold * 1.2))
        drops.append({"type": "gold", "amount": gold_drop})
        
        # 计算物品掉落
        for drop in self.drops:
            drop_rate = drop.get("rate", 0.1)
            # 应用怪物类型掉落倍率
            if self.type in ENEMY_TYPES:
                drop_rate *= ENEMY_TYPES[self.type]["掉落倍率"]
            
            if random.random() < drop_rate:
                drops.append({"type": "item", "item": drop["item"]})
        
        return drops
    
    def update(self, delta_time, current_time=None):
        """
        更新怪物状态
        
        参数:
            delta_time: 时间增量（秒）
            current_time: 当前游戏时间（可选，如果不提供则使用累积时间）
        """
        # 如果怪物已死亡，不执行任何操作
        if not self.is_alive:
            return
        
        # 如果没有提供current_time，使用累积的delta_time
        if current_time is None:
            if not hasattr(self, '_accumulated_time'):
                self._accumulated_time = 0.0
            self._accumulated_time += delta_time
            current_time = self._accumulated_time
        
        # 如果正在攻击目标
        if self.is_attacking and self.target:
            # 检查攻击冷却
            attack_cooldown = 1.0 / self.attack_speed
            
            if current_time - self.last_attack_time >= attack_cooldown:
                # 随机决定是使用普通攻击还是技能
                if self.skills and random.random() < self.skill_chance:
                    # 使用技能
                    self.use_skill(self.target)
                else:
                    # 普通攻击
                    self.attack(self.target)
                
                self.last_attack_time = current_time
    
    def get_data(self):
        """
        获取怪物数据
        
        返回:
            怪物数据字典
        """
        return {
            "id": self.id,
            "name": self.name,
            "level": self.level,
            "type": self.type,
            "description": self.description,
            "hp": self.hp,
            "max_hp": self.max_hp,
            "mp": self.mp,
            "max_mp": self.max_mp,
            "attack_min": self.attack_min,
            "attack_max": self.attack_max,
            "defense_min": self.defense_min,
            "defense_max": self.defense_max,
            "magic_attack_min": self.magic_attack_min,
            "magic_attack_max": self.magic_attack_max,
            "magic_defense": self.magic_defense,
            "attack_speed": self.attack_speed,
            "attack_range": self.attack_range,
            "sight_range": self.sight_range,
            "move_speed": self.move_speed,
            "exp": self.exp,
            "gold": self.gold,
            "drops": self.drops,
            "skills": self.skills,
            "battle_power": self.battle_power,
            "is_alive": self.is_alive,
            "position": self.position
        }
    
    def reset_combat(self):
        """
        重置战斗状态
        """
        self.current_hp = self.max_hp
        self.current_mp = self.max_mp
        self.is_alive = True
        self.is_attacking = False
        self.target = None
        self.last_attack_time = 0
    
    def get_max_hp(self):
        """
        获取最大生命值
        
        返回:
            int: 最大生命值
        """
        return self.max_hp
    
    def get_max_mp(self):
        """
        获取最大法力值
        
        返回:
            int: 最大法力值
        """
        return self.max_mp
    
    def get_total_luck(self):
        """
        获取总幸运值（为了兼容战斗系统）
        
        返回:
            int: 幸运值
        """
        return 0  # 怪物默认没有幸运值
    
    def get_total_curse(self):
        """
        获取总诅咒值（为了兼容战斗系统）
        
        返回:
            int: 诅咒值
        """
        return 0  # 怪物默认没有诅咒值
    
    def get_accuracy(self):
        """
        获取命中率（为了兼容战斗系统）
        
        返回:
            float: 命中率
        """
        return 0.8  # 怪物默认80%命中率
    
    def get_agility(self):
        """
        获取敏捷值（为了兼容战斗系统）
        
        返回:
            float: 敏捷值
        """
        return self.attack_speed * 10  # 基于攻击速度计算敏捷
    
    def get_attack_range(self):
        """
        获取攻击范围（为了兼容战斗系统）
        
        返回:
            tuple: (最小攻击力, 最大攻击力)
        """
        return (self.attack_min, self.attack_max)
    
    @property
    def critical_rate(self):
        """
        暴击率属性（为了兼容战斗系统）
        
        返回:
            float: 暴击率
        """
        return 0.05  # 怪物默认5%暴击率
    
    @property
    def critical_damage(self):
        """
        暴击伤害倍率（为了兼容战斗系统）
        
        返回:
            float: 暴击伤害倍率
        """
        return 1.5  # 怪物默认1.5倍暴击伤害
    
    @property
    def magic_min(self):
        """
        最小魔法攻击力（为了兼容战斗系统）
        
        返回:
            int: 最小魔法攻击力
        """
        return self.magic_attack_min
    
    @property
    def magic_max(self):
        """
        最大魔法攻击力（为了兼容战斗系统）
        
        返回:
            int: 最大魔法攻击力
        """
        return self.magic_attack_max
    
    @property
    def tao_min(self):
        """
        最小道术攻击力（为了兼容战斗系统）
        
        返回:
            int: 最小道术攻击力（怪物没有道术，返回0）
        """
        return 0
    
    @property
    def tao_max(self):
        """
        最大道术攻击力（为了兼容战斗系统）
        
        返回:
            int: 最大道术攻击力（怪物没有道术，返回0）
        """
        return 0
    
    def to_dict(self):
        """
        将敌人对象转换为字典（为了兼容战斗日志）
        
        返回:
            dict: 敌人数据字典
        """
        return {
            "name": self.name,
            "level": self.level,
            "type": self.type,
            "hp": self.current_hp,
            "max_hp": self.max_hp,
            "mp": self.current_mp,
            "max_mp": self.max_mp,
            "battle_power": self.battle_power
        }


class EnemyFactory:
    """
    怪物工厂，负责创建和管理怪物
    """
    def __init__(self):
        """初始化怪物工厂"""
        self.monster_templates = {}
        self._load_monster_templates()
    
    def _load_monster_templates(self):
        """加载怪物模板数据 - 使用统一的MonsterDataLoader"""
        try:
            from game.models.monster_data_loader import MonsterDataLoader
            loader = MonsterDataLoader.get_instance()
            loader.load_data()  # 确保数据已加载
            
            # 获取所有怪物名称并构建模板字典
            self.monster_templates = {}
            if loader.monsters_data:
                for monster_name in loader.monsters_data.keys():
                    monster_data = loader.get_full_monster_data(monster_name)
                    if monster_data:
                        self.monster_templates[monster_name] = monster_data
            
            print(f"成功加载 {len(self.monster_templates)} 个怪物模板")
            
            # 检查特定怪物是否存在
            target_monsters = ['多钩猫', '钉耙猫', '毒蜘蛛', '森林雪人']
            for monster_name in target_monsters:
                if monster_name in self.monster_templates:
                    print(f"✓ 找到怪物: {monster_name}")
                else:
                    print(f"✗ 未找到怪物: {monster_name}")
                    
        except Exception as e:
            print(f"加载怪物模板失败: {e}")
            import traceback
            traceback.print_exc()
            self.monster_templates = {}
    
    def _map_json_type_to_standard(self, json_type: str, level: int, is_world_boss: bool = False) -> str:
        """
        将JSON数据中的type字段映射到ENEMY_TYPES标准类型
        
        参数:
            json_type: JSON中的原始type值 ("boss", "monster", "object")
            level: 怪物等级
            is_world_boss: 是否为世界BOSS地图
            
        返回:
            str: 映射后的标准类型 ("普通", "精英", "首领", "世界BOSS")
        """
        # 对于object类型，直接返回，不应用ENEMY_TYPES倍率
        if json_type == "object":
            return json_type
        
        # 根据JSON类型和地图类型进行映射
        if json_type == "boss":
            # boss类型根据地图类型区分首领和世界BOSS
            if is_world_boss:  # 世界BOSS地图中的boss映射为世界BOSS
                return "世界BOSS"
            else:  # 普通地图中的boss映射为首领
                return "首领"
        elif json_type == "monster":
            # monster类型根据等级区分普通和精英
            if level >= 30:  # 高等级monster映射为精英
                return "精英"
            else:  # 低等级monster映射为普通
                return "普通"
        else:
            # 未知类型默认为普通
            return "普通"
    
    def create_enemy(self, name, level_adjust=0, type_override=None, position=None, is_world_boss_map=False):
        """
        创建怪物实例
        
        参数:
            name: 怪物模板名称
            level_adjust: 等级调整值
            type_override: 覆盖怪物类型
            position: 怪物位置
            is_world_boss_map: 是否为世界BOSS地图
            
        返回:
            怪物实例
        """
        # 如果模板不存在，返回None
        if name not in self.monster_templates:
            print(f"怪物模板 {name} 不存在")
            return None
        
        # 复制模板数据
        template = self.monster_templates[name].copy()
        
        # 映射JSON中的type字段到ENEMY_TYPES标准类型
        original_type = template.get("type", "monster")
        mapped_type = self._map_json_type_to_standard(original_type, template.get("level", 1), is_world_boss_map)
        template["type"] = mapped_type
        
        # 调试信息：显示类型映射结果（仅在类型发生变化时显示）
        if original_type != mapped_type:
            level_value = str(template.get('level', 1))
            print(f"🔄 类型映射: {name} (等级{str(level_value)}) {original_type} -> {mapped_type}")
        
        # 调整等级
        if level_adjust != 0:
            template["level"] += level_adjust
            
            # 简单的属性缩放
            scale_factor = 1.0 + level_adjust * 0.1  # 每级提升10%
            
            # 同步更新hp和max_hp
            template["hp"] = int(template["hp"] * scale_factor)
            if "max_hp" not in template:
                template["max_hp"] = template["hp"]  # 确保有max_hp字段
            else:
                template["max_hp"] = int(template["max_hp"] * scale_factor)
            
            # 同步更新mp和max_mp
            if "mp" in template:
                template["mp"] = int(template["mp"] * scale_factor)
                if "max_mp" not in template:
                    template["max_mp"] = template["mp"]  # 确保有max_mp字段
                else:
                    template["max_mp"] = int(template["max_mp"] * scale_factor)
            
            # 处理嵌套的攻击属性
            if "attack" in template and isinstance(template["attack"], dict):
                template["attack"]["min"] = int(template["attack"]["min"] * scale_factor)
                template["attack"]["max"] = int(template["attack"]["max"] * scale_factor)
            
            # 处理嵌套的防御属性
            if "defense" in template and isinstance(template["defense"], dict):
                template["defense"]["min"] = int(template["defense"]["min"] * scale_factor)
                template["defense"]["max"] = int(template["defense"]["max"] * scale_factor)
            
            # 处理嵌套的魔法攻击属性
            if "magic_attack" in template and isinstance(template["magic_attack"], dict):
                template["magic_attack"]["min"] = int(template["magic_attack"]["min"] * scale_factor)
                template["magic_attack"]["max"] = int(template["magic_attack"]["max"] * scale_factor)
            
            # 处理嵌套的魔法防御属性
            if "magic_defense" in template and isinstance(template["magic_defense"], dict):
                template["magic_defense"]["min"] = int(template["magic_defense"]["min"] * scale_factor)
                template["magic_defense"]["max"] = int(template["magic_defense"]["max"] * scale_factor)
            elif "magic_defense" in template:
                template["magic_defense"] = int(template["magic_defense"] * scale_factor)
            
            # 处理经验值（兼容exp和experience字段）
            if "experience" in template:
                template["experience"] = int(template["experience"] * scale_factor)
            elif "exp" in template:
                template["exp"] = int(template["exp"] * scale_factor)
            
            if "gold" in template:
                template["gold"] = int(template["gold"] * scale_factor)
        
        # 覆盖类型（如果指定了type_override且是有效的标准类型）
        if type_override and type_override in ENEMY_TYPES:
            template["type"] = type_override
        
        # 设置位置
        if position:
            template["position"] = position
        
        # 创建怪物实例
        enemy_instance = Enemy(template)
        return enemy_instance
    
    def create_random_enemy(self, level_range=(1, 5), types=None, position=None):
        """
        创建随机怪物
        
        参数:
            level_range: 等级范围
            types: 怪物类型列表
            position: 怪物位置
            
        返回:
            随机怪物实例
        """
        if not self.monster_templates:
            print("没有可用的怪物模板")
            return None
        
        # 随机选择模板
        template_name = random.choice(list(self.monster_templates.keys()))
        
        # 随机等级调整
        min_level, max_level = level_range
        base_level = self.monster_templates[template_name]["level"]
        level_adjust = random.randint(min_level, max_level) - base_level
        
        # 随机类型
        type_override = None
        if types:
            type_override = random.choice(types)
        
        # 创建怪物（随机怪物默认不在世界BOSS地图）
        return self.create_enemy(template_name, level_adjust, type_override, position, False)
    
    def create_enemies_for_map(self, map_data, count=5, position=None):
        """
        为地图创建怪物
        
        参数:
            map_data: 地图数据
            count: 创建的怪物数量
            position: 怪物位置
            
        返回:
            怪物列表
        """
        if not self.monster_templates:
            print("没有可用的怪物模板")
            return []
        
        enemies = []
        monster_list = map_data.get("monsters", [])
        
        if not monster_list:
            print(f"地图 {map_data.get('name', '未知')} 没有定义怪物")
            return []
        
        # 计算总权重
        total_weight = sum(monster["weight"] for monster in monster_list)
        
        # 创建指定数量的怪物
        for _ in range(count):
            # 随机选择怪物
            rand_value = random.uniform(0, total_weight)
            cumulative_weight = 0
            selected_monster = None
            
            for monster in monster_list:
                cumulative_weight += monster["weight"]
                if rand_value <= cumulative_weight:
                    selected_monster = monster["name"]
                    break
            
            if selected_monster:
                # 根据地图难度调整等级
                level_adjust = map_data.get("difficulty", 1) - 1
                
                # 检查是否为世界BOSS地图
                is_world_boss_map = map_data.get("is_world_boss", False)
                
                # 创建怪物
                enemy = self.create_enemy(selected_monster, level_adjust, None, position, is_world_boss_map)
                if enemy:
                    enemies.append(enemy)
        
        return enemies
    
    def create_boss_for_map(self, map_data, position=None):
        """
        为地图创建BOSS
        
        参数:
            map_data: 地图数据
            position: BOSS位置
            
        返回:
            BOSS怪物实例
        """
        if not self.monster_templates:
            print("没有可用的怪物模板")
            return None
        
        monster_list = map_data.get("monsters", [])
        
        if not monster_list:
            print(f"地图 {map_data.get('name', '未知')} 没有定义怪物")
            return None
        
        # 检查是否为世界BOSS地图
        is_world_boss_map = map_data.get("is_world_boss", False)
        
        # 首先检查地图中是否有标记为Boss的怪物
        boss_monsters = [monster for monster in monster_list if monster.get("is_boss", False)]
        
        if boss_monsters:
            # 如果地图中有定义Boss怪物，从中随机选择一个
            selected_boss = random.choice(boss_monsters)
            boss_name = selected_boss["name"]
            
            # 检查该Boss是否在怪物模板中存在
            if boss_name in self.monster_templates:
                # 创建指定的Boss，传递世界BOSS地图标记
                return self.create_enemy(boss_name, map_data.get("difficulty", 1), None, position, is_world_boss_map)
            else:
                print(f"警告: 地图 {map_data.get('name', '未知')} 中定义的Boss '{boss_name}' 在怪物模板中不存在")
        
        # 如果地图中没有定义Boss，或者定义的Boss不存在，则从地图怪物中选择权重最低的作为Boss
        # 找出权重最低的怪物
        min_weight_monster = min(monster_list, key=lambda x: x["weight"])
        
        # 根据地图类型决定Boss类型
        boss_type = "世界BOSS" if is_world_boss_map else "首领"
        
        # 创建怪物并升级为对应类型的Boss
        return self.create_enemy(min_weight_monster["name"], map_data.get("difficulty", 1), boss_type, position, is_world_boss_map)


# 创建全局怪物工厂实例
enemy_factory = EnemyFactory()