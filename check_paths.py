#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径检查工具
检查项目中是否存在硬编码的绝对路径
确保所有路径都是相对路径，适合打包
"""

import os
import re
from pathlib import Path

def check_absolute_paths():
    """检查项目中的绝对路径使用"""
    print("🔍 检查项目中的绝对路径...")
    
    # 绝对路径模式
    absolute_patterns = [
        r'[C-Z]:\\',  # Windows盘符
        r'/home/',    # Linux家目录
        r'/usr/',     # Linux系统目录
        r'/var/',     # Linux变量目录
    ]
    
    # 要检查的文件类型
    file_patterns = ['*.py', '*.json']
    
    # 要排除的目录
    exclude_dirs = {
        '__pycache__', 
        '.git', 
        'build', 
        'dist', 
        'specs',
        'build_logs',
        'logs'
    }
    
    issues = []
    checked_files = 0
    
    for pattern in file_patterns:
        for file_path in Path('.').rglob(pattern):
            # 跳过排除的目录
            if any(part in exclude_dirs for part in file_path.parts):
                continue
                
            checked_files += 1
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for line_num, line in enumerate(content.split('\n'), 1):
                    for pattern in absolute_patterns:
                        if re.search(pattern, line):
                            issues.append({
                                'file': str(file_path),
                                'line': line_num,
                                'content': line.strip(),
                                'pattern': pattern
                            })
            except UnicodeDecodeError:
                print(f"⚠️  跳过二进制文件: {file_path}")
            except Exception as e:
                print(f"❌ 检查文件失败 {file_path}: {e}")
    
    print(f"✅ 检查完成，共检查 {checked_files} 个文件")
    
    if issues:
        print(f"\n❌ 发现 {len(issues)} 个可能的绝对路径问题:")
        for issue in issues:
            print(f"  📁 {issue['file']}:{issue['line']}")
            print(f"     {issue['content']}")
            print()
    else:
        print("✅ 没有发现绝对路径问题！")
    
    return issues

def check_resource_usage():
    """检查资源路径的使用方式"""
    print("\n🔍 检查资源路径使用方式...")
    
    good_patterns = [
        'resource_path(',
        'get_game_data_path(',
        'get_game_asset_path(',
        'get_resource_path(',
        'os.path.join(',
        'os.path.dirname(__file__)'
    ]
    
    bad_patterns = [
        'open("/',         # 直接打开绝对路径
        'open("C:',        # Windows绝对路径
        'pygame.image.load("/', # 直接加载绝对路径
    ]
    
    good_usage = 0
    bad_usage = []
    
    for py_file in Path('.').rglob('*.py'):
        # 跳过排除的目录
        if any(part in {'__pycache__', '.git', 'build', 'dist'} for part in py_file.parts):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查好的用法
            for pattern in good_patterns:
                good_usage += content.count(pattern)
            
            # 检查坏的用法
            for line_num, line in enumerate(content.split('\n'), 1):
                for pattern in bad_patterns:
                    if pattern in line:
                        bad_usage.append({
                            'file': str(py_file),
                            'line': line_num,
                            'content': line.strip()
                        })
        except Exception as e:
            print(f"❌ 检查文件失败 {py_file}: {e}")
    
    print(f"✅ 发现 {good_usage} 个正确的资源路径使用")
    
    if bad_usage:
        print(f"❌ 发现 {len(bad_usage)} 个可能有问题的路径使用:")
        for usage in bad_usage:
            print(f"  📁 {usage['file']}:{usage['line']}")
            print(f"     {usage['content']}")
    else:
        print("✅ 所有资源路径使用方式都正确！")
    
    return good_usage, bad_usage

def check_import_paths():
    """检查导入路径"""
    print("\n🔍 检查模块导入路径...")
    
    issues = []
    
    for py_file in Path('.').rglob('*.py'):
        # 跳过排除的目录
        if any(part in {'__pycache__', '.git', 'build', 'dist'} for part in py_file.parts):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 检查sys.path.append使用
                    if 'sys.path.append' in line and 'abspath' in line:
                        # 这通常是正确的相对路径设置
                        continue
                    elif 'sys.path.append' in line and any(c in line for c in ['C:', 'D:', 'E:', '/']):
                        issues.append({
                            'file': str(py_file),
                            'line': line_num,
                            'content': line.strip(),
                            'type': 'sys.path绝对路径'
                        })
        except Exception as e:
            print(f"❌ 检查文件失败 {py_file}: {e}")
    
    if issues:
        print(f"❌ 发现 {len(issues)} 个导入路径问题:")
        for issue in issues:
            print(f"  📁 {issue['file']}:{issue['line']}")
            print(f"     {issue['content']}")
    else:
        print("✅ 所有导入路径都正确！")
    
    return issues

def main():
    """主检查函数"""
    print("=" * 60)
    print("🧪 萝卜传奇游戏 - 路径使用检查工具")
    print("=" * 60)
    
    # 检查绝对路径
    abs_issues = check_absolute_paths()
    
    # 检查资源路径使用
    good_count, bad_usage = check_resource_usage()
    
    # 检查导入路径
    import_issues = check_import_paths()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 检查总结")
    print("=" * 60)
    
    total_issues = len(abs_issues) + len(bad_usage) + len(import_issues)
    
    if total_issues == 0:
        print("🎉 恭喜！项目路径使用完全正确！")
        print("✅ 适合打包成exe文件")
        print("✅ 所有路径都是相对路径")
        print("✅ 使用了正确的资源管理器")
    else:
        print(f"⚠️  发现 {total_issues} 个需要注意的问题")
        print("建议修复后再进行打包")
    
    print(f"\n📈 使用统计:")
    print(f"- 正确的资源路径使用: {good_count} 次")
    print(f"- 绝对路径问题: {len(abs_issues)} 个")
    print(f"- 资源使用问题: {len(bad_usage)} 个")
    print(f"- 导入路径问题: {len(import_issues)} 个")

if __name__ == "__main__":
    main() 