{"金创药(小量)": {"name": "金创药(小量)", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 10, "effects": {"hp_restore": 20, "recovery_type": "slow", "cooldown": 3000}, "rarity": "common", "sell_price": 140, "description": "缓慢恢复少量生命值"}, "魔法药(小量)": {"name": "魔法药(小量)", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 10, "effects": {"mp_restore": 30, "recovery_type": "slow", "cooldown": 3000}, "rarity": "common", "sell_price": 210, "description": "缓慢恢复少量魔法值"}, "金创药(中量)": {"name": "金创药(中量)", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 10, "effects": {"hp_restore": 50, "recovery_type": "slow", "cooldown": 3000}, "rarity": "common", "sell_price": 350, "description": "缓慢恢复中量生命值"}, "魔法药(中量)": {"name": "魔法药(中量)", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 10, "effects": {"mp_restore": 80, "recovery_type": "slow", "cooldown": 3000}, "rarity": "common", "sell_price": 560, "description": "缓慢恢复中量魔法值"}, "强效金创药": {"name": "强效金创药", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 5, "effects": {"hp_restore": 90, "recovery_type": "slow", "cooldown": 3000}, "rarity": "rare", "sell_price": 630, "description": "缓慢恢复大量生命值"}, "强效魔法药": {"name": "强效魔法药", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 5, "effects": {"mp_restore": 150, "recovery_type": "slow", "cooldown": 3000}, "rarity": "rare", "sell_price": 630, "description": "缓慢恢复大量魔法值"}, "太阳水": {"name": "太阳水", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 10, "effects": {"hp_restore": 30, "mp_restore": 40, "recovery_type": "instant", "cooldown": 3000}, "rarity": "common", "sell_price": 350, "description": "立刻恢复少量生命值和魔法值"}, "强效太阳水": {"name": "强效太阳水", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 5, "effects": {"hp_restore": 50, "mp_restore": 80, "recovery_type": "instant", "cooldown": 3000}, "rarity": "rare", "sell_price": 650, "description": "立刻恢复中量生命值和魔法值"}, "万年雪霜": {"name": "万年雪霜", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 3, "effects": {"hp_restore": 100, "mp_restore": 100, "recovery_type": "instant", "cooldown": 3000}, "rarity": "epic", "sell_price": 1000, "description": "立刻恢复大量生命值和魔法值"}, "疗伤药": {"name": "疗伤药", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 3, "effects": {"hp_restore": 100, "mp_restore": 160, "recovery_type": "instant", "cooldown": 3000}, "rarity": "epic", "sell_price": 1300, "description": "立刻恢复大量生命值和超大量魔法值"}, "鹿茸": {"name": "鹿茸", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 3, "effects": {"hp_restore": 100, "mp_restore": 100, "recovery_type": "instant", "cooldown": 3000}, "rarity": "epic", "sell_price": 1000, "description": "立刻恢复大量生命值和魔法值"}, "鹿血": {"name": "鹿血", "type": "consumable", "category": "potion", "stackable": true, "max_stack": 20, "effects": {"hp_restore": 20, "mp_restore": 20, "recovery_type": "instant", "cooldown": 3000}, "rarity": "common", "sell_price": 20, "description": "立刻恢复少量生命值和魔法值"}, "祝福油": {"name": "祝福油", "type": "consumable", "category": "enhancement", "stackable": true, "max_stack": 5, "effects": {"luck_bonus": 1}, "rarity": "rare", "sell_price": 5000, "description": "增加装备幸运值"}, "山洞凭证": {"name": "山洞凭证", "type": "consumable", "category": "quest_item", "stackable": true, "max_stack": 1, "effects": {"special_access": "cave"}, "rarity": "rare", "sell_price": 10000, "description": "进入特殊山洞的凭证"}, "传送卷轴": {"name": "传送卷轴", "type": "consumable", "category": "scroll", "stackable": true, "max_stack": 20, "effects": {"teleport": "town"}, "rarity": "common", "sell_price": 500, "description": "使用后可传送回城"}, "幸运符": {"name": "幸运符", "type": "consumable", "category": "buff", "stackable": true, "max_stack": 10, "effects": {"luck_bonus": 5, "duration": 600}, "rarity": "rare", "sell_price": 5000, "description": "使用后暂时提升幸运值"}, "强化卷轴": {"name": "强化卷轴", "type": "consumable", "category": "enhancement", "stackable": true, "max_stack": 5, "effects": {"equipment_upgrade": true}, "rarity": "epic", "sell_price": 20000, "description": "有几率提高装备品质等级"}, "金锭": {"name": "金锭", "type": "consumable", "category": "currency", "stackable": true, "max_stack": 10, "effects": {"gold_value": 100000}, "rarity": "rare", "sell_price": 100000, "description": "使用后可兑换成100,000金币"}, "金条": {"name": "金条", "type": "consumable", "category": "currency", "stackable": true, "max_stack": 5, "effects": {"gold_value": 1000000}, "rarity": "epic", "sell_price": 1000000, "description": "使用后可兑换成1,000,000金币"}, "金砖": {"name": "金砖", "type": "consumable", "category": "currency", "stackable": true, "max_stack": 3, "effects": {"gold_value": 10000000}, "rarity": "legendary", "sell_price": 10000000, "description": "使用后可兑换成10,000,000金币"}, "一个元宝": {"name": "一个元宝", "type": "consumable", "category": "currency", "stackable": true, "max_stack": 99, "effects": {"yuanbao_value": 1}, "rarity": "rare", "sell_price": 0, "description": "使用后可获得1个元宝"}, "十个元宝": {"name": "十个元宝", "type": "consumable", "category": "currency", "stackable": true, "max_stack": 99, "effects": {"yuanbao_value": 10}, "rarity": "epic", "sell_price": 0, "description": "使用后可获得10个元宝"}}