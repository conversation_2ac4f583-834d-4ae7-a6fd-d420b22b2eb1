#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from typing import Dict, Any, List, Optional

class ShopManager:
    """
    通用商城管理器
    负责管理不同类型的商城（金币商城、元宝商城、积分商城等）
    """
    
    def __init__(self):
        """
        初始化商城管理器
        """
        self.config = self._load_main_config()
        self.shop_configs = {}
        self.current_shop_type = self.config.get('main_settings', {}).get('default_shop', 'gold_shop')
        
        # 加载所有商城配置
        self._load_all_shop_configs()
        
    def _load_main_config(self) -> Dict[str, Any]:
        """
        加载主商城配置文件
        
        Returns:
            Dict[str, Any]: 主配置数据
        """
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'shop_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载主商城配置失败: {e}")
            return {
                "shop_types": {
                    "gold_shop": {
                        "name": "金币商城",
                        "description": "使用金币购买基础装备和道具",
                        "currency": "gold",
                        "enabled": True,
                        "priority": 1
                    }
                },
                "main_settings": {
                    "default_shop": "gold_shop"
                }
            }
    
    def _load_all_shop_configs(self):
        """
        加载所有商城的具体配置
        """
        shop_types = self.config.get('shop_types', {})
        
        for shop_type, shop_info in shop_types.items():
            if shop_info.get('enabled', True):
                self._load_shop_config(shop_type)
    
    def _load_shop_config(self, shop_type: str):
        """
        加载指定商城的配置文件
        
        Args:
            shop_type: 商城类型
        """
        try:
            config_filename = f"{shop_type}_config.json"
            config_path = os.path.join(os.path.dirname(__file__), '..', 'data', config_filename)
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self.shop_configs[shop_type] = json.load(f)
                print(f"✅ 成功加载{shop_type}配置")
        except Exception as e:
            print(f"⚠️ 加载{shop_type}配置失败: {e}")
            # 创建默认配置
            self.shop_configs[shop_type] = {
                "categories": {},
                "shop_settings": {
                    "currency_type": shop_type.split('_')[0]
                }
            }
    
    def get_available_shops(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的商城类型
        
        Returns:
            List[Dict[str, Any]]: 商城类型列表
        """
        shops = []
        shop_types = self.config.get('shop_types', {})
        
        for shop_type, shop_info in shop_types.items():
            if shop_info.get('enabled', True) and shop_type in self.shop_configs:
                shops.append({
                    'type': shop_type,
                    'name': shop_info['name'],
                    'description': shop_info['description'],
                    'currency': shop_info['currency'],
                    'priority': shop_info.get('priority', 999)
                })
        
        # 按优先级排序
        shops.sort(key=lambda x: x['priority'])
        return shops
    
    def set_current_shop(self, shop_type: str) -> bool:
        """
        设置当前商城类型
        
        Args:
            shop_type: 商城类型
            
        Returns:
            bool: 是否设置成功
        """
        if shop_type in self.shop_configs:
            self.current_shop_type = shop_type
            print(f"切换到{shop_type}")
            return True
        return False
    
    def get_current_shop_info(self) -> Dict[str, Any]:
        """
        获取当前商城信息
        
        Returns:
            Dict[str, Any]: 当前商城信息
        """
        shop_types = self.config.get('shop_types', {})
        return shop_types.get(self.current_shop_type, {})
    
    def get_categories(self, shop_type: str = None) -> Dict[str, Any]:
        """
        获取指定商城的所有分类
        
        Args:
            shop_type: 商城类型，默认为当前商城
            
        Returns:
            Dict[str, Any]: 分类信息
        """
        if shop_type is None:
            shop_type = self.current_shop_type
            
        if shop_type in self.shop_configs:
            return self.shop_configs[shop_type].get('categories', {})
        return {}
    
    def get_category_items(self, category_name: str, shop_type: str = None) -> List[Dict[str, Any]]:
        """
        获取指定分类的商品列表
        
        Args:
            category_name: 分类名称
            shop_type: 商城类型，默认为当前商城
            
        Returns:
            List[Dict[str, Any]]: 商品列表
        """
        if shop_type is None:
            shop_type = self.current_shop_type
            
        categories = self.get_categories(shop_type)
        if category_name in categories:
            return categories[category_name].get('items', [])
        return []
    
    def can_purchase_item(self, player, item_id: str, category_name: str, shop_type: str = None) -> Dict[str, Any]:
        """
        检查玩家是否可以购买指定商品
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            category_name: 分类名称
            shop_type: 商城类型，默认为当前商城
            
        Returns:
            Dict[str, Any]: 检查结果
        """
        if shop_type is None:
            shop_type = self.current_shop_type
            
        try:
            # 获取商品信息
            items = self.get_category_items(category_name, shop_type)
            item_info = None
            for item in items:
                if item['item_id'] == item_id:
                    item_info = item
                    break
            
            if not item_info:
                return {'can_purchase': False, 'reason': '商品不存在'}
            
            # 获取商城信息
            shop_info = self.config.get('shop_types', {}).get(shop_type, {})
            currency_type = shop_info.get('currency', 'gold')
            
            # 检查玩家货币
            player_currency = self._get_player_currency(player, currency_type)
            item_cost = self._calculate_item_cost(player, item_info, currency_type)
            
            if player_currency < item_cost:
                return {
                    'can_purchase': False, 
                    'reason': f'{currency_type}不足，需要{item_cost}，当前{player_currency}'
                }
            
            # 检查等级要求
            level_req = item_info.get('level_requirement', 0)
            if player.level < level_req:
                return {
                    'can_purchase': False,
                    'reason': f'等级不足，需要等级{level_req}'
                }
            
            # 检查职业要求
            class_req = item_info.get('class_requirement', [])
            if class_req and player.character_class not in class_req:
                return {
                    'can_purchase': False,
                    'reason': f'职业不符，需要{"/".join(class_req)}'
                }
            
            # 检查库存
            stock = item_info.get('stock', -1)
            if stock == 0:
                return {'can_purchase': False, 'reason': '商品已售罄'}
            
            return {
                'can_purchase': True, 
                'cost': item_cost,
                'currency': currency_type,
                'item_info': item_info
            }
            
        except Exception as e:
            print(f"检查购买条件时出错: {e}")
            return {'can_purchase': False, 'reason': '系统错误'}
    
    def purchase_item(self, player, item_id: str, category_name: str, quantity: int = 1, shop_type: str = None) -> Dict[str, Any]:
        """
        购买商品
        
        Args:
            player: 玩家对象
            item_id: 商品ID
            category_name: 分类名称
            quantity: 购买数量
            shop_type: 商城类型，默认为当前商城
            
        Returns:
            Dict[str, Any]: 购买结果
        """
        if shop_type is None:
            shop_type = self.current_shop_type
            
        try:
            # 检查是否可以购买
            check_result = self.can_purchase_item(player, item_id, category_name, shop_type)
            if not check_result['can_purchase']:
                return {
                    'success': False,
                    'message': check_result['reason']
                }
            
            item_info = check_result['item_info']
            total_cost = check_result['cost'] * quantity
            currency_type = check_result['currency']
            
            # 扣除货币
            if not self._deduct_player_currency(player, currency_type, total_cost):
                return {
                    'success': False,
                    'message': f'{currency_type}扣除失败'
                }
            
            # 添加物品到玩家背包
            if not self._add_item_to_player(player, item_id, quantity):
                # 如果添加物品失败，退还货币
                self._add_player_currency(player, currency_type, total_cost)
                return {
                    'success': False,
                    'message': '背包空间不足'
                }
            
            return {
                'success': True,
                'message': f'成功购买{item_info["name"]} x{quantity}',
                'item_name': item_info['name'],
                'quantity': quantity,
                'cost': total_cost,
                'currency': currency_type
            }
            
        except Exception as e:
            print(f"购买商品时出错: {e}")
            return {
                'success': False,
                'message': '购买失败，请稍后重试'
            }
    
    def _get_player_currency(self, player, currency_type: str) -> int:
        """
        获取玩家指定类型的货币
        
        Args:
            player: 玩家对象
            currency_type: 货币类型
            
        Returns:
            int: 货币数量
        """
        if currency_type == 'points':
            if hasattr(player, 'daily_checkin') and 'points' in player.daily_checkin:
                return player.daily_checkin['points']
            return 0
        elif currency_type in ['gold', 'yuanbao', 'coin']:
            if hasattr(player, 'currencies') and currency_type in player.currencies:
                return player.currencies[currency_type]
            return 0
        return 0
    
    def _calculate_item_cost(self, player, item_info: Dict[str, Any], currency_type: str) -> int:
        """
        计算商品价格
        
        Args:
            player: 玩家对象
            item_info: 商品信息
            currency_type: 货币类型
            
        Returns:
            int: 实际价格
        """
        cost_key = f"{currency_type}_cost"
        base_cost = item_info.get(cost_key, item_info.get('cost', 0))
        
        # TODO: 可以在这里添加VIP折扣等逻辑
        
        return base_cost
    
    def _deduct_player_currency(self, player, currency_type: str, amount: int) -> bool:
        """
        扣除玩家货币
        
        Args:
            player: 玩家对象
            currency_type: 货币类型
            amount: 扣除数量
            
        Returns:
            bool: 是否成功
        """
        try:
            if currency_type == 'points':
                if hasattr(player, 'daily_checkin') and 'points' in player.daily_checkin:
                    if player.daily_checkin['points'] >= amount:
                        player.daily_checkin['points'] -= amount
                        return True
            elif currency_type in ['gold', 'yuanbao', 'coin']:
                if hasattr(player, 'currencies') and currency_type in player.currencies:
                    if player.currencies[currency_type] >= amount:
                        player.currencies[currency_type] -= amount
                        return True
            return False
        except Exception as e:
            print(f"扣除货币时出错: {e}")
            return False
    
    def _add_player_currency(self, player, currency_type: str, amount: int) -> bool:
        """
        添加玩家货币
        
        Args:
            player: 玩家对象
            currency_type: 货币类型
            amount: 添加数量
            
        Returns:
            bool: 是否成功
        """
        try:
            if currency_type == 'points':
                if hasattr(player, 'daily_checkin'):
                    if 'points' not in player.daily_checkin:
                        player.daily_checkin['points'] = 0
                    player.daily_checkin['points'] += amount
                    return True
            elif currency_type in ['gold', 'yuanbao', 'coin']:
                if hasattr(player, 'currencies'):
                    if currency_type not in player.currencies:
                        player.currencies[currency_type] = 0
                    player.currencies[currency_type] += amount
                    return True
            return False
        except Exception as e:
            print(f"添加货币时出错: {e}")
            return False
    
    def _add_item_to_player(self, player, item_id: str, quantity: int) -> bool:
        """
        添加物品到玩家背包
        
        Args:
            player: 玩家对象
            item_id: 物品ID
            quantity: 数量
            
        Returns:
            bool: 是否成功
        """
        try:
            # TODO: 实现实际的背包添加逻辑
            # 这里需要与背包管理器配合
            print(f"添加物品到背包: {item_id} x{quantity}")
            return True
        except Exception as e:
            print(f"添加物品到背包时出错: {e}")
            return False 