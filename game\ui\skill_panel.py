"""
技能面板界面
用于展示和管理角色技能
"""
import os
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import time

# 尝试导入PIL，如果失败则使用备用方案
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
    print("✅ PIL/Pillow 导入成功")
except ImportError as pil_error:
    print(f"⚠️ PIL/Pillow 导入失败: {pil_error}")
    print("💡 提示：如需图片功能，请安装 Pillow: pip install Pillow")
    PIL_AVAILABLE = False
    # 创建假的PIL类，避免引用错误
    class Image:
        @staticmethod
        def open(*args, **kwargs):
            raise ImportError("PIL not available")
        @staticmethod
        def new(*args, **kwargs):
            raise ImportError("PIL not available")
    
    class ImageTk:
        @staticmethod
        def PhotoImage(*args, **kwargs):
            raise ImportError("PIL not available")

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from game.models.skill_loader import skill_loader

class SkillPanel(tk.Toplevel):
    """技能面板界面类"""

    def __init__(self, parent=None, player=None):
        """
        初始化技能面板
        
        Args:
            parent: 父窗口
            player: 玩家对象，用于获取玩家数据
        """
        super().__init__(parent)
        # 窗口标题稍后根据PIL可用性确定
        self.geometry("700x600")
        self.resizable(False, False)
        
        # 设置窗口样式
        self.configure(bg="#2D2D2D")
        
        # 保存玩家对象引用
        self.player = player
        
        # 技能书页数量（模拟数据，实际应从玩家对象获取）
        self.book_pages = 50000
        
        # 设置玩家职业和等级（从存档获取）
        self.player_class = "法师"  # 临时设置，实际应该从self.player.class获取
        self.player_level = 1  # 默认等级
        
        # 图像处理模式设置
        # 如果PIL不可用，自动启用安全模式
        if not PIL_AVAILABLE:
            self.safe_mode = True
            self.title("技能面板（安全模式 - PIL不可用）")
            print("⚠️ PIL不可用，自动启用安全模式")
        else:
            self.safe_mode = False  # 默认图片模式：尝试加载技能图标
            self.title("技能面板")
        
        # 尝试从存档加载玩家数据
        self._load_player_data()
        
        # 职业对应的技能类别映射
        self.class_skill_mapping = {
            "战士": "战士技能",
            "法师": "魔法技能", 
            "道士": "道术技能"
        }
        
        # 通用技能（所有职业都可以学习）
        self.universal_skills = ["宠物技能"]
        
        # 获取当前职业对应的技能分类
        player_skill_category = self.class_skill_mapping.get(self.player_class)
        
        # 获取所有技能分类列表
        all_categories = skill_loader.get_skill_categories()
        
        # 如果启用职业限制，只显示当前职业的技能
        self.enable_class_restriction = True  # 设置为True启用职业限制，False显示所有技能
        
        if self.enable_class_restriction and player_skill_category:
            # 只显示当前职业的技能 + 通用技能
            self.available_categories = [player_skill_category] + [cat for cat in self.universal_skills if cat in all_categories]
            self.current_category = player_skill_category
        else:
            # 显示所有职业技能
            self.available_categories = all_categories
            self.current_category = all_categories[0] if all_categories else "战士技能"
        
        # 当前页码和每页显示数量 - 适应新的窗口大小
        self.current_page = 1
        self.items_per_page = 4  # 改为4个，适应600像素高度
        
        # 图标缓存 - 确保图像对象不被垃圾回收
        self.icon_cache = {}
        
        # 技能选择状态缓存
        self.skill_selections = {}
        
        # 保存所有图像引用，防止被垃圾回收
        self.image_references = []
        
        # 技能标签引用，确保图片对象不被回收
        self.skill_labels = {}
        
        # 创建UI组件
        self._create_widgets()
        
        # 加载当前分类的技能
        self._load_skills()
        
        # 设置窗口在屏幕中央
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"+{x}+{y}")
        
        # 绑定窗口关闭事件
        self.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # 让窗口获取焦点
        self.focus_set()
    
    def _get_skill_short_name(self, skill_name):
        """
        根据技能名称生成简化的显示文字
        
        Args:
            skill_name: 技能名称
            
        Returns:
            str: 简化的1-2个字符
        """
        if not skill_name:
            return "?"
        
        # 技能名称映射表
        skill_short_map = {
            "基本剑法": "基剑",
            "攻杀剑术": "攻杀", 
            "刺杀剑术": "刺杀",
            "半月弯刀": "半月",
            "烈火剑法": "烈火",
            "逐日剑法": "逐日",
            "火球术": "火球",
            "大火球": "大火",
            "爆裂火焰": "爆火",
            "地狱火": "地火",
            "雷电术": "雷电",
            "疾光电影": "疾电",
            "治愈术": "治愈",
            "群体治愈术": "群治",
            "精神力战法": "精神",
            "召唤骷髅": "骷髅",
            "隐身术": "隐身",
            "集体隐身术": "群隐",
            "神圣战甲术": "神甲",
            "困魔咒": "困魔",
            "毒凌波": "毒波",
            "灵魂火符": "魂符"
        }
        
        # 查找映射表
        if skill_name in skill_short_map:
            return skill_short_map[skill_name]
        
        # 如果没有映射，取前两个字符
        if len(skill_name) >= 2:
            return skill_name[:2]
        else:
            return skill_name[0] if skill_name else "?"
    
    def _load_player_data(self):
        """从存档加载玩家数据"""
        try:
            import json
            import os
            
            # 尝试加载自动存档
            save_path = "saves/autosave.json"
            if os.path.exists(save_path):
                with open(save_path, 'r', encoding='utf-8') as f:
                    save_data = json.load(f)
                
                # 获取玩家基本信息
                basic_info = save_data.get("basic_info", {})
                self.player_class = basic_info.get("character_class", "法师")
                self.player_level = basic_info.get("level", 1)
                
                print(f"已加载玩家数据: 职业={self.player_class}, 等级={self.player_level}")
            else:
                print("未找到存档文件，使用默认数据")
                
        except Exception as e:
            print(f"加载玩家数据失败: {e}")
            # 使用默认值
            self.player_class = "法师"
            self.player_level = 1
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建顶部标签栏
        self._create_tab_bar()
        
        # 创建技能列表区域
        self._create_skill_list_area()
        
        # 创建底部操作区域
        self._create_bottom_action_area()
    
    def _create_tab_bar(self):
        """创建顶部标签栏"""
        self.tab_buttons_frame = tk.Frame(self, bg="#2D2D2D")
        self.tab_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        tab_frame = self.tab_buttons_frame
        
        # 标签页样式
        tab_style = {
            "bg": "#2D2D2D", 
            "fg": "#CCCCCC", 
            "activebackground": "#3D3D3D", 
            "activeforeground": "#FFFFFF",
            "bd": 0,
            "padx": 15,
            "pady": 5,
            "font": ("微软雅黑", 10, "bold")
        }
        
        selected_tab_style = {
            "bg": "#4D4D4D", 
            "fg": "#FFFFFF",
            "activebackground": "#4D4D4D", 
            "activeforeground": "#FFFFFF",
            "bd": 0,
            "padx": 15,
            "pady": 5,
            "font": ("微软雅黑", 10, "bold")
        }
        
        # 初始化标签按钮字典
        self.tab_buttons = {}
        
        # 创建标签按钮
        self._create_tab_buttons(tab_frame)
    
    def _create_skill_list_area(self):
        """创建技能列表区域"""
        # 创建技能列表区域（去掉滚动条）
        self.skills_frame = tk.Frame(self, bg="#2D2D2D")
        self.skills_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 直接创建技能容器，不使用画布和滚动条
        self.skills_container = tk.Frame(self.skills_frame, bg="#2D2D2D")
        self.skills_container.pack(fill=tk.BOTH, expand=True)
    
    def _create_bottom_action_area(self):
        """创建底部操作区域"""
        bottom_frame = tk.Frame(self, bg="#2D2D2D", height=40)
        bottom_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        # 提示文本
        hint_label = tk.Label(
            bottom_frame,
            text="请选择要使用的技能",
            bg="#2D2D2D",
            fg="#CCCCCC",
            font=("微软雅黑", 9)
        )
        hint_label.pack(side=tk.LEFT, padx=5)
        
        # 资源文本
        self.resource_label = tk.Label(
            bottom_frame,
            text=f"拥有: 书页x{self.book_pages}",
            bg="#2D2D2D",
            fg="#CCCCCC",
            font=("微软雅黑", 9)
        )
        self.resource_label.pack(side=tk.LEFT, padx=20)
        
        # 翻页按钮和刷新按钮
        button_style = {
            "bg": "#3D3D3D",
            "fg": "#FFFFFF",
            "activebackground": "#4D4D4D",
            "activeforeground": "#FFFFFF",
            "bd": 1,
            "padx": 10,
            "pady": 2
        }
        
        # 上一页按钮
        self.prev_btn = tk.Button(
            bottom_frame, 
            text="<<", 
            command=self._prev_page,
            **button_style
        )
        self.prev_btn.pack(side=tk.RIGHT, padx=5)
        
        # 下一页按钮
        self.next_btn = tk.Button(
            bottom_frame, 
            text=">>", 
            command=self._next_page,
            **button_style
        )
        self.next_btn.pack(side=tk.RIGHT, padx=5)
        
        # 刷新按钮
        refresh_btn = tk.Button(
            bottom_frame, 
            text="手动刷新", 
            command=self._manual_refresh,
            **button_style
        )
        refresh_btn.pack(side=tk.RIGHT, padx=20)
        
        # 模式切换按钮
        if PIL_AVAILABLE:
            mode_text = "安全模式" if not self.safe_mode else "图片模式"
            mode_color = "#4CAF50" if not self.safe_mode else "#FF6666"
            mode_active_color = "#45A049" if not self.safe_mode else "#FF4444"
            mode_command = self._toggle_mode
        else:
            mode_text = "PIL不可用"
            mode_color = "#888888"
            mode_active_color = "#888888"
            mode_command = None  # 禁用按钮
        
        self.mode_btn = tk.Button(
            bottom_frame,
            text=mode_text,
            bg=mode_color,
            fg="#FFFFFF",
            activebackground=mode_active_color,
            activeforeground="#FFFFFF",
            font=("微软雅黑", 9, "bold"),
            bd=1,
            padx=15,
            command=mode_command,
            state=tk.NORMAL if PIL_AVAILABLE else tk.DISABLED
        )
        self.mode_btn.pack(side=tk.RIGHT, padx=10)
    
    def _process_skills_data(self, skills_dict):
        """
        处理技能数据，统一格式
        
        Args:
            skills_dict: 从skill_loader获取的原始技能数据
            
        Returns:
            list: 处理后的有效技能列表
        """
        skills = []
        if isinstance(skills_dict, dict):
            skills = list(skills_dict.values())
        elif isinstance(skills_dict, list):
            # 处理列表格式的技能数据
            for item in skills_dict:
                if isinstance(item, dict):
                    # 检查是否是嵌套结构（如魔法技能）
                    if any(isinstance(v, dict) for v in item.values()):
                        # 展开嵌套结构
                        for category_data in item.values():
                            if isinstance(category_data, dict):
                                skills.extend(category_data.values())
                    else:
                        # 直接添加技能数据
                        skills.append(item)
        else:
            skills = skills_dict if skills_dict else []
        
        # 过滤掉无效的技能数据
        valid_skills = []
        for skill in skills:
            if isinstance(skill, dict) and skill.get('name'):
                valid_skills.append(skill)
            else:
                print(f"跳过无效技能数据: {skill}")
        
        return valid_skills

    def _load_skills(self):
        """加载当前分类的技能"""
        # 清除现有技能条目
        for widget in self.skills_container.winfo_children():
            widget.destroy()
        
        # 清理技能标签引用
        self.skill_labels.clear()
        print("🧹 已清理技能标签引用")
        
        # 获取当前分类的技能列表
        skills_dict = skill_loader.get_skills_by_category(self.current_category)
        
        # 处理技能数据
        skills = self._process_skills_data(skills_dict)
        
        # 计算当前页的技能
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        page_skills = skills[start_index:end_index]
        
        # 创建技能条目
        for i, skill in enumerate(page_skills):
            self._create_skill_entry(i + 1 + start_index, skill)
        
        # 更新技能容器
        self.skills_container.update_idletasks()
        
        # 更新翻页按钮状态
        self.prev_btn["state"] = tk.NORMAL if self.current_page > 1 else tk.DISABLED
        self.next_btn["state"] = tk.NORMAL if end_index < len(skills) else tk.DISABLED
    
    def _load_skill_icon(self, icon_path, size=(40, 40)):
        """
        加载技能图标
        
        Args:
            icon_path: 图标路径
            size: 图标大小
            
        Returns:
            PhotoImage对象
        """
        # 如果PIL不可用，直接返回None
        if not PIL_AVAILABLE:
            print(f"⚠️ PIL不可用，跳过图标加载: {icon_path}")
            return None
        
        # 检查缓存
        cache_key = f"{icon_path}_{size[0]}x{size[1]}"
        if cache_key in self.icon_cache:
            print(f"🔄 使用缓存图标: {icon_path}")
            return self.icon_cache[cache_key]

        print(f"🔍 加载技能图标: {icon_path}")
        
        try:
            # 尝试加载图标
            if icon_path and os.path.exists(icon_path):
                print(f"✅ 文件存在: {icon_path}")
                
                # 使用 try-catch 包围 PIL 操作
                try:
                    image = Image.open(icon_path)
                    print(f"✅ PIL加载成功，原始尺寸: {image.size}")
                    image = image.resize(size, Image.LANCZOS)
                    print(f"✅ 调整尺寸成功: {size}")
                    
                    # 创建 PhotoImage 时更谨慎
                    photo = ImageTk.PhotoImage(image)
                    print(f"✅ PhotoImage转换成功")
                    
                    # 验证图片对象的有效性
                    try:
                        # 尝试获取图片的宽度和高度来验证对象有效性
                        width = photo.width()
                        height = photo.height()
                        print(f"✅ 图片对象验证成功: {width}x{height}")
                        
                        # 缓存图像并保存引用
                        self.icon_cache[cache_key] = photo
                        self.image_references.append(photo)  # 防止垃圾回收
                        return photo
                    except Exception as verify_error:
                        print(f"❌ 图片对象验证失败: {verify_error}")
                        return None
                    
                except Exception as pil_error:
                    print(f"❌ PIL处理失败: {icon_path} - {pil_error}")
                    # 如果PIL失败，尝试使用Tkinter原生方法
                    try:
                                                 # 对于PNG文件，Tkinter可能可以直接加载
                         if icon_path.lower().endswith('.png'):
                             photo = tk.PhotoImage(file=icon_path)
                             
                             # 验证Tkinter图片对象
                             try:
                                 width = photo.width()
                                 height = photo.height()
                                 print(f"✅ Tkinter图片验证成功: {width}x{height}")
                                 
                                 self.icon_cache[cache_key] = photo
                                 self.image_references.append(photo)
                                 print(f"✅ Tkinter加载PNG成功: {icon_path}")
                                 return photo
                             except Exception as tk_verify_error:
                                 print(f"❌ Tkinter图片对象验证失败: {tk_verify_error}")
                                 return None
                    except Exception as tk_error:
                        print(f"❌ Tkinter加载也失败: {icon_path} - {tk_error}")
                
            else:
                if icon_path:
                    print(f"❌ 技能图标文件不存在: {icon_path}")
                else:
                    print("❌ 技能图标路径为空")
        except Exception as e:
            print(f"❌ 加载技能图标出错: {icon_path} - {e}")
        
        print(f"🔧 创建默认图标: {icon_path}")
        
        # 创建默认图标（灰色方块背景加技能图标）
        try:
            # 创建一个PIL图片作为默认图标
            default_image = Image.new('RGBA', size, (80, 80, 80, 255))  # 灰色背景
            
            # 添加一个简单的技能图标形状
            from PIL import ImageDraw
            draw = ImageDraw.Draw(default_image)
            
            # 绘制一个圆形边框
            margin = 4
            draw.ellipse([margin, margin, size[0]-margin, size[1]-margin], 
                        outline=(200, 200, 200), width=2)
            
            # 在中心绘制一个问号或技能符号
            font_size = min(size) // 3
            try:
                # 尝试创建字体
                from PIL import ImageFont
                # 使用系统默认字体
                font = ImageFont.load_default()
            except:
                font = None
            
            # 绘制问号
            text = "?"
            if font:
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
            else:
                text_width, text_height = 10, 12  # 估算大小
            
            text_x = (size[0] - text_width) // 2
            text_y = (size[1] - text_height) // 2
            
            draw.text((text_x, text_y), text, fill=(220, 220, 220), font=font)
            
            photo = ImageTk.PhotoImage(default_image)
            
            # 验证默认图标的有效性
            try:
                width = photo.width()
                height = photo.height()
                print(f"✅ 默认图标验证成功: {width}x{height}")
                
                # 缓存图像并保存引用
                self.icon_cache[cache_key] = photo
                self.image_references.append(photo)  # 防止垃圾回收
                print(f"✅ 默认图标创建成功")
                return photo
            except Exception as default_verify_error:
                print(f"❌ 默认图标验证失败: {default_verify_error}")
                return None
            
        except Exception as e:
            print(f"❌ 创建默认图标失败: {e}")
            # 如果连默认图标都创建失败，使用最简单的空图标
            try:
                default_icon = tk.PhotoImage(width=size[0], height=size[1])
                
                # 验证空图标
                try:
                    width = default_icon.width()
                    height = default_icon.height()
                    print(f"✅ 空图标验证成功: {width}x{height}")
                    
                    self.icon_cache[cache_key] = default_icon
                    self.image_references.append(default_icon)  # 防止垃圾回收
                    return default_icon
                except Exception as empty_verify_error:
                    print(f"❌ 空图标验证失败: {empty_verify_error}")
                    return None
                    
            except Exception as fallback_e:
                print(f"❌ 创建空图标也失败: {fallback_e}")
                # 最后的备选方案：返回None，在使用时处理
                return None
    
    def _create_skill_entry(self, index, skill):
        """
        创建单个技能条目
        
        Args:
            index: 技能序号
            skill: 技能数据
        """
        # 创建技能条目框架
        entry_frame = tk.Frame(self.skills_container, bg="#333333", bd=1, relief=tk.SOLID, height=120)
        entry_frame.pack(fill=tk.X, padx=10, pady=5)
        entry_frame.pack_propagate(False)  # 固定高度
        
        # 左侧区域 - 技能图标和按钮
        left_frame = tk.Frame(entry_frame, bg="#333333", width=100)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        left_frame.pack_propagate(False)  # 固定宽度，防止被内容撑开
        
        # 技能图标
        icon_path = skill.get("icon_path", "")
        skill_name = skill.get('name', '未知技能')
        
        # 在安全模式下跳过图片加载
        if self.safe_mode:
            skill_icon = None
            print(f"🔒 安全模式：跳过图片加载 - {skill_name}")
        else:
            skill_icon = self._load_skill_icon(icon_path)
        
        # 创建图标标签，如果图标加载失败则显示文字
        try:
            if skill_icon is not None:
                icon_label = tk.Label(
                    left_frame,
                    image=skill_icon,
                    bg="#333333",
                    relief=tk.SUNKEN,
                    bd=1
                )
                # 重要：将图片对象绑定到标签对象，防止垃圾回收
                icon_label.image = skill_icon
                # 同时保存到技能标签字典中
                self.skill_labels[skill_name] = {
                    'icon_label': icon_label,
                    'icon_image': skill_icon
                }
                print(f"✅ 技能图标标签创建成功: {skill_name}")
            else:
                # 图标加载失败或安全模式时显示占位文字
                # 根据技能名称生成简化图标
                skill_short = self._get_skill_short_name(skill_name)
                
                if self.safe_mode:
                    icon_text = skill_short
                    icon_color = "#4CAF50"  # 绿色表示安全模式
                    status_msg = f"🔒 安全模式图标: {skill_name}"
                else:
                    icon_text = skill_short  # 图片模式失败时也显示技能缩写
                    icon_color = "#FFD700"  # 金色表示图片模式的文字回退
                    status_msg = f"🖼️ 图标加载失败，使用文字: {skill_name}"
                
                icon_label = tk.Label(
                    left_frame,
                    text=icon_text,
                    bg="#333333",
                    fg=icon_color,
                    font=("微软雅黑", 10, "bold"),
                    width=6,
                    height=3,
                    bd=1,
                    relief=tk.RAISED if self.safe_mode else tk.SUNKEN
                )
                self.skill_labels[skill_name] = {
                    'icon_label': icon_label,
                    'icon_image': None
                }
                print(status_msg)
        except Exception as label_error:
            print(f"❌ 创建技能图标标签失败: {skill_name} - {label_error}")
            # 最后的备用方案：创建技能缩写标签
            skill_short = self._get_skill_short_name(skill_name)
            icon_label = tk.Label(
                left_frame,
                text=skill_short,
                bg="#333333",
                fg="#ff8c00",  # 橙色表示错误恢复
                font=("微软雅黑", 10, "bold"),
                width=6,
                height=3,
                bd=1,
                relief=tk.RAISED
            )
            self.skill_labels[skill_name] = {
                'icon_label': icon_label,
                'icon_image': None
            }
            print(f"🔧 使用备用技能缩写标签: {skill_name} -> {skill_short}")
        icon_label.pack(pady=5)
        
        # 如果技能已学习，显示自动释放开关
        if skill.get("learned", False):
            # 获取当前开关状态
            skill_name = skill.get("name", "未知技能")
            switch_var = tk.BooleanVar()
            current_state = self._get_auto_cast_state(skill_name)
            switch_var.set(current_state)
            
            # 自动释放开关
            auto_switch = tk.Checkbutton(
                left_frame,
                text="自动释放",
                variable=switch_var,
                bg="#333333",
                fg="#00FF00" if current_state else "#FF6666",
                selectcolor="#333333",
                activebackground="#333333",
                activeforeground="#FFFFFF",
                font=("微软雅黑", 8, "bold"),
                command=lambda name=skill_name, var=switch_var: self._toggle_auto_cast(name, var)
            )
            auto_switch.pack(pady=5)
            
            # 状态标签
            status_text = "已启用" if current_state else "已禁用"
            status_color = "#00FF00" if current_state else "#FF6666"
            status_label = tk.Label(
                left_frame,
                text=status_text,
                bg="#333333",
                fg=status_color,
                font=("微软雅黑", 7)
            )
            status_label.pack()
            
        else:
            # 技能未学习，显示学习和升级按钮
            current_level = skill.get("level", 0)
            max_level = skill.get("max_level", 3)
            
            if current_level < max_level:
                if current_level == 0:
                    # 未学习，显示学习按钮
                    learn_btn = tk.Button(
                        left_frame,
                        text="学习",
                        bg="#32CD32",
                        fg="#FFFFFF", 
                        activebackground="#228B22",
                        activeforeground="#FFFFFF",
                        bd=1,
                        width=8,
                        height=1,
                        font=("微软雅黑", 8, "bold"),
                        command=lambda s=skill: self._learn_skill(s)
                    )
                    learn_btn.pack(anchor=tk.S, pady=2)
                else:
                    # 已学习但未满级，显示升级按钮
                    upgrade_btn = tk.Button(
                        left_frame,
                        text="升级",
                        bg="#4169E1",
                        fg="#FFFFFF",
                        activebackground="#1E90FF",
                        activeforeground="#FFFFFF",
                        bd=1,
                        width=8,
                        height=1,
                        font=("微软雅黑", 8, "bold"),
                        command=lambda s=skill: self._upgrade_skill(s)
                    )
                    upgrade_btn.pack(anchor=tk.S, pady=2)
            else:
                # 满级标签
                max_level_label = tk.Label(
                    left_frame,
                    text="满级",
                    bg="#333333",
                    fg="#FFD700",
                    font=("微软雅黑", 8, "bold"),
                    relief=tk.SUNKEN,
                    bd=1
                )
                max_level_label.pack(anchor=tk.S, pady=2)
        
        # 中间区域 - 技能信息（扩大宽度，去掉右侧区域后占用更多空间）
        mid_frame = tk.Frame(entry_frame, bg="#333333", width=580)
        mid_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        mid_frame.pack_propagate(False)  # 固定宽度，防止被内容撑开
        
        # 技能名称和等级
        name_frame = tk.Frame(mid_frame, bg="#333333")
        name_frame.pack(fill=tk.X)
        
        skill_name = tk.Label(
            name_frame,
            text=skill.get("name", "未知技能"),
            bg="#333333",
            fg="#FFFFFF",
            font=("微软雅黑", 11, "bold"),
            anchor=tk.W
        )
        skill_name.pack(side=tk.LEFT)
        
        skill_level = tk.Label(
            name_frame,
            text=f"Lv.{skill.get('level', 0)}",
            bg="#333333",
            fg="#CCCCCC",
            font=("微软雅黑", 10),
            padx=10
        )
        skill_level.pack(side=tk.LEFT)
        
        # 书页和附魔信息
        resource_frame = tk.Frame(mid_frame, bg="#333333")
        resource_frame.pack(fill=tk.X, pady=2)
        
        book_pages = tk.Label(
            resource_frame,
            text=f"需要书页:{skill.get('book_pages', 0)}",
            bg="#333333",
            fg="#FFD700",
            font=("微软雅黑", 9, "bold"),
            anchor=tk.W
        )
        book_pages.pack(side=tk.LEFT)
        
        enchant = tk.Label(
            resource_frame,
            text=f"附魔:{skill.get('enchant_percent', 0)}%",
            bg="#333333",
            fg="#DDA0DD",
            font=("微软雅黑", 9),
            padx=15
        )
        enchant.pack(side=tk.LEFT)
        
        # 技能描述区域
        desc_frame = tk.Frame(mid_frame, bg="#333333")
        desc_frame.pack(fill=tk.X, pady=3)
        
        # 技能描述中的数值用绿色和蓝色突出显示
        desc_text = skill.get("description", "无描述")
        # 保持原有的换行符，让文本能够正确换行
        desc_text = desc_text.replace('\\n', '\n')  # 将转义的换行符转换为真正的换行符
        
        desc_label = tk.Label(
            desc_frame,
            text=desc_text,
            bg="#333333",
            fg="#CCCCCC",
            font=("微软雅黑", 9),
            justify=tk.LEFT,
            anchor=tk.NW,
            wraplength=550,  # 调整换行宽度适应580像素中间区域
            height=3  # 增加高度以容纳更多行
        )
        desc_label.pack(anchor=tk.W, fill=tk.X)
        
        # 技能属性信息 - 分两行显示
        attr_frame1 = tk.Frame(mid_frame, bg="#333333")
        attr_frame1.pack(fill=tk.X, pady=2)
        
        attr_frame2 = tk.Frame(mid_frame, bg="#333333")
        attr_frame2.pack(fill=tk.X)
        
        # 第一行：耗蓝和冷却
        mana_cost = skill.get('mana_cost') or skill.get('mp_cost', 0)
        mana_label = tk.Label(
            attr_frame1,
            text=f"耗蓝:{mana_cost}",
            bg="#333333",
            fg="#87CEEB",
            font=("微软雅黑", 8),
            width=12,
            anchor=tk.W
        )
        mana_label.pack(side=tk.LEFT)
        
        cooldown_label = tk.Label(
            attr_frame1,
            text=f"冷却:{skill.get('cooldown', 0)}秒",
            bg="#333333",
            fg="#87CEEB",
            font=("微软雅黑", 8),
            width=12,
            anchor=tk.W
        )
        cooldown_label.pack(side=tk.LEFT)
        
        # 第二行：类型和目标
        type_label = tk.Label(
            attr_frame2,
            text=f"类型:{skill.get('type', '未知')}",
            bg="#333333",
            fg="#98FB98",
            font=("微软雅黑", 8),
            width=12,
            anchor=tk.W
        )
        type_label.pack(side=tk.LEFT)
        
        target_label = tk.Label(
            attr_frame2,
            text=f"目标:{skill.get('targets', 0)}个",
            bg="#333333",
            fg="#98FB98",
            font=("微软雅黑", 8),
            width=12,
            anchor=tk.W
        )
        target_label.pack(side=tk.LEFT)
    
    def _on_tab_click(self, category):
        """
        标签页点击事件处理
        
        Args:
            category: 点击的技能类别
        """
        if category != self.current_category:
            # 更新所有标签样式
            tab_style = {
                "bg": "#2D2D2D", 
                "fg": "#CCCCCC", 
                "activebackground": "#3D3D3D", 
                "activeforeground": "#FFFFFF"
            }
            
            selected_tab_style = {
                "bg": "#4D4D4D", 
                "fg": "#FFFFFF",
                "activebackground": "#4D4D4D", 
                "activeforeground": "#FFFFFF"
            }
            
            for cat, btn in self.tab_buttons.items():
                if cat == category:
                    btn.configure(**selected_tab_style)
                else:
                    btn.configure(**tab_style)
            
            # 更新当前分类和页码
            self.current_category = category
            self.current_page = 1
            
            # 加载新分类的技能
            self._load_skills()
    
    def _prev_page(self):
        """显示上一页技能"""
        if self.current_page > 1:
            self.current_page -= 1
            self._load_skills()
    
    def _next_page(self):
        """显示下一页技能"""
        skills_dict = skill_loader.get_skills_by_category(self.current_category)
        
        # 处理技能数据
        skills = self._process_skills_data(skills_dict)
        
        max_page = (len(skills) + self.items_per_page - 1) // self.items_per_page
        
        if self.current_page < max_page:
            self.current_page += 1
            self._load_skills()
    
    def _refresh(self):
        """刷新技能面板"""
        print("刷新技能面板...")
        
        # 清理旧的界面组件
        for widget in self.skills_container.winfo_children():
            widget.destroy()
        
        # 清理图像引用（保留缓存，以便重用）
        self.image_references.clear()
        
        # 清理技能标签引用
        self.skill_labels.clear()
        
        # 重新加载技能
        self._load_skills()
        print("技能面板刷新完成")
    
    def _manual_refresh(self):
        """手动刷新技能列表"""
        skill_loader.load_skills()
        self._load_skills()
        
        # 更新资源标签
        self.resource_label.configure(text=f"拥有: 书页x{self.book_pages}")
        
        messagebox.showinfo("刷新完成", "技能列表已更新")
    
    def _toggle_mode(self):
        """切换图像模式和安全模式"""
        # 如果PIL不可用，不允许切换
        if not PIL_AVAILABLE:
            messagebox.showwarning("功能不可用", 
                                 "PIL/Pillow 库不可用\n\n"
                                 "请安装 Pillow 以使用图片模式：\n"
                                 "pip install Pillow")
            return
        
        self.safe_mode = not self.safe_mode
        
        # 更新窗口标题
        if self.safe_mode:
            self.title("技能面板（安全模式）")
            mode_text = "图片模式"
            mode_color = "#FF6666"
            mode_active_color = "#FF4444"
            print("🔒 切换到安全模式（纯文字图标）")
        else:
            self.title("技能面板")
            mode_text = "安全模式"
            mode_color = "#4CAF50"
            mode_active_color = "#45A049"
            print("🖼️ 切换到图片模式（加载图标）")
        
        # 更新按钮显示
        self.mode_btn.configure(
            text=mode_text,
            bg=mode_color,
            activebackground=mode_active_color
        )
        
        # 清理现有图像缓存和引用
        try:
            for skill_name, label_info in self.skill_labels.items():
                if label_info.get('icon_image'):
                    label = label_info.get('icon_label')
                    if label and hasattr(label, 'image'):
                        label.image = None
            
            self.skill_labels.clear()
            self.icon_cache.clear()
            self.image_references.clear()
            print("🧹 已清理图像缓存以准备模式切换")
        except Exception as e:
            print(f"清理图像缓存时出错: {e}")
        
        # 重新加载技能以应用新模式
        self._load_skills()
        
        # 显示模式切换提示
        if self.safe_mode:
            messagebox.showinfo("模式切换", 
                              "已切换到安全模式\n\n"
                              "• 使用文字图标，避免图片错误\n"
                              "• 更稳定，不会出现pyimage错误\n"
                              "• 图标显示为技能名称缩写")
        else:
            messagebox.showinfo("模式切换", 
                              "已切换到图片模式\n\n"
                              "• 尝试加载技能图标图片\n"
                              "• 如果出现错误请切回安全模式\n"
                              "• 图标加载失败时显示占位符")
    
    def get_selected_skills(self):
        """获取当前选中的技能列表"""
        selected_skills = []
        for skill_id, var in self.skill_selections.items():
            if var.get():  # 如果选中
                selected_skills.append(skill_id)
        return selected_skills
    
    def clear_skill_selections(self):
        """清除所有技能选择状态"""
        for var in self.skill_selections.values():
            var.set(False)
    
    def _create_tab_buttons(self, tab_frame=None):
        """创建标签按钮（可用于初始创建和重新创建）"""
        if tab_frame is None:
            tab_frame = self.tab_buttons_frame
        
        # 清理现有按钮
        for widget in self.tab_buttons.values():
            widget.destroy()
        self.tab_buttons.clear()
        
        # 标签页样式
        tab_style = {
            "bg": "#2D2D2D", 
            "fg": "#CCCCCC", 
            "activebackground": "#3D3D3D", 
            "activeforeground": "#FFFFFF",
            "bd": 0,
            "padx": 15,
            "pady": 5,
            "font": ("微软雅黑", 10, "bold")
        }
        
        selected_tab_style = {
            "bg": "#4D4D4D", 
            "fg": "#FFFFFF",
            "activebackground": "#4D4D4D", 
            "activeforeground": "#FFFFFF",
            "bd": 0,
            "padx": 15,
            "pady": 5,
            "font": ("微软雅黑", 10, "bold")
        }
        
        for i, category in enumerate(self.available_categories):
            btn = tk.Button(
                tab_frame, 
                text=category,
                command=lambda cat=category: self._on_tab_click(cat),
                **tab_style
            )
            btn.pack(side=tk.LEFT)
            self.tab_buttons[category] = btn
        
        # 设置当前选中的标签样式
        if self.current_category in self.tab_buttons:
            self.tab_buttons[self.current_category].configure(**selected_tab_style)
    
    def _learn_skill(self, skill):
        """
        学习技能
        
        Args:
            skill: 要学习的技能数据
        """
        skill_name = skill.get("name", "未知技能")
        required_pages = skill.get("book_pages", 50)
        level_requirements = skill.get("level_requirements", [])
        required_level = level_requirements[0] if level_requirements else 1
        
        # 检查职业限制
        if self.enable_class_restriction:
            allowed_category = self.class_skill_mapping.get(self.player_class)
            # 通用技能任何职业都可以学习
            if self.current_category not in self.universal_skills and self.current_category != allowed_category:
                tk.messagebox.showwarning("学习失败", f"{self.player_class} 职业无法学习 {self.current_category} 的技能")
                return
        
        # 🔧 修改：所有技能都需要技能书，不再有例外
        # 检查是否有对应的技能书
        skill_book_available = False
        if self.player and hasattr(self.player, 'inventory_manager'):
            skill_book_available = self.player.inventory_manager.has_item(skill_name, 1)
        
        if not skill_book_available:
            tk.messagebox.showwarning("学习失败", f"学习 {skill_name} 需要对应的技能书，请先获得技能书")
            return
        
        # 检查书页数量
        if self.book_pages < required_pages:
            tk.messagebox.showwarning("学习失败", f"学习 {skill_name} 需要 {required_pages} 书页，当前只有 {self.book_pages} 书页")
            return
        
        # 检查角色等级
        if self.player and hasattr(self.player, 'level'):
            player_level = self.player.level
        else:
            # 从存档中获取玩家等级
            player_level = self.player_level
            
        if player_level < required_level:
            tk.messagebox.showwarning("学习失败", f"学习 {skill_name} 需要角色等级 {required_level}，当前等级 {player_level}")
            return
        
        # 扣除资源
        self.book_pages -= required_pages
        
        # 🔧 修改：所有技能都需要消耗技能书
        if self.player and hasattr(self.player, 'inventory_manager'):
            book_consumed = self.player.inventory_manager.remove_item(skill_name, 1)
            if not book_consumed:
                # 如果消耗技能书失败，返还书页
                self.book_pages += required_pages
                tk.messagebox.showerror("学习失败", f"消耗技能书失败：{skill_name}")
                return
        
        # 学习技能
        category = self.current_category
        skill_id = skill.get("id")
        if skill_id:
            success = skill_loader.learn_skill(category, skill_id)
            if success:
                message = f"成功学习 {skill_name}！\n已消耗技能书：{skill_name}"
                tk.messagebox.showinfo("学习成功", message)
                # 更新界面
                self._refresh()
            else:
                # 如果学习失败，返还书页和技能书
                self.book_pages += required_pages
                # 返还技能书（创建一个基础技能书对象）
                from game.data.inventory import SkillBook
                skill_book = SkillBook(
                    id=f"skillbook_{skill_name}",
                    name=skill_name,
                    description=f"学习{skill_name}技能的书籍",
                    skill_id=skill_id
                )
                self.player.inventory_manager.add_item(skill_book, 1)
                tk.messagebox.showerror("学习失败", f"学习 {skill_name} 失败")

    def _upgrade_skill(self, skill):
        """
        升级技能
        
        Args:
            skill: 要升级的技能数据
        """
        skill_name = skill.get("name", "未知技能")
        current_level = skill.get("level", 0)
        max_level = skill.get("max_level", 3)
        required_pages = skill.get("book_pages", 50)
        level_requirements = skill.get("level_requirements", [])
        
        # 检查是否已达到最大等级
        if current_level >= max_level:
            tk.messagebox.showinfo("升级提示", f"{skill_name} 已达到最高等级")
            return
        
        # 🔧 修改：所有技能都需要技能书，不再有例外
        # 检查是否有对应的技能书
        skill_book_available = False
        if self.player and hasattr(self.player, 'inventory_manager'):
            skill_book_available = self.player.inventory_manager.has_item(skill_name, 1)
        
        if not skill_book_available:
            tk.messagebox.showwarning("升级失败", f"升级 {skill_name} 需要对应的技能书，请先获得技能书")
            return
        
        # 检查书页数量
        if self.book_pages < required_pages:
            tk.messagebox.showwarning("升级失败", f"升级 {skill_name} 需要 {required_pages} 书页，当前只有 {self.book_pages} 书页")
            return
        
        # 检查角色等级要求
        if current_level < len(level_requirements):
            required_level = level_requirements[current_level]  # current_level对应下一级的要求
            if self.player and hasattr(self.player, 'level'):
                player_level = self.player.level
            else:
                # 从存档中获取玩家等级
                player_level = self.player_level
            
            if player_level < required_level:
                tk.messagebox.showwarning("升级失败", f"升级 {skill_name} 到 {current_level + 1} 级需要角色等级 {required_level}，当前等级 {player_level}")
                return
        
        # 扣除资源
        self.book_pages -= required_pages
        
        # 🔧 修改：所有技能都需要消耗技能书
        if self.player and hasattr(self.player, 'inventory_manager'):
            book_consumed = self.player.inventory_manager.remove_item(skill_name, 1)
            if not book_consumed:
                # 如果消耗技能书失败，返还书页
                self.book_pages += required_pages
                tk.messagebox.showerror("升级失败", f"消耗技能书失败：{skill_name}")
                return
        
        # 升级技能
        category = self.current_category
        skill_id = skill.get("id")
        if skill_id:
            success = skill_loader.upgrade_skill(category, skill_id)
            if success:
                message = f"{skill_name} 升级到 {current_level + 1} 级！\n已消耗技能书：{skill_name}"
                tk.messagebox.showinfo("升级成功", message)
                # 更新UI
                self._refresh()
            else:
                # 如果升级失败，返还书页和技能书
                self.book_pages += required_pages
                # 返还技能书
                from game.data.inventory import SkillBook
                skill_book = SkillBook(
                    id=f"skillbook_{skill_name}",
                    name=skill_name,
                    description=f"学习{skill_name}技能的书籍",
                    skill_id=skill_id
                )
                self.player.inventory_manager.add_item(skill_book, 1)
                tk.messagebox.showerror("升级失败", f"升级 {skill_name} 失败")
    
    def _on_close(self):
        """窗口关闭时的清理工作"""
        print("技能面板关闭")
        
        # 清理图像缓存和引用
        try:
            # 首先清理技能标签中的图片引用
            for skill_name, label_info in self.skill_labels.items():
                if label_info.get('icon_image'):
                    # 移除标签的图片引用
                    label = label_info.get('icon_label')
                    if label and hasattr(label, 'image'):
                        label.image = None
            
            self.skill_labels.clear()
            self.icon_cache.clear()
            self.image_references.clear()
            print("已清理所有图像缓存和引用")
        except Exception as e:
            print(f"清理图像资源时出错: {e}")
        
        # 销毁窗口
        self.destroy()

    def _get_auto_cast_state(self, skill_name):
        """获取技能的自动释放状态"""
        # 从存档中获取自动释放状态
        try:
            import json
            import os
            config_path = os.path.join("saves", "auto_cast_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config.get(skill_name, False)
        except Exception as e:
            print(f"读取自动释放配置失败: {e}")
        return False
    
    def _toggle_auto_cast(self, skill_name, switch_var):
        """切换技能的自动释放状态"""
        enabled = switch_var.get()
        
        # 保存状态到配置文件
        self._save_auto_cast_state(skill_name, enabled)
        
        # 通知技能栏系统更新
        self._notify_skill_hotbar_update(skill_name, enabled)
        
        # 更新UI颜色
        self._update_switch_color(skill_name, enabled)
        
        status = "启用" if enabled else "禁用"
        print(f"技能 {skill_name} 自动释放已{status}")
        
    def _save_auto_cast_state(self, skill_name, enabled):
        """保存自动释放状态到配置文件"""
        try:
            import json
            import os
            
            config_path = os.path.join("saves", "auto_cast_config.json")
            config = {}
            
            # 读取现有配置
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            # 更新配置
            config[skill_name] = enabled
            
            # 确保目录存在
            os.makedirs("saves", exist_ok=True)
            
            # 保存配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存自动释放配置失败: {e}")
    
    def _notify_skill_hotbar_update(self, skill_name, enabled):
        """通知技能栏系统更新"""
        # 这里可以通过事件系统或者直接调用技能栏的更新方法
        # 暂时通过文件系统进行通信
        try:
            import json
            import os
            
            hotbar_update_path = os.path.join("saves", "hotbar_update.json")
            update_info = {
                "skill_name": skill_name,
                "enabled": enabled,
                "timestamp": time.time()
            }
            
            os.makedirs("saves", exist_ok=True)
            with open(hotbar_update_path, 'w', encoding='utf-8') as f:
                json.dump(update_info, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"通知技能栏更新失败: {e}")
    
    def _update_switch_color(self, skill_name, enabled):
        """更新开关的颜色显示"""
        # 刷新界面以更新颜色
        self._refresh()


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建技能面板
    skill_panel = SkillPanel(root)
    
    # 启动主循环
    root.mainloop() 