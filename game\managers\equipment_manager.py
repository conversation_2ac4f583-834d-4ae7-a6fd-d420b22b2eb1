# -*- coding: utf-8 -*-
"""
装备管理器
负责管理玩家的装备系统，包括装备穿戴、卸下、属性计算等功能
"""

from typing import Dict, Optional, Any, List
import json
import os
import traceback
from game.data.equipment_converter import EquipmentConverter
from game.core.resource_manager import get_game_data_path


class EquipmentLoader:
    """装备加载器 - 负责加载和管理装备数据"""
    
    def __init__(self):
        self.equipment_data = {}
        self.set_data = {}
        self._load_equipment_data()
        self._load_set_data()
    
    def _load_equipment_data(self):
        """加载装备数据"""
        try:
            equipment_path = get_game_data_path("equipment_data.json")
            if os.path.exists(equipment_path):
                with open(equipment_path, 'r', encoding='utf-8') as f:
                    self.equipment_data = json.load(f)
                print(f"✅ 加载了 {len(self.equipment_data)} 件装备数据")
        except Exception as e:
            print(f"❌ 加载装备数据失败: {e}")
    
    def _load_set_data(self):
        """加载套装数据"""
        try:
            set_path = get_game_data_path("equipment_sets.json")
            if os.path.exists(set_path):
                with open(set_path, 'r', encoding='utf-8') as f:
                    self.set_data = json.load(f)
                print(f"✅ 加载了 {len(self.set_data)} 个套装数据")
        except Exception as e:
            print(f"❌ 加载套装数据失败: {e}")
            # 提供默认套装数据
            self.set_data = {
                "新手套装": {
                    "description": "适合新手冒险者的基础套装",
                    "bonuses": {
                        2: {"防御下限": 2, "防御上限": 4, "生命值": 20},
                        4: {"防御下限": 5, "防御上限": 10, "生命值": 50, "攻击下限": 2, "攻击上限": 5}
                    }
                },
                "战士套装": {
                    "description": "为勇猛战士打造的精良套装",
                    "bonuses": {
                        2: {"攻击下限": 5, "攻击上限": 10, "准确": 5},
                        3: {"攻击下限": 10, "攻击上限": 20, "准确": 10, "暴击率": 0.05},
                        5: {"攻击下限": 20, "攻击上限": 40, "准确": 20, "暴击率": 0.1, "生命值": 100}
                    }
                }
            }
    
    def get_suitable_equipment(self, player_level: int, player_class: str, slot: str = None) -> List[Dict[str, Any]]:
        """获取适合玩家的装备列表"""
        suitable_equipment = []
        
        for equip_id, equipment in self.equipment_data.items():
            # 检查等级要求
            if equipment.get('required_level', 1) > player_level:
                continue
            
            # 检查槽位
            if slot and equipment.get('slot') != slot:
                continue
            
            # 检查职业要求
            required_class = equipment.get('required_class')
            if required_class:
                if isinstance(required_class, str) and required_class != player_class:
                    continue
                elif isinstance(required_class, list) and player_class not in required_class:
                    continue
            
            suitable_equipment.append(equipment)
        
        # 按等级要求排序
        suitable_equipment.sort(key=lambda x: x.get('required_level', 1), reverse=True)
        
        return suitable_equipment
    
    def get_set_config(self) -> Dict[str, Any]:
        """获取套装配置"""
        return self.set_data.copy()


class EquipmentManager:
    """
    装备管理器类
    负责管理玩家的装备系统
    """
    
    # 装备槽位定义
    EQUIPMENT_SLOTS = {
        "武器": "weapon",
        "头盔": "helmet", 
        "胸甲": "armor",
        "护腿": "leggings",
        "靴子": "boots",
        "护符": "amulet",
        "戒指1": "ring1",
        "戒指2": "ring2",
        "手镯1": "bracelet1",
        "手镯2": "bracelet2"
    }
    
    def __init__(self):
        """
        初始化装备管理器
        """
        # 当前装备的物品
        self.equipped_items: Dict[str, Optional[Dict[str, Any]]] = {
            slot: None for slot in self.EQUIPMENT_SLOTS.keys()
        }
        
        # 🔧 新增：确保兼容性，检查并添加缺失的槽位
        self._ensure_slot_compatibility()
        
        # 🔧 新增：装备变化标记和版本号
        self.equipment_changed = False
        self.equipment_version = 0
        
        # 装备数据转换器
        self.equipment_converter = EquipmentConverter()
        
        # 装备加载器
        self.equipment_loader = EquipmentLoader()
        
        # 装备提供的属性加成
        self.equipment_stats = {
            "攻击下限": 0,
            "攻击上限": 0,
            "防御下限": 0,
            "防御上限": 0,
            "魔法攻击下限": 0,
            "魔法攻击上限": 0,
            "道术攻击下限": 0,
            "道术攻击上限": 0,
            "生命值": 0,
            "魔法值": 0,
            "魔抗": 0,
            "攻速": 0.0,
            "暴击率": 0.0,
            "准确": 0,
            "敏捷": 0,
            "幸运": 0,
            "掉落率加成": 0.0,
            "经验加成": 0.0,
            "金币加成": 0.0
        }
        
        # 性能优化：缓存机制
        self._stats_cache = None
        self._cache_version = 0
        self._set_bonus_cache = None
        self._set_bonus_cache_version = 0
    
    def _ensure_slot_compatibility(self):
        """
        确保装备槽位兼容性
        用于处理版本升级时新增槽位的情况
        """
        # 检查是否有缺失的槽位
        missing_slots = []
        for slot_name in self.EQUIPMENT_SLOTS.keys():
            if slot_name not in self.equipped_items:
                missing_slots.append(slot_name)
        
        # 添加缺失的槽位
        for slot_name in missing_slots:
            self.equipped_items[slot_name] = None
            print(f"🔧 添加缺失的装备槽位: {slot_name}")
        
        # 🔧 特殊处理：如果存在旧的"戒指"槽位，迁移到"戒指1"
        if "戒指" in self.equipped_items and "戒指1" in self.equipped_items:
            old_ring = self.equipped_items.get("戒指")
            if old_ring and not self.equipped_items.get("戒指1"):
                self.equipped_items["戒指1"] = old_ring
                print(f"🔄 迁移旧戒指到戒指1槽位: {old_ring.get('name', '未知戒指')}")
            # 移除旧的戒指槽位
            del self.equipped_items["戒指"]
            print(f"🗑️ 移除旧的戒指槽位")
    
    def equip_item(self, item: Dict[str, Any], player, specific_slot: str = None) -> Optional[Dict[str, Any]]:
        """
        装备物品
        
        参数:
            item: 要装备的物品（可以是简单数据或完整的装备配置）
            player: 玩家对象
            specific_slot: 指定的槽位（用于戒指等可选择槽位的装备）
            
        返回:
            Optional[Dict[str, Any]]: 被替换的装备，如果没有被替换的装备则返回None，装备失败返回False
        """
        try:
            # 参数验证
            if not item:
                print("❌ 装备失败：物品为空")
                return False
                
            if not player:
                print("❌ 装备失败：玩家对象无效")
                return False
            
            # 如果传入的是装备ID，则从配置中获取完整数据
            if isinstance(item, (str, int)):
                equipment_data = self.equipment_loader.equipment_data.get(str(item))
                if equipment_data:
                    item = equipment_data.copy()
                else:
                    print(f"❌ 装备ID {item} 未找到对应的装备数据")
                    return False
            elif isinstance(item, dict):
                # 如果是字典，使用转换器处理属性格式
                # 注意：这里假设装备物品已经包含了必要的字段
                if 'slot' not in item:
                    # 如果缺少slot字段，尝试基于名称推断
                    item_name = item.get('name', '')
                    inferred_slot = self._infer_equipment_slot(item_name)
                    if inferred_slot:
                        item['slot'] = inferred_slot
                        print(f"🔧 推断装备 {item_name} 的槽位: {inferred_slot}")
                    else:
                        print(f"❌ 无法推断装备 {item_name} 的槽位")
                        return False
                item = self.equipment_converter.standardize_item_dict(item)
            
            # 检查是否为有效的装备类型
            item_type = item.get('type', '')
            valid_equipment_types = ['装备', '武器', '防具', '头盔', '项链', '戒指', '手镯', '勋章']
            if not item or item_type not in valid_equipment_types:
                print(f"❌ 无效的装备类型: {item_type}, 有效类型: {valid_equipment_types}")
                return False
            
            # 获取装备槽位
            item_slot = item.get('slot')
            
            # 🔧 新增：戒指特殊处理逻辑
            if item_slot == '戒指':
                return self._equip_ring(item, player, specific_slot)
            
            # 🔧 新增：手镯特殊处理逻辑
            if item_slot == '手镯':
                return self._equip_bracelet(item, player, specific_slot)
            
            # 普通装备处理
            if item_slot not in self.EQUIPMENT_SLOTS:
                return False
            
            # 检查职业限制
            if not self._check_class_requirement(item, player):
                return False
            
            # 检查等级限制
            if not self._check_level_requirement(item, player):
                return False
            
            # 🔧 新增：检查性别限制
            if not self._check_gender_requirement(item, player):
                return False
            
            # 获取当前装备（如果有）
            current_item = self.equipped_items[item_slot]
            replaced_item = current_item.copy() if current_item else None
            
            # 装备新物品
            self.equipped_items[item_slot] = item.copy()
            
            # 重新计算装备属性
            self._recalculate_equipment_stats()
            
            # 🔧 新增：标记装备变化，通知面板刷新
            self._notify_equipment_changed()
            
            print(f"✅ 成功装备: {item.get('name', '未知装备')} 到 {item_slot}")
            return replaced_item
            
        except Exception as e:
            print(f"❌ 装备物品时发生错误: {e}")
            traceback.print_exc()
            return False
    
    def _equip_ring(self, item: Dict[str, Any], player, specific_slot: str = None) -> Optional[Dict[str, Any]]:
        """
        特殊处理装备戒指的逻辑
        
        参数:
            item: 要装备的戒指
            player: 玩家对象
            specific_slot: "戒指1" 或 "戒指2"
            
        返回:
            被替换的戒指或None
        """
        from game.ui.ring_choice_dialog import RingChoiceDialog
        
        # 如果指定了槽位，直接装备
        if specific_slot and specific_slot in ["戒指1", "戒指2"]:
            return self._equip_to_specific_slot(item, player, specific_slot)
            
        # 如果两个戒指槽都满了
        if self.equipped_items.get("戒指1") and self.equipped_items.get("戒指2"):
            # 打开选择对话框，让玩家选择替换哪个
            choice = RingChoiceDialog.ask_which_ring_to_replace()
            if choice:
                return self._equip_to_specific_slot(item, player, choice)
            else:
                return False  # 玩家取消
        
        # 如果有空位，自动装备到空位
        if not self.equipped_items.get("戒指1"):
            return self._equip_to_specific_slot(item, player, "戒指1")
        else:
            return self._equip_to_specific_slot(item, player, "戒指2")

    def _equip_bracelet(self, item: Dict[str, Any], player, specific_slot: str = None) -> Optional[Dict[str, Any]]:
        """
        特殊处理装备手镯的逻辑
        
        参数:
            item: 要装备的手镯
            player: 玩家对象
            specific_slot: "手镯1" 或 "手镯2"
            
        返回:
            被替换的手镯或None
        """
        # 暂时借用RingChoiceDialog，后续可以创建专门的BraceletChoiceDialog
        from game.ui.ring_choice_dialog import RingChoiceDialog
        
        # 如果指定了槽位，直接装备
        if specific_slot and specific_slot in ["手镯1", "手镯2"]:
            return self._equip_to_specific_slot(item, player, specific_slot)
            
        # 如果两个手镯槽都满了
        if self.equipped_items.get("手镯1") and self.equipped_items.get("手镯2"):
            # 打开选择对话框，让玩家选择替换哪个
            choice = RingChoiceDialog.ask_which_item_to_replace("手镯")
            if choice:
                return self._equip_to_specific_slot(item, player, choice)
            else:
                return False  # 玩家取消
        
        # 如果有空位，自动装备到空位
        if not self.equipped_items.get("手镯1"):
            return self._equip_to_specific_slot(item, player, "手镯1")
        else:
            return self._equip_to_specific_slot(item, player, "手镯2")

    def _equip_to_specific_slot(self, item: Dict[str, Any], player, slot: str) -> Optional[Dict[str, Any]]:
        """
        将物品装备到指定的槽位
        
        参数:
            item: 要装备的物品
            player: 玩家对象
            slot: 目标槽位
            
        返回:
            被替换的物品或None
        """
        if not self._check_class_requirement(item, player):
            return False
        if not self._check_level_requirement(item, player):
            return False
        if not self._check_gender_requirement(item, player):
            return False
        
        # 获取当前装备（如果有）
        current_item = self.equipped_items.get(slot)
        replaced_item = current_item.copy() if current_item else None
            
        # 装备新物品
        self.equipped_items[slot] = item.copy()
        
        # 重新计算装备属性
        self._recalculate_equipment_stats()
        
        # 🔧 新增：标记装备变化，通知面板刷新
        self._notify_equipment_changed()
            
        return replaced_item
    
    def unequip_item(self, slot: str, player) -> bool:
        """
        卸下装备
        
        参数:
            slot: 装备槽位
            player: 玩家对象
            
        返回:
            bool: 是否成功卸下
        """
        if slot not in self.equipped_items or not self.equipped_items[slot]:
            return False
        
        item = self.equipped_items[slot]
        
        # 检查背包是否有空间
        if hasattr(player, 'inventory_manager'):
            if not player.inventory_manager.add_item(item, 1):
                return False  # 背包满了，无法卸下
        
        # 卸下装备
        self.equipped_items[slot] = None
        
        # 重新计算装备属性
        self._recalculate_equipment_stats()
        
        # 🔧 新增：标记装备变化，通知面板刷新
        self._notify_equipment_changed()
        
        return True
    
    def _check_class_requirement(self, item: Dict[str, Any], player) -> bool:
        """
        检查职业限制
        
        参数:
            item: 装备物品
            player: 玩家对象
            
        返回:
            bool: 是否满足职业要求
        """
        required_class = item.get('required_class')
        if not required_class:
            return True  # 没有职业限制
        
        if isinstance(required_class, str):
            return player.character_class == required_class
        elif isinstance(required_class, list):
            return player.character_class in required_class
        
        return False
    
    def _check_level_requirement(self, item: Dict[str, Any], player) -> bool:
        """
        检查等级限制
        
        参数:
            item: 装备物品
            player: 玩家对象
            
        返回:
            bool: 是否满足等级要求
        """
        # 检查多种可能的等级字段
        required_level = item.get('required_level') or item.get('level', 1)
        
        # 如果有requirements字段，检查其中的等级要求
        requirements = item.get('requirements', {})
        if requirements and isinstance(requirements, dict):
            # 检查是否有等级要求
            if 'level' in requirements:
                required_level = max(required_level, requirements['level'])
            # 检查职业等级要求（攻击、魔法、道术）
            for attr in ['attack', 'magic', 'taoism']:
                if attr in requirements:
                    # 这里简化处理，假设职业等级要求约等于player level的2倍
                    attr_requirement = requirements[attr]
                    estimated_level = max(1, attr_requirement // 10)  # 粗略估算需要的等级
                    required_level = max(required_level, estimated_level)
        
        if player.level < required_level:
            print(f"❌ 等级不足: 需要等级 {required_level}，当前等级 {player.level}")
            return False
        
        return True
    
    def _check_gender_requirement(self, item: Dict[str, Any], player) -> bool:
        """
        检查性别限制
        
        参数:
            item: 装备物品
            player: 玩家对象
            
        返回:
            bool: 是否满足性别要求
        """
        # 方法1：检查装备是否有明确的性别要求字段
        required_gender = item.get('required_gender')
        if required_gender:
            player_gender = getattr(player, 'gender', '男')
            if required_gender != player_gender:
                print(f"❌ 装备失败: {item.get('name', '未知装备')} 不适合{player_gender}性角色")
                return False
            return True
        
        # 方法2：通过装备名称判断性别要求（兼容现有数据）
        item_name = item.get('name', '')
        player_gender = getattr(player, 'gender', '男')
        
        # 检查装备名称中的性别标识
        if '(男)' in item_name and player_gender != '男':
            print(f"❌ 装备失败: {item_name} 仅限男性角色使用")
            return False
        elif '(女)' in item_name and player_gender != '女':
            print(f"❌ 装备失败: {item_name} 仅限女性角色使用")
            return False
        
        # 如果没有性别标识，则认为是通用装备
        return True
    
    def _infer_equipment_slot(self, item_name: str) -> str:
        """根据装备名称推断装备槽位"""
        # 武器关键词
        weapon_keywords = ['剑', '刀', '斧', '棍', '杖', '锄', '匕首', '扇', '刃']
        # 头盔关键词
        helmet_keywords = ['头盔', '帽']
        # 防具关键词
        armor_keywords = ['衣', '甲', '袍', '披风']
        # 项链关键词
        necklace_keywords = ['项链', '珠', '链', '竹笛', '镜', '铃铛']
        # 戒指关键词
        ring_keywords = ['戒指']
        # 手镯关键词
        bracelet_keywords = ['手镯', '手套']
        # 勋章关键词
        medal_keywords = ['勋章', '塔']
        
        for keyword in weapon_keywords:
            if keyword in item_name:
                return '武器'
        
        for keyword in helmet_keywords:
            if keyword in item_name:
                return '头盔'
        
        for keyword in armor_keywords:
            if keyword in item_name:
                return '胸甲'
        
        for keyword in necklace_keywords:
            if keyword in item_name:
                return '护符'
        
        for keyword in ring_keywords:
            if keyword in item_name:
                return '戒指'
        
        for keyword in bracelet_keywords:
            if keyword in item_name:
                return '手镯'
        
        for keyword in medal_keywords:
            if keyword in item_name:
                return '勋章'
        
        return None
    
    def _recalculate_equipment_stats(self):
        """
        重新计算装备提供的属性加成（考虑耐久度影响）
        """
        # 重置所有属性
        for stat in self.equipment_stats:
            self.equipment_stats[stat] = 0
        
        # 累加所有装备的属性
        for slot, item in self.equipped_items.items():
            if item:
                # 检查耐久度
                durability_ratio = 1.0
                if 'durability' in item:
                    current = item.get('current_durability', item['durability'])
                    if current == 0:
                        continue  # 耐久度为0的装备不提供属性
                    elif current < item['durability'] * 0.3:  # 低于30%耐久度
                        durability_ratio = 0.5  # 属性减半
                
                # 支持多种属性字段格式：'stats'、'attributes' 和嵌套的 'attributes.stats'
                item_stats = item.get('stats')
                if not item_stats:
                    # 检查 attributes 字段
                    attributes = item.get('attributes', {})
                    if isinstance(attributes, dict):
                        # 检查是否有嵌套的 stats
                        item_stats = attributes.get('stats', attributes)
                    else:
                        item_stats = {}

                # 如果装备没有 stats 字段，可能需要转换
                if not item_stats:
                    item_stats = self._convert_legacy_item_stats(item)
                
                # 应用属性（考虑耐久度影响）
                for stat, value in item_stats.items():
                    if stat in self.equipment_stats:
                        if isinstance(value, float):
                            self.equipment_stats[stat] += value * durability_ratio
                        else:
                            self.equipment_stats[stat] += int(value * durability_ratio)
        
        # 添加套装加成
        set_bonuses = self.get_equipment_set_bonus()
        if set_bonuses:  # 确保set_bonuses不为None
            for stat, value in set_bonuses.items():
                if stat in self.equipment_stats:
                    self.equipment_stats[stat] += value
    
    def _convert_legacy_item_stats(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换旧格式的装备属性到新格式
        
        Args:
            item: 装备物品数据
            
        Returns:
            转换后的属性字典
        """
        try:
            # 使用装备转换器的标准化方法
            standardized_item = self.equipment_converter.standardize_item_dict(item)
            return standardized_item.get('attributes', {})
        except Exception:
            # 如果转换器不可用，使用内置的简单转换
            return self._simple_convert_stats(item)
    
    def _simple_convert_stats(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        简单的属性转换方法（兜底方案）
        
        Args:
            item: 装备物品数据
            
        Returns:
            转换后的属性字典
        """
        stats = {}
        
        # 转换攻击力
        if 'attack' in item:
            attack_val = item['attack']
            if isinstance(attack_val, list) and len(attack_val) == 2:
                stats['攻击下限'] = attack_val[0]
                stats['攻击上限'] = attack_val[1]
            elif isinstance(attack_val, (int, float)):
                stats['攻击下限'] = attack_val
                stats['攻击上限'] = attack_val
        
        # 转换防御力
        if 'defense' in item:
            defense_val = item['defense']
            if isinstance(defense_val, list) and len(defense_val) == 2:
                stats['防御下限'] = defense_val[0]
                stats['防御上限'] = defense_val[1]
            elif isinstance(defense_val, (int, float)):
                stats['防御下限'] = defense_val
                stats['防御上限'] = defense_val
        
        # 转换魔法攻击
        if 'magic' in item:
            magic_val = item['magic']
            if isinstance(magic_val, list) and len(magic_val) == 2:
                stats['魔法攻击下限'] = magic_val[0]
                stats['魔法攻击上限'] = magic_val[1]
            elif isinstance(magic_val, (int, float)):
                stats['魔法攻击下限'] = magic_val
                stats['魔法攻击上限'] = magic_val
        
        # 转换道术攻击
        if 'taoism' in item:
            taoism_val = item['taoism']
            if isinstance(taoism_val, list) and len(taoism_val) == 2:
                stats['道术攻击下限'] = taoism_val[0]
                stats['道术攻击上限'] = taoism_val[1]
            elif isinstance(taoism_val, (int, float)):
                stats['道术攻击下限'] = taoism_val
                stats['道术攻击上限'] = taoism_val
        
        # 转换单值属性
        single_attr_mapping = {
            'magic_defense': '魔抗',
            'accuracy': '准确',
            'agility': '敏捷',
            'luck': '幸运',
            'attack_speed': '攻速',
            'critical_rate': '暴击率',
            'hp': '生命值',
            'mp': '魔法值',
            'drop_rate_bonus': '掉落率加成',
            'exp_bonus': '经验加成',
            'gold_bonus': '金币加成',
        }
        
        for eng_attr, chinese_attr in single_attr_mapping.items():
            if eng_attr in item:
                stats[chinese_attr] = item[eng_attr]
        
        return stats
    
    def get_equipped_item(self, slot: str) -> Optional[Dict[str, Any]]:
        """
        获取指定槽位的装备
        
        参数:
            slot: 装备槽位
            
        返回:
            装备物品或None
        """
        return self.equipped_items.get(slot)
    
    def get_all_equipped_items(self) -> Dict[str, Optional[Dict[str, Any]]]:
        """
        获取所有装备
        
        返回:
            所有装备的字典
        """
        return self.equipped_items.copy()
    
    def get_equipment_stats(self) -> Dict[str, Any]:
        """
        获取装备提供的属性加成（带缓存）

        返回:
            属性加成字典
        """
        if self._cache_version != self.equipment_version:
            self._recalculate_equipment_stats()
            self._stats_cache = self.equipment_stats.copy()
            self._cache_version = self.equipment_version

        # 🔧 修复：确保返回值不为 None
        if self._stats_cache is None:
            self._stats_cache = self.equipment_stats.copy()

        return self._stats_cache
    
    def get_total_equipment_value(self) -> int:
        """
        获取所有装备的总价值
        
        返回:
            总价值
        """
        total_value = 0
        for item in self.equipped_items.values():
            if item:
                total_value += item.get('value', 0)
        return total_value
    
    def get_equipment_durability_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取装备耐久度信息
        
        返回:
            装备耐久度信息字典
        """
        durability_info = {}
        for slot, item in self.equipped_items.items():
            if item and 'durability' in item:
                durability_info[slot] = {
                    'current': item.get('current_durability', item['durability']),
                    'max': item['durability'],
                    'percentage': (item.get('current_durability', item['durability']) / item['durability']) * 100
                }
        return durability_info
    
    def repair_equipment(self, slot: str, repair_amount: int = None) -> bool:
        """
        修理装备
        
        参数:
            slot: 装备槽位
            repair_amount: 修理数量，None表示完全修理
            
        返回:
            bool: 是否成功修理
        """
        item = self.equipped_items.get(slot)
        if not item or 'durability' not in item:
            return False
        
        current_durability = item.get('current_durability', item['durability'])
        max_durability = item['durability']
        
        if current_durability >= max_durability:
            return False  # 已经是满耐久
        
        if repair_amount is None:
            # 完全修理
            item['current_durability'] = max_durability
        else:
            # 部分修理
            item['current_durability'] = min(max_durability, current_durability + repair_amount)
        
        # 重新计算属性
        self._recalculate_equipment_stats()
        self._notify_equipment_changed()
        
        return True
    
    def damage_equipment(self, slot: str, damage_amount: int = 1):
        """
        损坏装备耐久度
        
        参数:
            slot: 装备槽位
            damage_amount: 损坏数量
        """
        item = self.equipped_items.get(slot)
        if not item or 'durability' not in item:
            return
        
        current_durability = item.get('current_durability', item['durability'])
        item['current_durability'] = max(0, current_durability - damage_amount)
        
        # 如果耐久度为0，装备失效
        if item['current_durability'] == 0:
            # 标记装备需要重新计算属性
            self._recalculate_equipment_stats()
            self._notify_equipment_changed()
            
            # 添加提示
            print(f"⚠️ {item.get('name', '装备')}已损坏，需要修理！")
    
    def get_equipment_set_bonus(self) -> Dict[str, Any]:
        """
        获取套装加成（带缓存）
        
        返回:
            套装加成字典
        """
        # 检查缓存
        if self._set_bonus_cache_version == self.equipment_version:
            return self._set_bonus_cache
        
        # 统计套装件数
        set_counts = {}
        for item in self.equipped_items.values():
            if item and 'set_name' in item:
                set_name = item['set_name']
                set_counts[set_name] = set_counts.get(set_name, 0) + 1
        
        # 计算套装加成
        set_bonus = {}
        # 获取套装配置
        set_config = self.equipment_loader.get_set_config()
        
        for set_name, count in set_counts.items():
            if set_name in set_config:
                bonuses = set_config[set_name].get('bonuses', {})
                for required_count, bonus in bonuses.items():
                    if count >= int(required_count):
                        # 累加套装加成
                        for stat, value in bonus.items():
                            set_bonus[stat] = set_bonus.get(stat, 0) + value
        
        # 更新缓存
        self._set_bonus_cache = set_bonus
        self._set_bonus_cache_version = self.equipment_version
        
        return set_bonus
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式（用于保存）
        
        返回:
            字典格式的装备数据
        """
        return {
            'equipped_items': self.equipped_items,
            'equipment_stats': self.equipment_stats,
            'equipment_version': self.equipment_version
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """
        从字典格式加载（用于读取存档）
        
        参数:
            data: 字典格式的装备数据
        """
        self.equipped_items = data.get('equipped_items', {slot: None for slot in self.EQUIPMENT_SLOTS.keys()})
        self.equipment_stats = data.get('equipment_stats', {
            "攻击下限": 0, "攻击上限": 0, "防御下限": 0, "防御上限": 0,
            "魔法攻击下限": 0, "魔法攻击上限": 0, "道术攻击下限": 0, "道术攻击上限": 0,
            "生命值": 0, "魔法值": 0, "魔抗": 0, "攻速": 0.0
        })
        self.equipment_version = data.get('equipment_version', 0)
        
        # 🔧 新增：确保存档加载后的兼容性
        self._ensure_slot_compatibility()
        
        # 重新计算装备属性（确保数据一致性）
        self._recalculate_equipment_stats()
    
    def equip_starter_equipment(self, player):
        """
        为玩家装备初始装备
        
        参数:
            player: 玩家对象
        """
        try:
            import json
            import os
            
            # 加载初始装备配置 - 使用资源管理器
            config_path = get_game_data_path("starter_equipment.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    starter_config = json.load(f)
            else:
                print("未找到初始装备配置文件，跳过装备初始装备")
                return
            
            # 转换性别格式
            gender_key = "male" if player.gender == "男" else "female"
            
            # 获取该职业和性别的初始装备
            if player.character_class in starter_config["starter_equipment"]:
                char_config = starter_config["starter_equipment"][player.character_class][gender_key]
                equipped_count = 0
                
                # 装备武器
                weapon_name = char_config.get("weapon")
                if weapon_name:
                    weapon_item = self._create_starter_weapon(weapon_name, player.character_class)
                    if weapon_item and self.equip_item(weapon_item, player) is not False:
                        equipped_count += 1
                
                # 装备护甲
                armor_name = char_config.get("armor") 
                if armor_name:
                    armor_item = self._create_starter_armor(armor_name)
                    if armor_item and self.equip_item(armor_item, player) is not False:
                        equipped_count += 1
                
                if equipped_count > 0:
                    print(f"✅ 为 {player.name}({player.character_class}-{player.gender}) 装备了 {equipped_count} 件初始装备")
                else:
                    print(f"没有为 {player.name} 装备任何初始装备")
            else:
                print(f"⚠️ 未找到职业 {player.character_class} 的初始装备配置")
                
        except Exception as e:
            print(f"装备初始装备时出错: {e}")
            
    def _create_starter_weapon(self, weapon_name, character_class):
        """根据武器名称和职业创建初始武器"""
        weapon_configs = {
            "木剑": {
                "id": 1001,
                "name": "木剑",
                "type": "装备",
                "slot": "武器",
                "rarity": "普通",
                "required_level": 1,
                "required_class": [],  # 修改：允许所有职业装备木剑作为初始武器
                "stats": {"攻击下限": 1, "攻击上限": 4, "准确": 1},
                "sell_price": 10,
                "durability": 100,
                "icon_path": "武器/木剑.png"  # 修正图片路径
            },            
        }
        return weapon_configs.get(weapon_name)
    
    def _create_starter_armor(self, armor_name):
        """根据护甲名称创建初始护甲"""
        armor_configs = {
            "布衣(男)": {
                "id": 4001,
                "name": "布衣(男)",
                "type": "装备",
                "slot": "胸甲",
                "rarity": "普通",
                "required_level": 1,
                "required_class": [],
                "stats": {"防御下限": 1, "防御上限": 2, "生命值": 5},
                "sell_price": 8,
                "durability": 60,
                "icon_path": "防具/布衣(男).png"  # 修正图片路径
            },
            "布衣(女)": {
                "id": 4002,
                "name": "布衣(女)",
                "type": "装备",
                "slot": "胸甲", 
                "rarity": "普通",
                "required_level": 1,
                "required_class": [],
                "stats": {"防御下限": 1, "防御上限": 2, "生命值": 5},
                "sell_price": 8,
                "durability": 60,
                "icon_path": "防具/布衣(女).png"  # 修正图片路径
            }
        }
        return armor_configs.get(armor_name)
    
    def get_equipment_by_slot_for_class(self, slot: str, player_class: str, player_level: int) -> List[Dict[str, Any]]:
        """
        获取指定槽位、职业和等级的装备列表
        
        参数:
            slot: 装备槽位
            player_class: 玩家职业
            player_level: 玩家等级
            
        返回:
            适合的装备列表
        """
        return self.equipment_loader.get_suitable_equipment(player_level, player_class, slot)
    
    def get_equipment_info_display(self, slot: str) -> str:
        """
        获取装备信息显示文本
        
        参数:
            slot: 装备槽位
            
        返回:
            装备信息文本
        """
        equipment = self.equipped_items.get(slot)
        if not equipment:
            return f"{slot}: 无"
        
        name = equipment.get('name', '未知装备')
        # 支持两种属性字段格式：'stats' 和 'attributes'
        stats = equipment.get('stats') or equipment.get('attributes', {})
        
        # 构建属性显示文本 - 使用属性翻译器
        stat_texts = []
        for stat_name, value in stats.items():
            if value != 0:
                from game.utils.attribute_translator import format_attribute_text
                # 格式化属性文本，去掉前面的空格
                formatted_text = format_attribute_text(stat_name, value)
                if formatted_text.startswith('  '):
                    formatted_text = formatted_text[2:]  # 去掉前面的两个空格
                stat_texts.append(formatted_text)
        
        stat_text = " ".join(stat_texts) if stat_texts else "无属性"
        
        # 添加耐久度信息
        durability_text = ""
        if 'durability' in equipment:
            current = equipment.get('current_durability', equipment['durability'])
            max_dur = equipment['durability']
            if current < max_dur:
                durability_text = f" [耐久:{current}/{max_dur}]"
                if current == 0:
                    durability_text += " (已损坏)"
        
        return f"{slot}: {name} ({stat_text}){durability_text}"
    
    def _notify_equipment_changed(self):
        """
        🔧 新增：通知装备变化
        标记装备发生变化，用于装备面板实时更新
        """
        self.equipment_changed = True
        self.equipment_version += 1
        
    def check_equipment_changed(self) -> bool:
        """
        🔧 新增：检查装备是否发生变化
        
        返回:
            bool: 装备是否发生变化
        """
        return self.equipment_changed
    
    def get_equipment_version(self) -> int:
        """
        🔧 新增：获取装备版本号
        
        返回:
            int: 当前装备版本号
        """
        return self.equipment_version
    
    def mark_equipment_synced(self):
        """
        🔧 新增：标记装备已同步
        装备面板更新后调用此方法，清除变化标记
        """
        self.equipment_changed = False
        
    def get_equipment_comparison(self, new_item: Dict[str, Any], slot: str = None) -> Dict[str, Any]:
        """
        获取新装备与当前装备的比较
        
        参数:
            new_item: 新装备
            slot: 装备槽位（如果不指定，则根据装备类型自动判断）
            
        返回:
            比较结果字典
        """
        if not slot:
            slot = new_item.get('slot')
            if slot == '戒指':
                # 对于戒指，比较两个戒指槽中属性较低的那个
                ring1 = self.equipped_items.get('戒指1')
                ring2 = self.equipped_items.get('戒指2')
                if not ring1:
                    slot = '戒指1'
                elif not ring2:
                    slot = '戒指2'
                else:
                    # 比较两个戒指的总属性值，选择较弱的
                    ring1_value = self._calculate_equipment_score(ring1)
                    ring2_value = self._calculate_equipment_score(ring2)
                    slot = '戒指1' if ring1_value <= ring2_value else '戒指2'
            elif slot == '手镯':
                # 对于手镯，采用类似逻辑
                bracelet1 = self.equipped_items.get('手镯1')
                bracelet2 = self.equipped_items.get('手镯2')
                if not bracelet1:
                    slot = '手镯1'
                elif not bracelet2:
                    slot = '手镯2'
                else:
                    bracelet1_value = self._calculate_equipment_score(bracelet1)
                    bracelet2_value = self._calculate_equipment_score(bracelet2)
                    slot = '手镯1' if bracelet1_value <= bracelet2_value else '手镯2'
        
        current_item = self.equipped_items.get(slot)
        
        # 获取新装备属性
        new_stats = new_item.get('stats') or new_item.get('attributes', {})
        if not new_stats:
            new_stats = self._convert_legacy_item_stats(new_item)
        
        # 获取当前装备属性
        current_stats = {}
        if current_item:
            current_stats = current_item.get('stats') or current_item.get('attributes', {})
            if not current_stats:
                current_stats = self._convert_legacy_item_stats(current_item)
        
        # 计算属性差异
        stat_diff = {}
        all_stats = set(new_stats.keys()) | set(current_stats.keys())
        for stat in all_stats:
            new_value = new_stats.get(stat, 0)
            current_value = current_stats.get(stat, 0)
            diff = new_value - current_value
            if diff != 0:
                stat_diff[stat] = {
                    'current': current_value,
                    'new': new_value,
                    'diff': diff
                }
        
        return {
            'current_item': current_item,
            'new_item': new_item,
            'stat_differences': stat_diff,
            'slot': slot
        }
    
    def _calculate_equipment_score(self, item: Dict[str, Any]) -> float:
        """
        计算装备的综合评分
        
        参数:
            item: 装备物品
            
        返回:
            装备评分
        """
        if not item:
            return 0
        
        score = 0
        stats = item.get('stats') or item.get('attributes', {})
        if not stats:
            stats = self._convert_legacy_item_stats(item)
        
        # 属性权重
        stat_weights = {
            '攻击下限': 1.0,
            '攻击上限': 1.2,
            '防御下限': 0.8,
            '防御上限': 1.0,
            '魔法攻击下限': 1.0,
            '魔法攻击上限': 1.2,
            '道术攻击下限': 1.0,
            '道术攻击上限': 1.2,
            '生命值': 0.1,
            '魔法值': 0.1,
            '魔抗': 0.6,
            '攻速': 50.0,
            '暴击率': 100.0,
            '准确': 0.5,
            '敏捷': 0.5,
            '幸运': 0.8,
            '掉落率加成': 50.0,
            '经验加成': 50.0,
            '金币加成': 30.0
        }
        
        for stat, value in stats.items():
            weight = stat_weights.get(stat, 1.0)
            score += value * weight
        
        # 考虑装备品质
        rarity_multipliers = {
            '普通': 1.0,
            '优秀': 1.2,
            '精良': 1.5,
            '稀有': 2.0,
            '史诗': 3.0,
            '传说': 5.0
        }
        rarity = item.get('rarity', '普通')
        score *= rarity_multipliers.get(rarity, 1.0)
        
        return score