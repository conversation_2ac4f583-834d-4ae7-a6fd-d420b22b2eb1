import json
import os
import logging
import time
from threading import Lock
from game.core.resource_manager import get_game_data_path

# 设置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('MonsterDataLoader')

class MonsterDataLoader:
    """
    怪物数据加载器 - 单例模式
    用于加载和处理怪物数据和技能数据，实现数据缓存机制
    """
    
    # 单例实例
    _instance = None
    _lock = Lock()
    
    # 怪物属性索引常量，便于访问数组格式数据
    LEVEL_INDEX = 0
    HP_INDEX = 1
    MP_INDEX = 2
    DEFENSE_INDEX = 3
    ATTACK_INDEX = 4
    SPEED_INDEX = 5
    ATTACK_SPEED_INDEX = 6
    GOLD_INDEX = 7
    
    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(MonsterDataLoader, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, data_path=None, monsters_file='monsters.json', skills_file='monsterskills.json'):
        """
        初始化怪物数据加载器
        
        Args:
            data_path: 数据文件所在目录
            monsters_file: 怪物数据文件名
            skills_file: 技能数据文件名
        """
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
            
        # 使用资源管理器而不是硬编码路径
        self.data_path = data_path if data_path else "game/data"
        self.monsters_file = monsters_file
        self.skills_file = skills_file
        self.monsters_data = {}
        self.skills_data = {}
        self.monster_skills = {}
        
        # 缓存相关属性
        self._monsters_file_mtime = 0
        self._skills_file_mtime = 0
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'reloads': 0
        }
        
        self.load_data()
        self._initialized = True
    
    @classmethod
    def get_instance(cls, *args, **kwargs):
        """
        获取单例实例
        
        Returns:
            MonsterDataLoader: 单例实例
        """
        if cls._instance is None:
            cls._instance = cls(*args, **kwargs)
        return cls._instance
        
    def load_data(self, force_reload=False):
        """
        加载怪物和技能数据，支持缓存检查
        
        Args:
            force_reload: 是否强制重新加载数据
        """
        try:
            # 使用资源管理器获取正确的文件路径
            monster_file = get_game_data_path(self.monsters_file)
            skills_file = get_game_data_path(self.skills_file)
            
            # 检查文件修改时间
            monsters_mtime = os.path.getmtime(monster_file) if os.path.exists(monster_file) else 0
            skills_mtime = os.path.getmtime(skills_file) if os.path.exists(skills_file) else 0
            
            # 判断是否需要重新加载
            need_reload_monsters = (force_reload or 
                                  monsters_mtime > self._monsters_file_mtime or 
                                  not self.monsters_data)
            need_reload_skills = (force_reload or 
                                skills_mtime > self._skills_file_mtime or 
                                not self.skills_data)
            
            if need_reload_monsters or need_reload_skills:
                logger.info("检测到数据文件更新，重新加载数据...")
                self._cache_stats['reloads'] += 1
                
                if need_reload_monsters:
                    # 加载怪物数据
                    with open(monster_file, 'r', encoding='utf-8') as f:
                        self.monsters_data = json.load(f)
                    self._monsters_file_mtime = monsters_mtime
                    logger.info(f"重新加载怪物数据: {len(self.monsters_data)} 个怪物")
                
                if need_reload_skills:
                    # 加载技能数据
                    with open(skills_file, 'r', encoding='utf-8') as f:
                        skills_data = json.load(f)
                        self.skills_data = skills_data['技能定义']
                        self.monster_skills = skills_data['怪物技能']
                    self._skills_file_mtime = skills_mtime
                    logger.info(f"重新加载技能数据: {len(self.skills_data)} 个技能")
                
                logger.info(f"数据加载完成 - 怪物: {len(self.monsters_data)}, 技能: {len(self.skills_data)}")
            else:
                self._cache_stats['hits'] += 1
                logger.debug("使用缓存数据，无需重新加载")
                
        except Exception as e:
            self._cache_stats['misses'] += 1
            logger.error(f"加载数据时出错: {e}")
            raise
    
    def get_cache_stats(self):
        """
        获取缓存统计信息
        
        Returns:
            dict: 缓存统计数据
        """
        return self._cache_stats.copy()
    
    def clear_cache(self):
        """
        清空缓存，强制下次重新加载数据
        """
        self.monsters_data = {}
        self.skills_data = {}
        self.monster_skills = {}
        self._monsters_file_mtime = 0
        self._skills_file_mtime = 0
        logger.info("缓存已清空")
    
    def get_monster_base_data(self, monster_name):
        """
        获取怪物基础数据 - 支持新的对象格式
        
        Args:
            monster_name: 怪物名称
            
        Returns:
            dict: 怪物基础属性字典
        """
        if monster_name not in self.monsters_data:
            logger.warning(f"找不到怪物: {monster_name}")
            return None
            
        monster_data = self.monsters_data[monster_name]
        
        # 检查数据格式：对象格式 vs 数组格式
        if isinstance(monster_data, dict):
            # 新的对象格式
            monster_attrs = {
                'name': monster_name,
                'level': monster_data.get('level', 1),
                'hp': monster_data.get('hp', 100),
                'mp': monster_data.get('mp', 0),
                'defense': monster_data.get('defense', 0)
            }
            
            # 处理攻击力范围
            attack_data = monster_data.get('attack', [1, 1])
            if isinstance(attack_data, list) and len(attack_data) == 2:
                monster_attrs['attack_min'] = attack_data[0]
                monster_attrs['attack_max'] = attack_data[1]
            else:
                monster_attrs['attack_min'] = attack_data
                monster_attrs['attack_max'] = attack_data
            
            # 添加敏捷和攻击速度
            monster_attrs['agility'] = monster_data.get('agility', 10)
            monster_attrs['attack_speed'] = monster_data.get('attack_speed', 1.0)
            
            # 添加经验和金币
            monster_attrs['exp'] = monster_data.get('exp', 0)
            monster_attrs['gold'] = monster_data.get('gold', 0)
            
            # 添加最大生命值和法力值
            monster_attrs['max_hp'] = monster_data.get('max_hp', monster_attrs['hp'])
            monster_attrs['max_mp'] = monster_data.get('max_mp', monster_attrs['mp'])
            
            # 添加类型和描述
            monster_attrs['type'] = monster_data.get('type', 'monster')
            monster_attrs['description'] = monster_data.get('description', f"等级{monster_attrs['level']}的怪物")
            
        else:
            # 兼容旧的数组格式
            monster_attrs = {
                'name': monster_name,
                'level': monster_data[self.LEVEL_INDEX],
                'hp': monster_data[self.HP_INDEX],
                'mp': monster_data[self.MP_INDEX],
                'defense': monster_data[self.DEFENSE_INDEX]
            }
            
            # 处理攻击力范围
            if isinstance(monster_data[self.ATTACK_INDEX], list) and len(monster_data[self.ATTACK_INDEX]) == 2:
                monster_attrs['attack_min'] = monster_data[self.ATTACK_INDEX][0]
                monster_attrs['attack_max'] = monster_data[self.ATTACK_INDEX][1]
            else:
                monster_attrs['attack_min'] = monster_data[self.ATTACK_INDEX]
                monster_attrs['attack_max'] = monster_data[self.ATTACK_INDEX]
            
            # 添加敏捷和攻击速度
            monster_attrs['agility'] = monster_data[self.SPEED_INDEX]
            monster_attrs['attack_speed'] = monster_data[self.ATTACK_SPEED_INDEX]
            
            # 添加经验和金币
            if len(monster_data) > self.GOLD_INDEX:
                monster_attrs['exp'] = monster_data[self.GOLD_INDEX]
                monster_attrs['gold'] = int(monster_data[self.GOLD_INDEX] * 0.8)
            else:
                monster_attrs['exp'] = 0
                monster_attrs['gold'] = 0
                
            # 添加最大生命值和法力值
            monster_attrs['max_hp'] = monster_attrs['hp']
            monster_attrs['max_mp'] = monster_attrs['mp']
            
            # 根据等级设置类型
            level = monster_attrs['level']
            if level >= 50:
                monster_attrs['type'] = "boss"
            elif level >= 35:
                monster_attrs['type'] = "elite"
            else:
                monster_attrs['type'] = "monster"
            
            monster_attrs['description'] = f"等级{level}的{monster_attrs['type']}怪物"
            
        return monster_attrs
    
    def get_monster_skills(self, monster_name):
        """
        获取怪物技能列表
        
        Args:
            monster_name: 怪物名称
            
        Returns:
            list: 技能详情列表
        """
        if monster_name not in self.monster_skills:
            return []
            
        skill_names = self.monster_skills[monster_name]
        skills = []
        
        for skill_name in skill_names:
            if skill_name in self.skills_data:
                skills.append(self.skills_data[skill_name])
            else:
                logger.warning(f"找不到技能: {skill_name}")
                
        return skills
    
    def get_full_monster_data(self, monster_name):
        """
        获取怪物完整数据，包括基础属性和技能
        
        Args:
            monster_name: 怪物名称
            
        Returns:
            dict: 怪物完整数据
        """
        monster_base = self.get_monster_base_data(monster_name)
        if not monster_base:
            return None
            
        monster_data = monster_base.copy()
        monster_data['skills'] = self.get_monster_skills(monster_name)
        
        # 根据怪物等级确定怪物类型
        level = monster_data['level']
        if level >= 50:
            monster_data['type'] = "首领"
        elif level >= 35:
            monster_data['type'] = "精英"
        else:
            monster_data['type'] = "普通"
        
        # 添加Enemy类需要的额外字段
        monster_data['id'] = hash(monster_name) % 10000  # 生成简单ID
        monster_data['description'] = f"等级{level}的{monster_data['type']}怪物"
        monster_data['image_size'] = (40, 40)
        monster_data['current_hp'] = monster_data['hp']
        monster_data['current_mp'] = monster_data['mp']
        
        # 确保攻击力数据正确传递（修复攻击力过低的问题）
        if 'attack_min' in monster_data and 'attack_max' in monster_data:
            # 确保attack字典格式存在，供Enemy类使用
            monster_data['attack'] = {
                'min': monster_data['attack_min'], 
                'max': monster_data['attack_max']
            }
        elif 'attack' in monster_data and isinstance(monster_data['attack'], dict):
            # 从attack字典提取min/max值
            monster_data['attack_min'] = monster_data['attack']['min']
            monster_data['attack_max'] = monster_data['attack']['max']
        else:
            # 如果基础数据中没有正确的攻击力，从原始数据重新解析
            raw_data = self.monsters_data[monster_name]
            attack_data = raw_data[self.ATTACK_INDEX]
            if isinstance(attack_data, list) and len(attack_data) == 2:
                monster_data['attack_min'] = attack_data[0]
                monster_data['attack_max'] = attack_data[1]
                monster_data['attack'] = {'min': attack_data[0], 'max': attack_data[1]}
            else:
                monster_data['attack_min'] = attack_data
                monster_data['attack_max'] = attack_data
                monster_data['attack'] = {'min': attack_data, 'max': attack_data}
        
        # 添加防御力字典格式（如果不存在）
        if 'defense' in monster_data and not isinstance(monster_data['defense'], dict):
            defense_val = monster_data['defense']
            monster_data['defense'] = {'min': defense_val, 'max': defense_val}
            monster_data['defense_min'] = defense_val
            monster_data['defense_max'] = defense_val
        
        # 添加魔法攻击力（默认为0）
        if 'magic_attack' not in monster_data:
            monster_data['magic_attack'] = {'min': 0, 'max': 0}
            monster_data['magic_attack_min'] = 0
            monster_data['magic_attack_max'] = 0
            
        return monster_data
    
    def get_all_monster_names(self):
        """
        获取所有怪物名称
        
        Returns:
            list: 怪物名称列表
        """
        return list(self.monsters_data.keys())
    
    def get_monsters_by_level_range(self, min_level, max_level):
        """
        根据等级范围获取怪物列表
        
        Args:
            min_level: 最小等级
            max_level: 最大等级
            
        Returns:
            list: 符合条件的怪物名称列表
        """
        result = []
        for monster_name, monster_data in self.monsters_data.items():
            level = monster_data[self.LEVEL_INDEX]
            if min_level <= level <= max_level:
                result.append(monster_name)
        return result
    
    def create_monster_instance(self, monster_name, level_modifier=0):
        """
        创建怪物实例，可以应用等级修正
        
        Args:
            monster_name: 怪物名称
            level_modifier: 等级修正值，可以是正数或负数
            
        Returns:
            dict: 调整后的怪物数据
        """
        monster_data = self.get_full_monster_data(monster_name)
        if not monster_data:
            return None
            
        # 应用等级修正
        if level_modifier != 0:
            modified_data = monster_data.copy()
            base_level = modified_data['level']
            modified_level = max(1, base_level + level_modifier)
            
            # 等级修正系数
            level_ratio = modified_level / base_level
            
            # 调整基础属性
            modified_data['level'] = modified_level
            modified_data['hp'] = int(modified_data['hp'] * level_ratio)
            modified_data['mp'] = int(modified_data['mp'] * level_ratio)
            modified_data['attack_min'] = int(modified_data['attack_min'] * level_ratio)
            modified_data['attack_max'] = int(modified_data['attack_max'] * level_ratio)
            modified_data['defense'] = int(modified_data['defense'] * level_ratio)
            modified_data['exp'] = int(modified_data['exp'] * level_ratio)
            modified_data['gold'] = int(modified_data['gold'] * level_ratio)
            
            # 根据新等级重新确定怪物类型
            if modified_level >= 50:
                modified_data['type'] = "首领"
            elif modified_level >= 35:
                modified_data['type'] = "精英"
            else:
                modified_data['type'] = "普通"
                
            return modified_data
        
        return monster_data


# 演示使用方法
if __name__ == "__main__":
    loader = MonsterDataLoader(monsters_file="test_monsters.json")
    
    # 获取怪物数据
    monster = loader.get_full_monster_data("沃玛教主")
    if monster:
        print(f"=== 怪物信息: {monster['name']} ===")
        print(f"等级: {monster['level']}")
        print(f"类型: {monster['type']}")
        print(f"生命值: {monster['hp']}")
        print(f"攻击力: {monster['attack_min']} - {monster['attack_max']}")
        print(f"防御力: {monster['defense']}")
        print(f"移动速度: {monster['speed']}")
        print(f"攻击速度: {monster['attack_speed']}")
        print(f"经验值: {monster['exp']}")
        print(f"金币: {monster['gold']}")
        
        # 显示技能
        print("\n技能列表:")
        for skill in monster['skills']:
            print(f"  - {skill['name']}: {skill['description']}")
            print(f"    类型: {skill['type']}")
            if skill['damage_multiplier'] > 0:
                print(f"    伤害倍率: {skill['damage_multiplier']}")
            if skill['area_effect']:
                print(f"    范围效果: 半径 {skill['area_radius']} 格")
            print(f"    冷却时间: {skill['cooldown']} 秒")
            
            # 显示技能效果
            if skill['effects']:
                print(f"    效果:")
                for effect in skill['effects']:
                    effect_type = effect['type']
                    if effect_type == "dot":
                        print(f"      持续伤害: {effect['damage']}/秒, 持续 {effect['duration']} 秒")
                    elif effect_type == "hot":
                        print(f"      持续治疗: {effect['damage']}/秒, 持续 {effect['duration']} 秒")
                    elif effect_type == "buff" or effect_type == "debuff":
                        print(f"      {'增益' if effect_type == 'buff' else '减益'}: {effect['stat']} {effect['value']}, 持续 {effect['duration']} 秒")
                    elif effect_type == "stun":
                        print(f"      眩晕: 持续 {effect['duration']} 秒")
                    elif effect_type == "summon":
                        print(f"      召唤: {effect['count']} 个 {effect['monster']}, 持续 {effect['duration']} 秒")
    
    # 查找一定等级范围的怪物
    level_range_monsters = loader.get_monsters_by_level_range(20, 30)
    print("\n等级 20-30 的怪物:")
    for name in level_range_monsters:
        monster_data = loader.get_monster_base_data(name)
        print(f"  - {name} (Lv.{monster_data['level']})")
    
    # 创建带等级修正的怪物
    print("\n创建等级+5的沃玛教主:")
    modified_monster = loader.create_monster_instance("沃玛教主", 5)
    print(f"原始等级: 60, 修正后等级: {modified_monster['level']}")
    print(f"原始生命值: 2200, 修正后生命值: {modified_monster['hp']}")
    print(f"原始攻击力: 35-60, 修正后攻击力: {modified_monster['attack_min']}-{modified_monster['attack_max']}")