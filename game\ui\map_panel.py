import pygame
import os
import logging
from game.ui.ui_panel import UIPanel
from game.core.resource_manager import get_game_asset_path

class MapPanel(UIPanel):
    """
    地图面板 - 简单原始版本
    """
    
    def __init__(self, screen, map_manager, player, position, size, battle_manager=None):
        """初始化地图面板"""
        super().__init__(screen, position, size)

        # 核心依赖
        self.map_manager = map_manager
        self.player = player
        self.battle_manager = battle_manager

        # 基础状态
        self.current_tab = "玛法"
        self.current_page = 0
        self.selected_map_id = None
        
        # 网格配置
        self.grid_cols = 3  # 一排两个
        self.grid_rows = 3  # 一页三排
        self.grid_cell_width = 85  # 减少宽度，更合理
        self.grid_cell_height = 70  # 适当减少高度
        self.grid_padding = 8       # 减少间距
        self.grid_margin_top = 45   # 减少顶部边距
        self.grid_margin_horizontal = 20  # 适当增加水平边距保持居中
        
        # 标签页配置
        self.tab_categories = ["玛法", "世界Boss", "幻境塔", "副本"]
        self.tab_height = 30
        self.tab_margin = 10
        self.tab_padding = 10
        self.tab_spacing = 5
        
        # 颜色配置
        self.background_color = (20, 20, 20)
        self.text_color = (255, 255, 255)
        self.tab_active_color = (80, 80, 120)
        self.tab_inactive_color = (50, 50, 50)
        self.button_color = (60, 60, 60)
        self.button_disabled_color = (40, 40, 40)
        self.selected_border_color = (50, 150, 255)
        self.unlocked_indicator_color = (50, 255, 50)
        self.locked_indicator_color = (255, 50, 50)
        
        # 缓存地图相关的rect信息
        self.map_grid_rects = []
        self.tab_rects = {}
        self.prev_button_rect = None
        self.next_button_rect = None
        self.boss_switch_rect = None
        
        # Boss开关状态
        self.boss_auto_enabled = True  # 默认开启Boss自动挑战
        
        # 从map_manager获取当前Boss自动挑战状态
        if self.map_manager and hasattr(self.map_manager, 'get_boss_spawn_enabled'):
            self.boss_auto_enabled = self.map_manager.get_boss_spawn_enabled()
        elif self.map_manager and hasattr(self.map_manager, 'get_boss_auto_challenge'):
            self.boss_auto_enabled = self.map_manager.get_boss_auto_challenge()
        
        print("MapPanel 简单版本初始化完成")

    def get_maps_for_current_tab_and_page(self):
        """获取当前标签页和页码对应的地图数据"""
        try:
            if not self.map_manager:
                return [], 0
            
            maps_per_page = self.grid_cols * self.grid_rows
            all_maps = self.map_manager.get_maps_by_category(self.current_tab, self.player)
            
            if not all_maps:
                return [], 0
            
            start_index = self.current_page * maps_per_page
            end_index = min(start_index + maps_per_page, len(all_maps))
            total_pages = (len(all_maps) + maps_per_page - 1) // maps_per_page
            
            return all_maps[start_index:end_index], total_pages
            
        except Exception as e:
            print(f"获取地图数据失败: {e}")
            return [], 0

    def _load_map_image(self, map_data):
        """加载地图缩略图"""
        try:
            map_name = map_data.get("name", "未知地图")
            
            # 尝试多种图片格式
            image_paths = [
                f"images/maps/{map_name}.jpg",
                f"images/maps/{map_name}.png"
            ]
            
            for path in image_paths:
                full_path = get_game_asset_path(path)
                if os.path.exists(full_path):
                    image = pygame.image.load(full_path).convert_alpha()
                    # 缩放到合适大小
                    return pygame.transform.scale(image, (self.grid_cell_width, self.grid_cell_height))
            
            # 如果没有找到图片，返回占位符
            return self._create_placeholder_image()
            
        except Exception as e:
            print(f"加载地图图片失败 {map_data.get('name', '未知')}: {e}")
            return self._create_placeholder_image()
    
    def _create_placeholder_image(self):
        """创建占位符图片"""
        try:
            placeholder = pygame.Surface((self.grid_cell_width, self.grid_cell_height)).convert_alpha()
            placeholder.fill((50, 50, 60))
            
            # 添加占位文字
            if hasattr(self, 'normal_font'):
                text = self.normal_font.render("无图片", True, self.text_color)
                text_rect = text.get_rect(center=(self.grid_cell_width//2, self.grid_cell_height//2))
                placeholder.blit(text, text_rect)
            
            return placeholder
            
        except Exception as e:
            print(f"创建占位符图片失败: {e}")
            return pygame.Surface((self.grid_cell_width, self.grid_cell_height))

    def render_tabs(self):
        """渲染标签页"""
        try:
            if not self.map_manager:
                categories = self.tab_categories
            else:
                categories = self.map_manager.get_categories() or self.tab_categories
            
            current_x = self.rect.left + self.tab_margin
            tab_y = self.rect.top + self.tab_margin
            
            # 清空之前的标签按钮rect
            self.tab_rects = {}
            
            for category in categories:
                # 计算标签尺寸
                text_surface = self.normal_font.render(category, True, self.text_color)
                tab_width = text_surface.get_width() + 2 * self.tab_padding
                tab_rect = pygame.Rect(current_x, tab_y, tab_width, self.tab_height)
                
                # 保存标签rect用于点击检测
                self.tab_rects[category] = tab_rect
                
                # 绘制标签背景
                is_current = (category == self.current_tab)
                bg_color = self.tab_active_color if is_current else self.tab_inactive_color

                pygame.draw.rect(self.screen, bg_color, tab_rect)
                
                # 绘制标签文字
                text_rect = text_surface.get_rect(center=tab_rect.center)
                self.screen.blit(text_surface, text_rect)
                
                current_x += tab_width + self.tab_spacing
                
        except Exception as e:
            print(f"渲染标签页失败: {e}")

    def render_map_grid(self):
        """渲染地图网格"""
        try:
            maps_on_page, _ = self.get_maps_for_current_tab_and_page()
            if not maps_on_page:
                    self._render_empty_grid()
                    return

            # 清空并重建网格rect
            self.map_grid_rects = []
            
            grid_origin_x = self.rect.left + self.grid_margin_horizontal
            grid_origin_y = self.rect.top + self.grid_margin_top

            for i, map_data in enumerate(maps_on_page):
                row = i // self.grid_cols
                col = i % self.grid_cols

                cell_x = grid_origin_x + col * (self.grid_cell_width + self.grid_padding)
                cell_y = grid_origin_y + row * (self.grid_cell_height + self.grid_padding)
                cell_rect = pygame.Rect(cell_x, cell_y, self.grid_cell_width, self.grid_cell_height)

                # 保存rect信息
                map_id = map_data.get("id", f"map_{i}")
                self.map_grid_rects.append({
                    'rect': cell_rect,
                    'map_data': map_data,
                    'map_id': map_id
                })
                
                # 渲染单个地图格子
                self._render_map_cell(cell_rect, map_data, map_id)
            
        except Exception as e:
            print(f"渲染地图网格失败: {e}")
    
    def _render_map_cell(self, cell_rect, map_data, map_id):
        """渲染单个地图格子"""
        try:
            # 1. 加载并绘制地图图片
            map_image = self._load_map_image(map_data)
            if map_image:
                self.screen.blit(map_image, cell_rect.topleft)
                
                # 添加半透明遮罩提高文字可读性
                overlay = pygame.Surface((cell_rect.width, cell_rect.height))
                overlay.set_alpha(100)
                overlay.fill((0, 0, 0))
                self.screen.blit(overlay, cell_rect.topleft)
            
            # 2. 绘制地图名称
            map_name = map_data.get("name", "未知地图")
            name_surface = self.normal_font.render(map_name, True, self.text_color)
            name_rect = name_surface.get_rect(
                centerx=cell_rect.centerx,
                top=cell_rect.top + 8
            )
            self.screen.blit(name_surface, name_rect)
            
            # 3. 绘制状态指示器
            is_unlocked = map_data.get("unlocked", True)
            indicator_text = "●" if is_unlocked else "×"
            indicator_color = self.unlocked_indicator_color if is_unlocked else self.locked_indicator_color
            
            indicator_surface = self.small_font.render(indicator_text, True, indicator_color)
            indicator_rect = indicator_surface.get_rect(
                bottomright=(cell_rect.right - 5, cell_rect.bottom - 5)
            )
            self.screen.blit(indicator_surface, indicator_rect)
            
            # 4. 绘制选中边框
            if self.selected_map_id == map_id:
                pygame.draw.rect(self.screen, self.selected_border_color, cell_rect, 2)
                
        except Exception as e:
            print(f"渲染地图格子失败 {map_data.get('name', '未知')}: {e}")
            # 绘制错误占位符
            pygame.draw.rect(self.screen, (255, 100, 100), cell_rect)
            error_text = self.small_font.render("渲染错误", True, self.text_color)
            error_rect = error_text.get_rect(center=cell_rect.center)
            self.screen.blit(error_text, error_rect)
    
    def _render_empty_grid(self):
        """渲染空网格"""
        try:
            empty_text = "此分类下暂无地图"
            text_surface = self.normal_font.render(empty_text, True, self.text_color)
            text_rect = text_surface.get_rect(center=self.rect.center)
            self.screen.blit(text_surface, text_rect)
        except Exception as e:
            print(f"渲染空网格失败: {e}")

    def render_mid_controls(self):
        """渲染中间控制区域（分页按钮等）"""
        try:
            _, total_pages = self.get_maps_for_current_tab_and_page()
            
            # 计算控制区域位置
            y_pos = (self.rect.top + self.grid_margin_top + 
                    self.grid_rows * (self.grid_cell_height + self.grid_padding) + 5)
        
            # 翻页按钮
            prev_text = f"上一页 ({self.current_page + 1}/{total_pages})"
            next_text = f"下一页 ({self.current_page + 1}/{total_pages})"
            
            prev_text_surf = self.small_font.render(prev_text, True, self.text_color)
            next_text_surf = self.small_font.render(next_text, True, self.text_color)
            
            button_width = max(prev_text_surf.get_width(), next_text_surf.get_width()) + 20
            button_height = prev_text_surf.get_height() + 10

            # 上一页按钮
            self.prev_button_rect = pygame.Rect(
                self.rect.left + self.grid_margin_horizontal, 
                y_pos, 
                button_width, 
                button_height
            )
            
            prev_color = self.button_disabled_color if self.current_page == 0 else self.button_color
            pygame.draw.rect(self.screen, prev_color, self.prev_button_rect)
            
            text_rect = prev_text_surf.get_rect(center=self.prev_button_rect.center)
            self.screen.blit(prev_text_surf, text_rect)
            
            # 下一页按钮
            self.next_button_rect = pygame.Rect(
                self.prev_button_rect.right + 10, 
                y_pos, 
                button_width, 
                button_height
            )
            
            next_color = self.button_disabled_color if self.current_page >= total_pages - 1 else self.button_color
            pygame.draw.rect(self.screen, next_color, self.next_button_rect)
            
            text_rect = next_text_surf.get_rect(center=self.next_button_rect.center)
            self.screen.blit(next_text_surf, text_rect)
            
            # Boss开关按钮
            self._render_boss_switch_button(y_pos, button_height)
            
        except Exception as e:
            print(f"渲染中间控制区域失败: {e}")
    
    def _render_boss_switch_button(self, y_pos, button_height):
        """渲染Boss开关按钮"""
        try:
            # Boss开关按钮文字和颜色
            status_text = "Boss系统: 开启" if self.boss_auto_enabled else "Boss系统: 关闭"
            button_color = (50, 150, 50) if self.boss_auto_enabled else (150, 50, 50)  # 绿色/红色
            
            boss_text_surf = self.small_font.render(status_text, True, self.text_color)
            boss_button_width = boss_text_surf.get_width() + 20
            
            # Boss开关按钮位置（放在下一页按钮右侧）
            self.boss_switch_rect = pygame.Rect(
                self.next_button_rect.right + 15, 
                y_pos, 
                boss_button_width, 
                button_height
            )
            
            # 绘制按钮背景
            pygame.draw.rect(self.screen, button_color, self.boss_switch_rect)
            pygame.draw.rect(self.screen, (255, 255, 255), self.boss_switch_rect, 1)  # 白色边框
            
            # 绘制按钮文字
            text_rect = boss_text_surf.get_rect(center=self.boss_switch_rect.center)
            self.screen.blit(boss_text_surf, text_rect)
            
            # 显示说明文字
            help_text = "开启/关闭Boss生成"
            help_surf = pygame.font.Font(None, 14).render(help_text, True, (150, 150, 150))
            help_x = self.boss_switch_rect.left
            help_y = self.boss_switch_rect.bottom + 3
            self.screen.blit(help_surf, (help_x, help_y))
            
        except Exception as e:
            print(f"渲染Boss开关按钮失败: {e}")

    def _update_boss_spawn_mode(self):
        """更新Boss生成模式"""
        try:
            if not self.map_manager:
                return
            
            if self.boss_auto_enabled:
                # 开启Boss：恢复正常Boss生成
                if hasattr(self.map_manager, 'enable_boss_spawn'):
                    self.map_manager.enable_boss_spawn()
                elif hasattr(self.map_manager, 'set_boss_spawn_enabled'):
                    self.map_manager.set_boss_spawn_enabled(True)
                elif hasattr(self.map_manager, 'restore_boss_monsters'):
                    self.map_manager.restore_boss_monsters()
                    
                print("✅ Boss生成已开启 - Boss将正常刷新")
            else:
                # 关闭Boss：将所有Boss替换为普通怪物
                if hasattr(self.map_manager, 'disable_boss_spawn'):
                    self.map_manager.disable_boss_spawn()
                elif hasattr(self.map_manager, 'set_boss_spawn_enabled'):
                    self.map_manager.set_boss_spawn_enabled(False)
                elif hasattr(self.map_manager, 'replace_boss_with_normal_monsters'):
                    self.map_manager.replace_boss_with_normal_monsters()
                    
                print("❌ Boss生成已关闭 - 所有Boss已替换为普通怪物")
            
            # 通知其他可能需要更新的系统
            if hasattr(self.map_manager, 'refresh_current_map_monsters'):
                self.map_manager.refresh_current_map_monsters()
                
        except Exception as e:
            print(f"更新Boss生成模式失败: {e}")

    def render(self):
        """主渲染方法"""
        try:
            if not self.visible:
                return
            
            # 绘制面板背景
            super().render()

            # 渲染各个组件
            self.render_tabs()
            self.render_map_grid()
            self.render_mid_controls()

        except Exception as e:
            print(f"MapPanel 渲染失败: {e}")

    def handle_event(self, event):
        """事件处理方法"""
        try:
            if not self.visible:
                return False
            
            if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                return self._handle_mouse_click(event.pos)
            
            return False

        except Exception as e:
            print(f"事件处理失败: {e}")
            return False
    
    def _handle_mouse_click(self, mouse_pos):
        """处理鼠标点击事件"""
        try:
            # 1. 检测标签页点击
            for tab_name, rect in self.tab_rects.items():
                if rect.collidepoint(mouse_pos):
                    if self.current_tab != tab_name:
                        self.current_tab = tab_name
                        self.current_page = 0
                        self.selected_map_id = None
                        self.map_grid_rects = []
                        print(f"切换到标签页: {tab_name}")
                    return True

            # 2. 检测地图格子点击
            for grid_info in self.map_grid_rects:
                if grid_info['rect'].collidepoint(mouse_pos):
                    map_data = grid_info['map_data']
                    map_id = grid_info['map_id']
                    
                    if map_data.get("unlocked", True):
                        self.selected_map_id = map_id
                        map_name = map_data.get('name')
                        print(f"选择了地图: {map_name} (ID: {map_id})")
                        
                        # 检查战斗状态 - 只有在未战斗时才能切换地图
                        if self.battle_manager:
                            # 检查是否正在战斗
                            if hasattr(self.battle_manager, 'is_in_battle') and self.battle_manager.is_in_battle:
                                print("⚔️ 战斗中无法切换地图！请先结束当前战斗。")
                                return True  # 事件已处理，但不切换地图
                            
                            # 检查是否正在自动战斗/寻怪
                            if hasattr(self.battle_manager, 'auto_battle_enabled') and self.battle_manager.auto_battle_enabled:
                                print("🤖 自动战斗进行中无法切换地图！请先停止自动战斗。")
                                return True
                            
                            if hasattr(self.battle_manager, 'auto_hunt_enabled') and self.battle_manager.auto_hunt_enabled:
                                print("🔍 自动寻怪进行中无法切换地图！请先停止自动寻怪。")
                                return True
                        
                        # 实际切换地图（只有在未战斗时才执行）
                        if self.map_manager and hasattr(self.map_manager, 'switch_map'):
                            success = self.map_manager.switch_map(map_name)
                            if success:
                                print(f"✅ 成功切换到地图: {map_name}")
                                if hasattr(self.player, 'set_current_map'):
                                    self.player.set_current_map(map_id)
                            else:
                                print(f"❌ 切换地图失败: {map_name}")
                    else:
                        print(f"🔒 点击了未解锁地图: {map_data.get('name')}")
                    
                    return True

            # 3. 检测翻页按钮点击
            if self.prev_button_rect and self.prev_button_rect.collidepoint(mouse_pos):
                if self.current_page > 0:
                    self.current_page -= 1
                    self.map_grid_rects = []
                    print("翻到上一页")
                return True
            
            if self.next_button_rect and self.next_button_rect.collidepoint(mouse_pos):
                _, total_pages = self.get_maps_for_current_tab_and_page()
                if self.current_page < total_pages - 1:
                    self.current_page += 1
                    self.map_grid_rects = []
                    print("翻到下一页")
                return True
            
            # 4. 检测Boss开关按钮点击
            if self.boss_switch_rect and self.boss_switch_rect.collidepoint(mouse_pos):
                self.boss_auto_enabled = not self.boss_auto_enabled
                status_text = "开启" if self.boss_auto_enabled else "关闭"
                print(f"Boss系统: {status_text}")
                
                # 通知map_manager更新Boss生成状态
                self._update_boss_spawn_mode()
                
                return True
            
            return False
            
        except Exception as e:
            print(f"处理鼠标点击失败: {e}")
            return False

    def update(self, current_time=None):
        """更新方法
        
        Args:
            current_time: 当前时间（毫秒），由ui_manager传入
        """
        try:
            # 定期同步Boss生成状态（防止外部修改）
            if self.map_manager and hasattr(self.map_manager, 'get_boss_spawn_enabled'):
                current_status = self.map_manager.get_boss_spawn_enabled()
                if current_status != self.boss_auto_enabled:
                    self.boss_auto_enabled = current_status
        except Exception as e:
            print(f"更新Boss状态失败: {e}")
    
    def set_boss_auto_enabled(self, enabled):
        """设置Boss自动挑战状态（外部调用）"""
        try:
            self.boss_auto_enabled = enabled
            # 更新Boss生成模式
            self._update_boss_spawn_mode()
        except Exception as e:
            print(f"设置Boss自动状态失败: {e}")
    
    def get_boss_auto_enabled(self):
        """获取当前Boss自动挑战状态"""
        return self.boss_auto_enabled
    
    def cleanup(self):
        """清理资源"""
        print("MapPanel 清理完成")