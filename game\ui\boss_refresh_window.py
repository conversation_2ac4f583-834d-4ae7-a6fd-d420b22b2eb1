#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Boss刷新状态窗口（可移动版本）
显示所有地图Boss的刷新状态和倒计时
"""

import tkinter as tk
from tkinter import ttk
import time
import json
import os
import threading
try:
    from typing import Dict, List, Tuple
except ImportError:
    # 为旧版本Python提供兼容性
    Dict = dict
    List = list
    Tuple = tuple

class BossRefreshWindow(tk.Toplevel):
    """
    Boss刷新状态窗口类
    显示所有地图Boss的刷新状态，包括刷新倒计时
    """
    
    def __init__(self, parent=None, map_manager=None):
        super().__init__(parent)
        
        # 窗口基本设置
        self.title("Boss刷新状态")
        self.geometry("500x600")
        self.resizable(True, True)
        self.configure(bg="#1a1a1a")
        
        # 设置窗口图标（可选）
        try:
            # 如果有图标文件可以设置
            pass
        except:
            pass
        
        self.map_manager = map_manager
        
        # 颜色主题
        self.colors = {
            'bg': '#1a1a1a',
            'panel_bg': '#2d2d2d',
            'border': '#404040',
            'text': '#ffffff',
            'title': '#ffffff',
            'available': '#00ff00',      # 绿色 - 已刷新
            'unavailable': '#ff6464',    # 红色 - 未刷新
            'time': '#ffff64',           # 黄色 - 时间显示
            'map_name': '#999999',       # 灰色 - 地图名称
            'world_boss': '#ff8c00'      # 橙色 - 世界Boss
        }
        
        # Boss数据
        self.boss_data = []
        
        # 更新控制
        self.is_updating = True
        self.update_interval = 1000  # 1秒更新一次
        
        # 创建界面
        self._create_widgets()
        
        # 加载Boss数据
        self._load_boss_data()
        
        # 启动自动更新
        self._start_auto_update()
        
        # 设置关闭事件
        self.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = tk.Frame(self, bg=self.colors['bg'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题栏
        title_frame = tk.Frame(main_frame, bg=self.colors['panel_bg'], relief=tk.RAISED, bd=1)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = tk.Label(
            title_frame,
            text="🏆 Boss刷新状态监控",
            bg=self.colors['panel_bg'],
            fg=self.colors['title'],
            font=("微软雅黑", 14, "bold"),
            pady=10
        )
        title_label.pack()
        
        # 控制按钮栏
        control_frame = tk.Frame(main_frame, bg=self.colors['bg'])
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        refresh_btn = tk.Button(
            control_frame,
            text="🔄 刷新数据",
            bg="#4CAF50",
            fg="white",
            font=("微软雅黑", 10),
            command=self._refresh_data,
            relief=tk.RAISED,
            bd=2
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 自动更新开关
        self.auto_update_var = tk.BooleanVar(value=True)
        auto_update_check = tk.Checkbutton(
            control_frame,
            text="自动更新",
            variable=self.auto_update_var,
            bg=self.colors['bg'],
            fg=self.colors['text'],
            selectcolor=self.colors['panel_bg'],
            font=("微软雅黑", 10),
            command=self._toggle_auto_update
        )
        auto_update_check.pack(side=tk.LEFT)
        
        # 说明文本
        info_label = tk.Label(
            control_frame,
            text="● 绿色=已刷新  ● 红色=未刷新  ● 橙色=世界Boss",
            bg=self.colors['bg'],
            fg=self.colors['map_name'],
            font=("微软雅黑", 9)
        )
        info_label.pack(side=tk.RIGHT)
        
        # Boss列表容器
        list_frame = tk.Frame(main_frame, bg=self.colors['panel_bg'], relief=tk.SUNKEN, bd=2)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建滚动区域
        canvas = tk.Canvas(list_frame, bg=self.colors['panel_bg'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg=self.colors['panel_bg'])
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.canvas = canvas
        
        # 绑定鼠标滚轮事件
        canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.bind("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def _load_boss_data(self):
        """加载Boss数据"""
        self.boss_data = []
        
        try:
            # 从地图配置文件中读取Boss信息
            config_path = os.path.join("game", "data", "maps_config.json")
            
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                maps = config_data.get("maps", {})
                for map_name, map_data in maps.items():
                    # 检查地图中的普通Boss
                    monsters = map_data.get("monsters", [])
                    for monster in monsters:
                        if monster.get("is_boss", False):
                            boss_info = {
                                "map_name": map_name,
                                "boss_name": monster["name"],
                                "respawn_time": map_data.get("respawn_time", 300),  # 默认5分钟
                                "is_world_boss": False,
                                "difficulty": monster.get("level", 1)
                            }
                            self.boss_data.append(boss_info)
                    
                    # 检查世界Boss
                    if map_data.get("is_world_boss", False):
                        boss_monster = map_data.get("boss_monster")
                        if boss_monster:
                            boss_info = {
                                "map_name": map_name,
                                "boss_name": boss_monster["name"],
                                "respawn_time": map_data.get("respawn_time", 7200),  # 默认2小时
                                "is_world_boss": True,
                                "difficulty": boss_monster.get("level", 100)
                            }
                            self.boss_data.append(boss_info)
        except Exception as e:
            print(f"加载Boss数据时出错: {e}")
            # 提供默认Boss数据
            self.boss_data = [
                {"map_name": "沃玛寺庙", "boss_name": "沃玛教主", "respawn_time": 1800, "is_world_boss": False, "difficulty": 50},
                {"map_name": "祖玛寺庙", "boss_name": "祖玛教主", "respawn_time": 7200, "is_world_boss": True, "difficulty": 80},
                {"map_name": "封魔谷", "boss_name": "虹魔教主", "respawn_time": 14400, "is_world_boss": True, "difficulty": 120},
                {"map_name": "赤月峡谷", "boss_name": "赤月恶魔", "respawn_time": 10800, "is_world_boss": True, "difficulty": 150},
            ]
        
        # 按难度排序
        self.boss_data.sort(key=lambda x: x["difficulty"])
        
        print(f"✅ 加载了 {len(self.boss_data)} 个Boss数据")
    
    def _get_boss_status(self, boss_info):
        """
        获取Boss的刷新状态
        
        Args:
            boss_info: Boss信息字典
            
        Returns:
            tuple: (是否已刷新, 剩余时间秒数)
        """
        map_name = boss_info["map_name"]
        respawn_time = boss_info["respawn_time"]
        
        # 获取Boss最后击杀时间
        current_time = time.time()
        last_kill_time = 0
        
        # 从map_manager获取Boss刷新时间
        if self.map_manager and hasattr(self.map_manager, 'boss_respawn_times'):
            last_kill_time = self.map_manager.boss_respawn_times.get(map_name, 0)
        
        # 计算是否已刷新
        time_since_kill = current_time - last_kill_time
        is_available = time_since_kill >= respawn_time
        time_until_respawn = max(0, respawn_time - time_since_kill)
        
        return is_available, time_until_respawn
    
    def _format_time(self, seconds):
        """
        将秒数格式化为时间字符串
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if seconds <= 0:
            return "00:00:00"
            
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def _update_boss_list(self):
        """更新Boss列表显示"""
        # 清除现有内容
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        if not self.boss_data:
            no_data_label = tk.Label(
                self.scrollable_frame,
                text="⚠️ 没有找到Boss数据",
                bg=self.colors['panel_bg'],
                fg=self.colors['unavailable'],
                font=("微软雅黑", 12),
                pady=20
            )
            no_data_label.pack()
            return
        
        # 创建表头
        header_frame = tk.Frame(self.scrollable_frame, bg=self.colors['border'], height=2)
        header_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        
        # 为每个Boss创建显示条目
        for i, boss_info in enumerate(self.boss_data):
            self._create_boss_entry(boss_info, i)
    
    def _create_boss_entry(self, boss_info, index):
        """创建单个Boss条目"""
        # 获取Boss状态
        is_available, time_until_respawn = self._get_boss_status(boss_info)
        
        # 主容器
        entry_frame = tk.Frame(
            self.scrollable_frame,
            bg=self.colors['panel_bg'],
            relief=tk.RAISED,
            bd=1
        )
        entry_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # 左侧：Boss信息
        info_frame = tk.Frame(entry_frame, bg=self.colors['panel_bg'])
        info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=8)
        
        # Boss名称
        boss_name = boss_info["boss_name"]
        name_color = self.colors['world_boss'] if boss_info['is_world_boss'] else self.colors['text']
        boss_icon = "🌍" if boss_info['is_world_boss'] else "👹"
        
        name_label = tk.Label(
            info_frame,
            text=f"{boss_icon} {boss_name}",
            bg=self.colors['panel_bg'],
            fg=name_color,
            font=("微软雅黑", 12, "bold"),
            anchor="w"
        )
        name_label.pack(anchor="w")
        
        # 地图名称和难度
        map_text = f"📍 {boss_info['map_name']} | 难度: {boss_info['difficulty']}"
        map_label = tk.Label(
            info_frame,
            text=map_text,
            bg=self.colors['panel_bg'],
            fg=self.colors['map_name'],
            font=("微软雅黑", 9),
            anchor="w"
        )
        map_label.pack(anchor="w")
        
        # 右侧：状态和时间
        status_frame = tk.Frame(entry_frame, bg=self.colors['panel_bg'])
        status_frame.pack(side=tk.RIGHT, padx=10, pady=8)
        
        if is_available:
            status_text = "✅ 已刷新"
            status_color = self.colors['available']
            time_text = "可挑战"
            time_color = self.colors['available']
        else:
            status_text = "❌ 未刷新"
            status_color = self.colors['unavailable']
            time_text = self._format_time(time_until_respawn)
            time_color = self.colors['time']
        
        # 状态标签
        status_label = tk.Label(
            status_frame,
            text=status_text,
            bg=self.colors['panel_bg'],
            fg=status_color,
            font=("微软雅黑", 10, "bold")
        )
        status_label.pack(anchor="e")
        
        # 时间标签
        time_label = tk.Label(
            status_frame,
            text=time_text,
            bg=self.colors['panel_bg'],
            fg=time_color,
            font=("微软雅黑", 11, "bold")
        )
        time_label.pack(anchor="e")
    
    def _start_auto_update(self):
        """启动自动更新"""
        def update_loop():
            while self.is_updating:
                if self.auto_update_var.get():
                    try:
                        self.after(0, self._update_boss_list)
                    except:
                        break
                time.sleep(self.update_interval / 1000.0)
        
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def _toggle_auto_update(self):
        """切换自动更新状态"""
        if self.auto_update_var.get():
            print("✅ Boss状态自动更新已启用")
        else:
            print("⏸️ Boss状态自动更新已暂停")
    
    def _refresh_data(self):
        """手动刷新数据"""
        print("🔄 正在刷新Boss数据...")
        self._load_boss_data()
        self._update_boss_list()
        print("✅ Boss数据刷新完成")
    
    def _on_closing(self):
        """处理窗口关闭事件"""
        self.is_updating = False
        self.destroy()
        print("🔚 Boss刷新状态窗口已关闭")
    
    def show(self):
        """显示窗口"""
        self.deiconify()
        self.lift()
        self._update_boss_list()
    
    def hide(self):
        """隐藏窗口"""
        self.withdraw()


def open_boss_refresh_window(map_manager=None):
    """
    打开Boss刷新状态窗口（在单独线程中）
    
    Args:
        map_manager: 地图管理器实例
    """
    def run_boss_window():
        try:
            import tkinter as tk
            
            # 创建临时根窗口（隐藏）
            root = tk.Tk()
            root.withdraw()
            
            # 创建Boss刷新窗口
            boss_window = BossRefreshWindow(root, map_manager)
            boss_window.show()
            
            # 运行主循环
            root.mainloop()
            
        except Exception as e:
            print(f"❌ 打开Boss刷新窗口失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 在单独线程中运行
    from threading import Thread
    window_thread = Thread(target=run_boss_window)
    window_thread.daemon = True
    window_thread.start()


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.withdraw()
    
    boss_window = BossRefreshWindow(root)
    boss_window.show()
    
    root.mainloop() 