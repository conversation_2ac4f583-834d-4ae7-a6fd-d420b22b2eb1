#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公告面板布局测试

测试公告面板的布局调整，确保分页按钮在底端，内容区域充分利用空间
"""

import sys
import os
import pygame
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from game.ui.announcement_panel import AnnouncementPanel


def test_layout_calculation():
    """测试布局计算"""
    print("\n=== 测试布局计算 ===")
    
    # 初始化pygame
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    # 测试不同尺寸的公告面板
    test_sizes = [
        (400, 300, "小尺寸"),
        (500, 400, "中等尺寸"),
        (600, 500, "大尺寸")
    ]
    
    for width, height, size_name in test_sizes:
        print(f"\n--- {size_name} ({width}x{height}) ---")
        
        # 创建公告面板
        mock_user_message_manager = Mock()
        mock_user_message_manager.get_messages_by_type.return_value = []
        
        panel = AnnouncementPanel(
            screen=screen,
            battle_manager=Mock(),
            position=(0, 0),
            size=(width, height),
            player=Mock(),
            user_message_manager=mock_user_message_manager
        )
        
        # 输出布局信息
        print(f"✅ 面板总大小: {panel.size}")
        print(f"   - 标签页区域: {panel.tab_rect.width}x{panel.tab_rect.height} at ({panel.tab_rect.x}, {panel.tab_rect.y})")
        print(f"   - 内容区域: {panel.content_rect.width}x{panel.content_rect.height} at ({panel.content_rect.x}, {panel.content_rect.y})")
        print(f"   - 导航区域: {panel.nav_rect.width}x{panel.nav_rect.height} at ({panel.nav_rect.x}, {panel.nav_rect.y})")
        print(f"   - 可见消息数: {panel.visible_messages}")
        print(f"   - 每页消息数: {panel.page_size}")
        
        # 验证布局合理性
        assert panel.tab_rect.bottom <= panel.content_rect.top, "标签页应该在内容区域上方"
        assert panel.content_rect.bottom <= panel.nav_rect.top, "内容区域应该在导航区域上方"
        assert panel.nav_rect.bottom <= panel.rect.bottom, "导航区域应该在面板内部"
        
        # 检查导航区域是否在底端
        bottom_margin = panel.rect.bottom - panel.nav_rect.bottom
        print(f"   - 导航区域距离底端: {bottom_margin}px")
        assert bottom_margin <= 10, f"导航区域应该接近底端，当前距离: {bottom_margin}px"
    
    pygame.quit()
    print("\n✅ 所有布局测试通过！")


def test_content_display():
    """测试内容显示"""
    print("\n=== 测试内容显示 ===")
    
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    # 创建公告面板
    mock_user_message_manager = Mock()
    mock_user_message_manager.get_messages_by_type.return_value = []
    
    panel = AnnouncementPanel(
        screen=screen,
        battle_manager=Mock(),
        position=(100, 100),
        size=(500, 400),
        player=Mock(),
        user_message_manager=mock_user_message_manager
    )
    
    # 添加大量测试数据
    print("添加测试数据...")
    for i in range(30):
        panel.add_drop_info(
            f"测试物品{i+1}",
            "测试类型",
            "普通" if i % 3 == 0 else ("稀有" if i % 3 == 1 else "史诗"),
            f"怪物{i+1}",
            f"地图{i % 5 + 1}"
        )
    
    # 切换到掉落标签页
    panel.current_tab = 1
    messages = panel.get_current_tab_messages()
    total_pages = max(1, (len(messages) + panel.page_size - 1) // panel.page_size)
    
    print(f"✅ 测试数据统计:")
    print(f"   - 总消息数: {len(messages)}")
    print(f"   - 每页显示: {panel.page_size}")
    print(f"   - 总页数: {total_pages}")
    print(f"   - 可见行数: {panel.visible_messages}")
    
    # 测试分页
    print(f"\n--- 分页测试 ---")
    for page in range(min(3, total_pages)):
        panel.current_page = page
        start_index = page * panel.page_size
        end_index = min(start_index + panel.page_size, len(messages))
        page_messages = messages[start_index:end_index]
        
        print(f"✅ 第{page+1}页: {len(page_messages)} 条消息")
        
        # 检查是否超出可见范围
        available_lines = (panel.content_rect.height - 10) // panel.message_line_height
        display_count = min(len(page_messages), available_lines)
        
        if len(page_messages) > available_lines:
            print(f"   ⚠️ 警告: 页面消息数({len(page_messages)})超过可见行数({available_lines})")
        else:
            print(f"   ✅ 页面消息数({len(page_messages)})适合可见行数({available_lines})")
    
    pygame.quit()


def test_visual_layout():
    """测试视觉布局（创建一个可视化的测试）"""
    print("\n=== 测试视觉布局 ===")
    
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    pygame.display.set_caption("公告面板布局测试")
    
    # 创建公告面板
    mock_user_message_manager = Mock()
    mock_user_message_manager.get_messages_by_type.return_value = []
    
    panel = AnnouncementPanel(
        screen=screen,
        battle_manager=Mock(),
        position=(50, 50),
        size=(500, 400),
        player=Mock(),
        user_message_manager=mock_user_message_manager
    )
    
    # 添加测试数据
    for i in range(20):
        panel.add_drop_info(f"物品{i+1}", "装备", "普通", f"怪物{i+1}", "测试地图")
    
    panel.add_important_info("测试升级信息", "升级", "重要", (0, 255, 0))
    panel.add_important_info("测试成就信息", "成就", "重要", (255, 215, 0))
    panel.add_system_announcement("测试系统公告", (255, 255, 0))
    
    # 启用调试模式
    panel.debug_mode = True
    
    print("✅ 创建了可视化测试窗口")
    print("   - 面板位置: (50, 50)")
    print("   - 面板大小: 500x400")
    print("   - 已添加测试数据")
    print("   - 可以手动检查布局是否合理")
    
    # 简单的事件循环（用于测试）
    clock = pygame.time.Clock()
    running = True
    test_duration = 3  # 运行3秒
    start_time = pygame.time.get_ticks()
    
    while running and (pygame.time.get_ticks() - start_time) < test_duration * 1000:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            else:
                panel.handle_event(event)
        
        # 清屏
        screen.fill((30, 30, 30))
        
        # 渲染公告面板
        panel.render()
        
        # 添加布局指示线
        pygame.draw.rect(screen, (255, 0, 0), panel.rect, 2)  # 面板边框
        pygame.draw.rect(screen, (0, 255, 0), panel.tab_rect, 1)  # 标签页区域
        pygame.draw.rect(screen, (0, 0, 255), panel.content_rect, 1)  # 内容区域
        pygame.draw.rect(screen, (255, 255, 0), panel.nav_rect, 1)  # 导航区域
        
        # 添加说明文字
        font = pygame.font.SysFont("SimHei", 12)
        legend = [
            "红色: 面板边框",
            "绿色: 标签页区域", 
            "蓝色: 内容区域",
            "黄色: 导航区域"
        ]
        
        for i, text in enumerate(legend):
            color = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)][i]
            text_surface = font.render(text, True, color)
            screen.blit(text_surface, (600, 100 + i * 20))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    print("✅ 视觉测试完成")


def run_all_tests():
    """运行所有布局测试"""
    print("开始测试公告面板布局调整...")
    print("=" * 60)
    
    try:
        test_layout_calculation()
        test_content_display()
        test_visual_layout()
        
        print("\n" + "=" * 60)
        print("🎉 所有布局测试通过！")
        print("\n📝 布局改进总结:")
        print("- ✅ 分页按钮现在位于面板最底端")
        print("- ✅ 内容区域充分利用可用空间")
        print("- ✅ 每页可显示更多消息（12条）")
        print("- ✅ 行高适中，提高可读性")
        print("- ✅ 布局计算准确，无重叠问题")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
