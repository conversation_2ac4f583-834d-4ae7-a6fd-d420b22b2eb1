# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件配置
datas = [('E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\assets', 'game/assets'), ('E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\game\\data', 'game/data'), ('E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\ui', 'ui'), ('E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\logs', 'logs'), ('E:\\BaiduNetdiskDownload\\IDM\\Demo\\新建文件夹\\saves', 'saves')]

# 隐藏导入模块
hiddenimports = ['game.core.game', 'game.core.data_manager', 'game.core.resource_manager', 'game.core.battle_manager', 'game.core.battle_calculator', 'game.core.map_manager', 'game.core.pathfinding', 'game.core.luck_system', 'game.core.log_manager', 'game.core.log_config', 'game.core.user_message_manager', 'game.core.error_message_converter', 'game.core.enemy_image_manager', 'game.core.monster_distribution_manager', 'game.ui.ui_manager', 'game.ui.ui_panel', 'game.ui.start_menu_panel', 'game.ui.character_creation_panel', 'game.ui.inventory_panel', 'game.ui.equipment_panel', 'game.ui.battle_panel', 'game.ui.skill_panel', 'game.ui.map_panel', 'game.ui.info_panel', 'game.ui.log_panel', 'game.ui.rank_panel', 'game.ui.item_slot', 'game.ui.font_manager', 'game.ui.damage_text_manager', 'game.ui.announcement_panel', 'game.ui.auto_potion_panel', 'game.ui.medicine_panel', 'game.ui.warehouse_panel', 'game.ui.boss_refresh_panel', 'game.data.inventory', 'game.data.equipment_loader', 'game.managers.inventory_manager', 'game.managers.equipment_manager', 'game.managers.skill_manager', 'game.managers.save_load_manager', 'game.managers.quest_manager', 'game.managers.checkin_manager', 'game.managers.points_shop_manager', 'game.models.player_refactored', 'game.models.enemy', 'game.models.battle_entity', 'game.models.character_stats', 'game.models.rank_manager', 'game.models.skill_loader', 'game.models.monster_data_loader', 'game.systems.item_generator', 'ui.checkin_panel', 'ui.points_shop_panel', 'pygame', 'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'pyperclip', 'PIL', 'PIL.Image', 'PIL.ImageTk', 'email', 'email.mime', 'email.mime.text', 'pkg_resources', 'setuptools']

# 排除的模块
excludes = ['numpy', 'scipy', 'matplotlib', 'pandas', 'sklearn', 'tensorflow', 'torch', 'pytest', 'doctest', 'pydoc', 'urllib3', 'requests']

a = Analysis(
    [r'E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\main.py'],
    pathex=[r'E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹'],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='萝卜传奇_控制台版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r'E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\assets\images\icon.png',
)
