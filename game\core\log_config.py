# -*- coding: utf-8 -*-
"""
日志系统配置文件

提供日志系统的配置管理，包括日志级别、文件路径、格式等设置
"""

import os
from enum import Enum
from typing import Dict, Any


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogConfig:
    """日志系统配置类"""
    
    def __init__(self):
        """初始化日志配置"""
        # 基础配置
        self.enabled = True
        self.console_output = True
        self.file_output = True
        
        # 日志级别配置
        self.min_level = LogLevel.DEBUG
        self.console_level = LogLevel.INFO
        self.file_level = LogLevel.DEBUG
        
        # 文件配置
        self.log_dir = "logs"
        self.log_filename = "game.log"
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.backup_count = 5
        
        # 格式配置
        self.timestamp_format = "%Y-%m-%d %H:%M:%S"
        self.log_format = "[{timestamp}] [{level}] [{source}] {message}"
        
        # 用户消息配置
        self.user_message_max_count = 100
        self.user_message_auto_cleanup = True
        self.user_message_cleanup_interval = 300  # 5分钟
        
        # 战斗日志配置
        self.battle_log_max_count = 200
        self.battle_log_auto_save = True
        self.battle_log_save_interval = 60  # 1分钟
        
        # 错误转换配置
        self.error_conversion_enabled = True
        self.auto_convert_errors = True
        self.show_technical_details = False  # 是否向用户显示技术细节
        
        # 性能配置
        self.async_logging = True
        self.buffer_size = 1000
        self.flush_interval = 5  # 秒
        
        # 开发者模式配置
        self.developer_mode = False
        self.debug_output = False
        self.trace_enabled = False
        
        # 确保日志目录存在
        self._ensure_log_directory()
    
    def _ensure_log_directory(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            try:
                os.makedirs(self.log_dir, exist_ok=True)
            except Exception as e:
                print(f"创建日志目录失败: {e}")
                self.file_output = False
    
    def get_log_file_path(self) -> str:
        """获取日志文件完整路径"""
        return os.path.join(self.log_dir, self.log_filename)
    
    def is_level_enabled(self, level: LogLevel) -> bool:
        """检查指定日志级别是否启用"""
        level_order = {
            LogLevel.DEBUG: 0,
            LogLevel.INFO: 1,
            LogLevel.WARNING: 2,
            LogLevel.ERROR: 3,
            LogLevel.CRITICAL: 4
        }
        return level_order.get(level, 0) >= level_order.get(self.min_level, 0)
    
    def should_output_to_console(self, level: LogLevel) -> bool:
        """检查是否应该输出到控制台"""
        if not self.console_output:
            return False
        
        level_order = {
            LogLevel.DEBUG: 0,
            LogLevel.INFO: 1,
            LogLevel.WARNING: 2,
            LogLevel.ERROR: 3,
            LogLevel.CRITICAL: 4
        }
        return level_order.get(level, 0) >= level_order.get(self.console_level, 0)
    
    def should_output_to_file(self, level: LogLevel) -> bool:
        """检查是否应该输出到文件"""
        if not self.file_output:
            return False
        
        level_order = {
            LogLevel.DEBUG: 0,
            LogLevel.INFO: 1,
            LogLevel.WARNING: 2,
            LogLevel.ERROR: 3,
            LogLevel.CRITICAL: 4
        }
        return level_order.get(level, 0) >= level_order.get(self.file_level, 0)
    
    def format_log_message(self, level: LogLevel, source: str, message: str, timestamp: str = None) -> str:
        """格式化日志消息"""
        if timestamp is None:
            from datetime import datetime
            timestamp = datetime.now().strftime(self.timestamp_format)
        
        return self.log_format.format(
            timestamp=timestamp,
            level=level.value,
            source=source,
            message=message
        )
    
    def update_config(self, config_dict: Dict[str, Any]):
        """更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        # 重新确保日志目录存在
        self._ensure_log_directory()
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        config_dict = {}
        for attr_name in dir(self):
            if not attr_name.startswith('_') and not callable(getattr(self, attr_name)):
                attr_value = getattr(self, attr_name)
                if isinstance(attr_value, LogLevel):
                    config_dict[attr_name] = attr_value.value
                else:
                    config_dict[attr_name] = attr_value
        return config_dict
    
    def load_from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                # 处理枚举类型
                if key in ['min_level', 'console_level', 'file_level']:
                    if isinstance(value, str):
                        try:
                            setattr(self, key, LogLevel(value))
                        except ValueError:
                            pass  # 忽略无效的枚举值
                    else:
                        setattr(self, key, value)
                else:
                    setattr(self, key, value)
        
        # 重新确保日志目录存在
        self._ensure_log_directory()


# 全局配置实例
_global_config = None


def get_log_config() -> LogConfig:
    """获取全局日志配置实例"""
    global _global_config
    if _global_config is None:
        _global_config = LogConfig()
    return _global_config


def set_log_config(config: LogConfig):
    """设置全局日志配置实例"""
    global _global_config
    _global_config = config


def reset_log_config():
    """重置日志配置为默认值"""
    global _global_config
    _global_config = LogConfig()


# 预定义配置模板
class LogConfigTemplates:
    """日志配置模板"""
    
    @staticmethod
    def development() -> LogConfig:
        """开发环境配置"""
        config = LogConfig()
        config.developer_mode = True
        config.debug_output = True
        config.trace_enabled = True
        config.min_level = LogLevel.DEBUG
        config.console_level = LogLevel.DEBUG
        config.show_technical_details = True
        return config
    
    @staticmethod
    def production() -> LogConfig:
        """生产环境配置"""
        config = LogConfig()
        config.developer_mode = False
        config.debug_output = False
        config.trace_enabled = False
        config.min_level = LogLevel.INFO
        config.console_level = LogLevel.WARNING
        config.show_technical_details = False
        return config
    
    @staticmethod
    def testing() -> LogConfig:
        """测试环境配置"""
        config = LogConfig()
        config.developer_mode = True
        config.debug_output = True
        config.min_level = LogLevel.DEBUG
        config.console_level = LogLevel.INFO
        config.file_output = False  # 测试时不写文件
        return config
    
    @staticmethod
    def minimal() -> LogConfig:
        """最小化配置"""
        config = LogConfig()
        config.console_output = False
        config.file_output = False
        config.min_level = LogLevel.ERROR
        config.user_message_max_count = 50
        config.battle_log_max_count = 100
        return config