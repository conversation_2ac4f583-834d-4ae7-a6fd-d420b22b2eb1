# -*- coding: utf-8 -*-
"""
技能管理器
负责管理玩家的技能系统，包括技能学习、升级、使用等功能
"""

from typing import Dict, List, Optional, Any
import time
import json


class SkillManager:
    """
    技能管理器类
    负责管理玩家的技能系统
    """
    
    def __init__(self):
        """
        初始化技能管理器
        """
        # 已学习的技能列表
        self.learned_skills: List[Dict[str, Any]] = []
        
        # 技能冷却时间记录
        self.skill_cooldowns: Dict[str, float] = {}
        
        # 技能经验值
        self.skill_experience: Dict[str, int] = {}

        # 🔧 新增：被动技能属性加成缓存
        self.passive_skill_bonuses: Dict[str, Any] = {}
        self._passive_bonuses_cache_dirty: bool = True
    
    def learn_skill(self, skill_data: Dict[str, Any], player) -> bool:
        """
        学习技能
        
        参数:
            skill_data: 技能数据
            player: 玩家对象
            
        返回:
            bool: 是否成功学习
        """
        if not skill_data:
            return False
        
        skill_name = skill_data.get('name')
        if not skill_name:
            return False
        
        # 检查是否已经学会该技能
        if self.has_skill(skill_name):
            return False
        
        # 检查职业限制
        if not self._check_class_requirement(skill_data, player):
            return False
        
        # 检查等级限制
        if not self._check_level_requirement(skill_data, player):
            return False
        
        # 检查前置技能
        if not self._check_prerequisite_skills(skill_data):
            return False
        
        # 🔧 新增：检查技能书要求
        if not self._check_skill_book_requirement(skill_name, player):
            return False
        
        # 🔧 新增：消耗技能书
        if not self._consume_skill_book(skill_name, player):
            return False
        
        # 学习技能
        new_skill = skill_data.copy()
        new_skill['level'] = 1
        new_skill['learned_at'] = time.time()
        
        self.learned_skills.append(new_skill)
        self.skill_experience[skill_name] = 0
        
        # 🔧 新增：学习技能后重新计算被动技能加成
        self._passive_bonuses_cache_dirty = True

        return True

    def upgrade_skill(self, skill_name: str, player) -> bool:
        """
        升级技能
        
        参数:
            skill_name: 技能名称
            player: 玩家对象
            
        返回:
            bool: 是否成功升级
        """
        skill = self.get_skill(skill_name)
        if not skill:
            return False
        
        current_level = skill.get('level', 1)
        max_level = skill.get('max_level', 10)
        
        # 检查是否已达到最大等级
        if current_level >= max_level:
            return False
        
        # 检查升级所需经验
        required_exp = self._calculate_required_exp(skill_name, current_level + 1)
        current_exp = self.skill_experience.get(skill_name, 0)
        
        if current_exp < required_exp:
            return False
        
        # 检查升级所需等级
        required_player_level = skill.get('level_requirement', {}).get(current_level + 1, 0)
        if player.level < required_player_level:
            return False
        
        # 升级技能
        skill['level'] = current_level + 1
        self.skill_experience[skill_name] -= required_exp

        # 🔧 新增：升级技能后重新计算被动技能加成
        self._passive_bonuses_cache_dirty = True

        return True
    
    def use_skill(self, skill_name: str, target=None, player=None) -> Dict[str, Any]:
        """
        使用技能
        
        参数:
            skill_name: 技能名称
            target: 目标对象
            player: 玩家对象
            
        返回:
            技能使用结果字典
        """
        result = {
            'success': False,
            'message': '',
            'effects': {},
            'damage': 0,
            'healing': 0
        }
        
        skill = self.get_skill(skill_name)
        if not skill:
            result['message'] = f'未学会技能：{skill_name}'
            return result
        
        # 检查冷却时间
        if not self._check_cooldown(skill_name):
            cooldown_remaining = self._get_cooldown_remaining(skill_name)
            result['message'] = f'技能冷却中，剩余时间：{cooldown_remaining:.1f}秒'
            return result
        
        # 检查魔法值消耗
        mp_cost = skill.get('mp_cost', 0)
        if player and hasattr(player, 'current_mp'):
            if player.current_mp < mp_cost:
                result['message'] = '魔法值不足'
                return result
        
        # 执行技能效果
        skill_type = skill.get('type', 'active')
        skill_category = skill.get('category', 'attack')
        
        if skill_category == 'attack':
            result = self._execute_attack_skill(skill, target, player)
        elif skill_category == 'heal':
            result = self._execute_heal_skill(skill, target, player)
        elif skill_category == 'buff':
            result = self._execute_buff_skill(skill, target, player)
        elif skill_category == 'debuff':
            result = self._execute_debuff_skill(skill, target, player)
        elif skill_type == '召唤':
            # 🔧 新增：召唤技能支持
            result = self.summon_creature(player, skill)
        
        # 如果技能使用成功
        if result['success']:
            # 消耗魔法值
            if player and hasattr(player, 'current_mp'):
                player.current_mp = max(0, player.current_mp - mp_cost)
            
            # 设置冷却时间
            cooldown = skill.get('cooldown', 0)
            if cooldown > 0:
                self.skill_cooldowns[skill_name] = time.time() + cooldown
            
            # 增加技能经验
            self._add_skill_experience(skill_name, 1)
        
        return result
    
    def _execute_attack_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行攻击技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 计算技能伤害
        base_damage = skill.get('base_damage', 0)
        damage_multiplier = skill.get('damage_multiplier', 1.0)
        skill_level = skill.get('level', 1)
        
        # 基础伤害计算
        if player:
            # 根据技能类型使用不同的攻击力
            skill_attack_type = skill.get('attack_type', 'physical')
            if skill_attack_type == 'physical':
                player_attack = (getattr(player, '攻击下限', 0) + getattr(player, '攻击上限', 0)) / 2
            elif skill_attack_type == 'magic':
                player_attack = (getattr(player, '魔法攻击下限', 0) + getattr(player, '魔法攻击上限', 0)) / 2
            elif skill_attack_type == 'tao':
                player_attack = (getattr(player, '道术攻击下限', 0) + getattr(player, '道术攻击上限', 0)) / 2
            else:
                player_attack = 0

            # 🔧 新增：应用被动技能伤害加成
            damage_multiplier_bonus, extra_damage = self.get_skill_damage_bonus(player)

            # 计算基础伤害
            base_total_damage = (base_damage + player_attack * damage_multiplier) * (1 + skill_level * 0.1)

            # 应用被动技能加成
            total_damage = int(base_total_damage * damage_multiplier_bonus + extra_damage)
        else:
            total_damage = int(base_damage * (1 + skill_level * 0.1))
        
        result['damage'] = total_damage
        result['message'] = f'使用{skill["name"]}造成{total_damage}点伤害'
        
        return result
    
    def _execute_heal_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行治疗技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 计算治疗量
        base_healing = skill.get('base_healing', 0)
        healing_multiplier = skill.get('healing_multiplier', 1.0)
        skill_level = skill.get('level', 1)
        
        if player:
            # 治疗技能通常基于道术攻击力
            tao_attack = (getattr(player, '道术攻击下限', 0) + getattr(player, '道术攻击上限', 0)) / 2
            total_healing = int((base_healing + tao_attack * healing_multiplier) * (1 + skill_level * 0.1))
        else:
            total_healing = int(base_healing * (1 + skill_level * 0.1))
        
        result['healing'] = total_healing
        result['message'] = f'使用{skill["name"]}恢复{total_healing}点生命值'
        
        return result
    
    def _execute_buff_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行增益技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 获取增益效果
        buff_effects = skill.get('buff_effects', {})
        duration = skill.get('duration', 60)  # 默认60秒
        
        result['effects'] = {
            'type': 'buff',
            'effects': buff_effects,
            'duration': duration
        }
        result['message'] = f'使用{skill["name"]}获得增益效果'
        
        return result
    
    def _execute_debuff_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行减益技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 获取减益效果
        debuff_effects = skill.get('debuff_effects', {})
        duration = skill.get('duration', 30)  # 默认30秒
        
        result['effects'] = {
            'type': 'debuff',
            'effects': debuff_effects,
            'duration': duration
        }
        result['message'] = f'使用{skill["name"]}对目标施加减益效果'
        
        return result
    
    def _check_class_requirement(self, skill_data: Dict[str, Any], player) -> bool:
        """
        检查职业限制
        
        参数:
            skill_data: 技能数据
            player: 玩家对象
            
        返回:
            bool: 是否满足职业要求
        """
        required_class = skill_data.get('required_class')
        if not required_class:
            return True
        
        if isinstance(required_class, str):
            return player.character_class == required_class
        elif isinstance(required_class, list):
            return player.character_class in required_class
        
        return False
    
    def _check_level_requirement(self, skill_data: Dict[str, Any], player) -> bool:
        """
        检查等级限制
        
        参数:
            skill_data: 技能数据
            player: 玩家对象
            
        返回:
            bool: 是否满足等级要求
        """
        required_level = skill_data.get('required_level', 1)
        return player.level >= required_level
    
    def _check_prerequisite_skills(self, skill_data: Dict[str, Any]) -> bool:
        """
        检查前置技能
        
        参数:
            skill_data: 技能数据
            
        返回:
            bool: 是否满足前置技能要求
        """
        prerequisites = skill_data.get('prerequisites', [])
        if not prerequisites:
            return True
        
        for prereq in prerequisites:
            prereq_name = prereq.get('name')
            prereq_level = prereq.get('level', 1)
            
            skill = self.get_skill(prereq_name)
            if not skill or skill.get('level', 0) < prereq_level:
                return False
        
        return True
    
    def _check_cooldown(self, skill_name: str) -> bool:
        """
        检查技能冷却时间
        
        参数:
            skill_name: 技能名称
            
        返回:
            bool: 是否可以使用
        """
        if skill_name not in self.skill_cooldowns:
            return True
        
        return time.time() >= self.skill_cooldowns[skill_name]
    
    def _get_cooldown_remaining(self, skill_name: str) -> float:
        """
        获取技能剩余冷却时间
        
        参数:
            skill_name: 技能名称
            
        返回:
            剩余冷却时间（秒）
        """
        if skill_name not in self.skill_cooldowns:
            return 0.0
        
        remaining = self.skill_cooldowns[skill_name] - time.time()
        return max(0.0, remaining)
    
    def _calculate_required_exp(self, skill_name: str, target_level: int) -> int:
        """
        计算技能升级所需经验
        
        参数:
            skill_name: 技能名称
            target_level: 目标等级
            
        返回:
            所需经验值
        """
        # 简单的经验计算公式
        return target_level * 100
    
    def _add_skill_experience(self, skill_name: str, exp: int):
        """
        增加技能经验
        
        参数:
            skill_name: 技能名称
            exp: 经验值
        """
        if skill_name not in self.skill_experience:
            self.skill_experience[skill_name] = 0
        
        self.skill_experience[skill_name] += exp
    
    def has_skill(self, skill_name: str) -> bool:
        """
        检查是否拥有技能
        
        参数:
            skill_name: 技能名称
            
        返回:
            bool: 是否拥有该技能
        """
        return any(skill.get('name') == skill_name for skill in self.learned_skills)
    
    def get_skill(self, skill_name: str) -> Optional[Dict[str, Any]]:
        """
        获取技能信息
        
        参数:
            skill_name: 技能名称
            
        返回:
            技能数据或None
        """
        for skill in self.learned_skills:
            if skill.get('name') == skill_name:
                return skill.copy()
        return None
    
    def get_all_skills(self) -> List[Dict[str, Any]]:
        """
        获取所有已学技能
        
        返回:
            技能列表
        """
        return self.learned_skills.copy()

    def get_skills_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        获取指定类别的技能

        参数:
            category: 技能类别

        返回:
            技能列表
        """
        return [skill for skill in self.learned_skills if skill.get('category') == category]

    def get_passive_skill_bonuses(self) -> Dict[str, Any]:
        """
        获取所有被动技能的属性加成

        返回:
            属性加成字典
        """
        # 🔧 修复：强制重新计算，确保获取最新的技能加成
        self._calculate_passive_skill_bonuses()

        return self.passive_skill_bonuses.copy()

    def _calculate_passive_skill_bonuses(self):
        """
        计算被动技能的属性加成
        """
        self.passive_skill_bonuses = {}

        for skill in self.learned_skills:
            # 只处理被动技能
            if skill.get('type') != '被动':
                continue

            skill_level = skill.get('level', 0)
            if skill_level <= 0:
                continue

            # 获取技能效果
            effects = skill.get('effects', [])
            if not effects or len(effects) < skill_level:
                continue

            # 获取当前等级的效果（索引从0开始，所以是level-1）
            current_effect = effects[skill_level - 1]
            effect_type = current_effect.get('type')
            effect_value = current_effect.get('value', 0)

            # 根据效果类型应用属性加成
            if effect_type == 'accuracy':
                # 准确度加成
                self.passive_skill_bonuses['准确'] = self.passive_skill_bonuses.get('准确', 0) + effect_value
            elif effect_type == 'damage_percent':
                # 伤害百分比加成
                self.passive_skill_bonuses['伤害加成'] = self.passive_skill_bonuses.get('伤害加成', 0) + effect_value / 100.0
            elif effect_type == 'extra_damage':
                # 额外伤害
                self.passive_skill_bonuses['额外伤害'] = self.passive_skill_bonuses.get('额外伤害', 0) + effect_value
            elif effect_type == 'defense_boost':
                # 防御力加成
                self.passive_skill_bonuses['防御力'] = self.passive_skill_bonuses.get('防御力', 0) + effect_value
            elif effect_type == 'hp_boost':
                # 生命值加成
                self.passive_skill_bonuses['生命值'] = self.passive_skill_bonuses.get('生命值', 0) + effect_value
            elif effect_type == 'mp_boost':
                # 魔法值加成
                self.passive_skill_bonuses['魔法值'] = self.passive_skill_bonuses.get('魔法值', 0) + effect_value
            elif effect_type == 'critical_rate':
                # 暴击率加成
                self.passive_skill_bonuses['暴击率'] = self.passive_skill_bonuses.get('暴击率', 0) + effect_value / 100.0
            elif effect_type == 'attack_speed':
                # 攻击速度加成
                self.passive_skill_bonuses['攻速'] = self.passive_skill_bonuses.get('攻速', 0) + effect_value

            print(f"🎯 被动技能 {skill.get('name')} Lv.{skill_level}: {effect_type} +{effect_value}")

        print(f"✅ 被动技能属性加成计算完成: {self.passive_skill_bonuses}")

    def apply_passive_skills_to_player(self, player):
        """
        将被动技能效果应用到玩家属性

        参数:
            player: 玩家对象
        """
        bonuses = self.get_passive_skill_bonuses()

        if not bonuses:
            return

        # 应用属性加成到玩家
        for attr_name, bonus_value in bonuses.items():
            if hasattr(player, attr_name):
                current_value = getattr(player, attr_name, 0)
                if attr_name in ['伤害加成', '暴击率']:
                    # 百分比加成
                    setattr(player, attr_name, current_value + bonus_value)
                else:
                    # 数值加成
                    setattr(player, attr_name, current_value + bonus_value)

                print(f"🔧 应用被动技能加成: {attr_name} +{bonus_value}")

    def get_skill_damage_bonus(self, player) -> float:
        """
        获取技能伤害加成倍率

        参数:
            player: 玩家对象

        返回:
            伤害加成倍率
        """
        bonuses = self.get_passive_skill_bonuses()
        damage_bonus = bonuses.get('伤害加成', 0)
        extra_damage = bonuses.get('额外伤害', 0)

        # 计算总伤害倍率
        total_multiplier = 1.0 + damage_bonus

        return total_multiplier, extra_damage

    def summon_creature(self, player, skill: Dict[str, Any]) -> Dict[str, Any]:
        """
        召唤生物

        参数:
            player: 玩家对象
            skill: 召唤技能数据

        返回:
            召唤结果
        """
        # 检查MP消耗
        mp_cost = skill.get('mana_cost', 0)
        if player.魔法值 < mp_cost:
            return {'success': False, 'message': 'MP不足'}

        # 检查召唤数量限制
        if len(player.summoned_creatures) >= player.max_summons:
            return {'success': False, 'message': f'召唤物数量已达上限({player.max_summons})'}

        skill_level = skill.get('level', 1)
        effects = skill.get('effects', [])

        if not effects or len(effects) < skill_level:
            return {'success': False, 'message': '技能效果数据错误'}

        # 获取当前等级的召唤效果
        summon_effect = effects[skill_level - 1]
        creature_type = summon_effect.get('value', 'skeleton')
        duration = summon_effect.get('duration', -1)

        # 创建召唤物（等级基于玩家等级）
        creature_level = max(1, player.level // 2)
        creature = self._create_summoned_creature(creature_type, creature_level, duration)

        # 🔧 新增：设置召唤物初始位置（在玩家附近）
        self._set_summon_position(creature, player, len(player.summoned_creatures))

        # 添加到玩家的召唤物列表
        player.summoned_creatures.append(creature)

        # 消耗MP
        player.魔法值 -= mp_cost

        return {
            'success': True,
            'creature': creature,
            'mp_cost': mp_cost,
            'message': f'{skill["name"]} 召唤了 {creature["name"]} Lv.{creature["level"]}'
        }

    def _create_summoned_creature(self, creature_type: str, level: int, duration: int = -1) -> Dict[str, Any]:
        """
        创建召唤物数据

        参数:
            creature_type: 召唤物类型
            level: 召唤物等级
            duration: 持续时间（-1表示永久）

        返回:
            召唤物数据字典
        """
        # 根据类型设置基础属性
        if creature_type == "skeleton":
            name = "骷髅"
            hp = 50 + level * 10
            attack = 5 + level * 2
            defense = 2 + level
        elif creature_type == "beast":
            name = "神兽"
            hp = 80 + level * 15
            attack = 8 + level * 3
            defense = 5 + level * 2
        else:
            name = "未知召唤物"
            hp = 30
            attack = 3
            defense = 1

        return {
            'name': name,
            'type': creature_type,
            'level': level,
            'hp': hp,
            'max_hp': hp,
            'attack': attack,
            'defense': defense,
            'duration': duration,
            'remaining_time': duration,
            'position': None,  # 🔧 新增：召唤物位置，将在召唤时设置
            'created_time': __import__('time').time()  # 🔧 新增：创建时间
        }

    def dismiss_creature(self, player, creature_index: int) -> bool:
        """
        解散召唤物

        参数:
            player: 玩家对象
            creature_index: 召唤物索引

        返回:
            是否成功解散
        """
        if 0 <= creature_index < len(player.summoned_creatures):
            dismissed = player.summoned_creatures.pop(creature_index)
            print(f"✅ 解散了 {dismissed['name']} Lv.{dismissed['level']}")
            return True
        return False

    def dismiss_all_creatures(self, player) -> int:
        """
        解散所有召唤物

        参数:
            player: 玩家对象

        返回:
            解散的召唤物数量
        """
        count = len(player.summoned_creatures)
        player.summoned_creatures.clear()
        print(f"✅ 解散了所有召唤物 ({count}个)")
        return count

    def _set_summon_position(self, creature, player, summon_index):
        """
        设置召唤物位置

        参数:
            creature: 召唤物数据
            player: 玩家对象
            summon_index: 召唤物索引
        """
        # 获取玩家当前位置
        if hasattr(player, 'position') and player.position:
            player_x, player_y = player.position
        else:
            # 默认位置
            player_x, player_y = 400, 300

        # 计算召唤物位置（围绕玩家分布）
        import math
        angle = (summon_index * 2 * math.pi / max(1, player.max_summons))
        offset_distance = 50  # 距离玩家的偏移距离

        summon_x = player_x + offset_distance * math.cos(angle)
        summon_y = player_y + offset_distance * math.sin(angle)

        creature['position'] = (summon_x, summon_y)

    def update_summon_positions(self, player):
        """
        更新所有召唤物位置（跟随玩家移动）

        参数:
            player: 玩家对象
        """
        if not hasattr(player, 'summoned_creatures') or not player.summoned_creatures:
            return

        for i, creature in enumerate(player.summoned_creatures):
            self._set_summon_position(creature, player, i)
    
    def get_skill_cooldowns(self) -> Dict[str, float]:
        """
        获取所有技能的冷却时间
        
        返回:
            技能冷却时间字典
        """
        current_time = time.time()
        cooldowns = {}
        
        for skill_name, cooldown_end in self.skill_cooldowns.items():
            remaining = max(0.0, cooldown_end - current_time)
            if remaining > 0:
                cooldowns[skill_name] = remaining
        
        return cooldowns
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式（用于保存）
        
        返回:
            字典格式的技能数据
        """
        return {
            'learned_skills': self.learned_skills,
            'skill_cooldowns': self.skill_cooldowns,
            'skill_experience': self.skill_experience
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """
        从字典格式加载（用于读取存档）
        
        参数:
            data: 字典格式的技能数据
        """
        self.learned_skills = data.get('learned_skills', [])
        self.skill_cooldowns = data.get('skill_cooldowns', {})
        self.skill_experience = data.get('skill_experience', {})
    
    def _check_skill_book_requirement(self, skill_name: str, player) -> bool:
        """
        检查技能书要求
        
        参数:
            skill_name: 技能名称
            player: 玩家对象
            
        返回:
            bool: 是否拥有对应的技能书
        """
        # 🔧 修改：所有技能都需要技能书，不再有例外
        # 检查玩家是否拥有对应的技能书
        if hasattr(player, 'inventory_manager') and player.inventory_manager:
            skill_book_name = f"{skill_name}"  # 技能书名称与技能名称相同
            return player.inventory_manager.has_item(skill_book_name, 1)
        
        return False
    
    def _consume_skill_book(self, skill_name: str, player) -> bool:
        """
        消耗技能书
        
        参数:
            skill_name: 技能名称
            player: 玩家对象
            
        返回:
            bool: 是否成功消耗
        """
        # 🔧 修改：所有技能都需要消耗技能书，不再有例外
        # 消耗对应的技能书
        if hasattr(player, 'inventory_manager') and player.inventory_manager:
            skill_book_name = f"{skill_name}"  # 技能书名称与技能名称相同
            success = player.inventory_manager.remove_item(skill_book_name, 1)
            if success:
                print(f"📚 消耗技能书: {skill_book_name}")
                return True
            else:
                print(f"❌ 技能书不足: {skill_book_name}")
                return False
        
        return False