#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玩家死亡恢复系统测试

测试玩家死亡后的地图怪物刷新和危险怪物避让功能
"""

import sys
import os
import time
import unittest
from unittest.mock import Mock, MagicMock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from game.core.battle_manager import BattleManager
from game.models.enemy import Enemy


class TestDeathRecoverySystem(unittest.TestCase):
    """死亡恢复系统测试类"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建战斗管理器
        self.battle_manager = BattleManager(debug_console_output=True)
        
        # 模拟玩家对象
        self.mock_player = Mock()
        self.mock_player.name = "测试玩家"
        self.mock_player.level = 10
        self.mock_player.max_hp = 100
        self.mock_player.current_hp = 100
        self.mock_player.x = 25
        self.mock_player.y = 25
        
        # 模拟地图管理器
        self.mock_map_manager = Mock()
        self.mock_current_map = Mock()
        self.mock_current_map.name = "测试地图"
        self.mock_current_map.active_enemies = []
        
        # 设置地图管理器
        self.mock_map_manager.current_map = self.mock_current_map
        self.mock_map_manager.data_manager = Mock()
        
        # 将模拟对象设置到战斗管理器
        self.battle_manager.player_instance = self.mock_player
        self.battle_manager.map_manager = self.mock_map_manager
    
    def create_mock_enemy(self, name="测试怪物", level=5, position=(10, 10)):
        """创建模拟怪物"""
        enemy = Mock()
        enemy.name = name
        enemy.level = level
        enemy.position = position
        enemy.hp = 50
        enemy.current_hp = 50
        enemy.enemy_type = "普通"
        return enemy
    
    def test_record_death_causing_monster(self):
        """测试记录导致死亡的怪物"""
        print("\n=== 测试记录死亡怪物 ===")
        
        # 创建一个怪物
        dangerous_monster = self.create_mock_enemy("危险怪物", 8, (15, 15))
        
        # 记录死亡怪物
        self.battle_manager._record_death_causing_monster(dangerous_monster)
        
        # 验证记录是否正确
        self.assertTrue(hasattr(self.battle_manager, '_death_causing_monsters'))
        self.assertEqual(len(self.battle_manager._death_causing_monsters), 1)
        
        record = self.battle_manager._death_causing_monsters[0]
        self.assertEqual(record['name'], "危险怪物")
        self.assertEqual(record['level'], 8)
        self.assertEqual(record['position'], (15, 15))
        
        print(f"✅ 成功记录危险怪物: {record['name']} (等级{record['level']})")
    
    def test_is_dangerous_monster(self):
        """测试危险怪物判断"""
        print("\n=== 测试危险怪物判断 ===")
        
        # 创建怪物
        dangerous_monster = self.create_mock_enemy("危险怪物", 8, (15, 15))
        safe_monster = self.create_mock_enemy("安全怪物", 3, (20, 20))
        similar_monster = self.create_mock_enemy("危险怪物", 9, (16, 16))  # 同名但等级相近
        
        # 记录危险怪物
        self.battle_manager._record_death_causing_monster(dangerous_monster)
        
        # 测试判断
        self.assertTrue(self.battle_manager._is_dangerous_monster(dangerous_monster))
        self.assertFalse(self.battle_manager._is_dangerous_monster(safe_monster))
        self.assertTrue(self.battle_manager._is_dangerous_monster(similar_monster))  # 等级相近的同名怪物
        
        print("✅ 危险怪物判断功能正常")
        print(f"   - 危险怪物: {dangerous_monster.name} -> 危险")
        print(f"   - 安全怪物: {safe_monster.name} -> 安全")
        print(f"   - 相似怪物: {similar_monster.name} -> 危险（等级相近）")
    
    def test_find_safe_enemy_from_map(self):
        """测试寻找安全怪物"""
        print("\n=== 测试寻找安全怪物 ===")
        
        # 创建多个怪物
        dangerous_monster = self.create_mock_enemy("危险怪物", 8, (15, 15))
        safe_monster1 = self.create_mock_enemy("安全怪物1", 3, (20, 20))
        safe_monster2 = self.create_mock_enemy("安全怪物2", 4, (30, 30))
        
        # 设置地图怪物
        self.mock_current_map.active_enemies = [dangerous_monster, safe_monster1, safe_monster2]
        
        # 记录危险怪物
        self.battle_manager._record_death_causing_monster(dangerous_monster)
        
        # 寻找安全怪物
        safe_enemy, distance = self.battle_manager._find_safe_enemy_from_map(
            self.mock_current_map, (25, 25)
        )
        
        # 验证结果
        self.assertIsNotNone(safe_enemy)
        self.assertIn(safe_enemy.name, ["安全怪物1", "安全怪物2"])
        self.assertNotEqual(safe_enemy.name, "危险怪物")
        
        print(f"✅ 成功找到安全怪物: {safe_enemy.name} (距离: {distance:.1f})")
    
    def test_refresh_map_monsters_on_revival(self):
        """测试复活时刷新地图怪物"""
        print("\n=== 测试复活时刷新怪物 ===")
        
        # 设置初始怪物
        old_monsters = [
            self.create_mock_enemy("旧怪物1", 5, (10, 10)),
            self.create_mock_enemy("旧怪物2", 6, (20, 20))
        ]
        self.mock_current_map.active_enemies = old_monsters.copy()
        
        # 模拟spawn_monsters方法
        def mock_spawn_monsters(data_manager):
            # 清空并生成新怪物
            self.mock_current_map.active_enemies.clear()
            new_monsters = [
                self.create_mock_enemy("新怪物1", 4, (12, 12)),
                self.create_mock_enemy("新怪物2", 7, (22, 22)),
                self.create_mock_enemy("新怪物3", 5, (32, 32))
            ]
            self.mock_current_map.active_enemies.extend(new_monsters)
        
        self.mock_current_map.spawn_monsters = mock_spawn_monsters
        
        # 执行刷新
        self.battle_manager._refresh_map_monsters_on_revival()
        
        # 验证结果
        self.assertEqual(len(self.mock_current_map.active_enemies), 3)
        monster_names = [monster.name for monster in self.mock_current_map.active_enemies]
        self.assertIn("新怪物1", monster_names)
        self.assertIn("新怪物2", monster_names)
        self.assertIn("新怪物3", monster_names)
        self.assertNotIn("旧怪物1", monster_names)
        self.assertNotIn("旧怪物2", monster_names)
        
        print(f"✅ 成功刷新地图怪物: {len(self.mock_current_map.active_enemies)} 个新怪物")
        for monster in self.mock_current_map.active_enemies:
            print(f"   - {monster.name} (等级{monster.level}) at {monster.position}")
    
    def test_complete_death_recovery_flow(self):
        """测试完整的死亡恢复流程"""
        print("\n=== 测试完整死亡恢复流程 ===")
        
        # 1. 设置战斗场景
        dangerous_monster = self.create_mock_enemy("强力怪物", 10, (15, 15))
        self.battle_manager.defender = dangerous_monster
        
        # 2. 模拟玩家死亡
        print("1. 模拟玩家死亡...")
        self.battle_manager.player_is_dead = True
        self.battle_manager.death_time = time.time()
        self.battle_manager.revival_start_time = time.time()
        self.battle_manager._record_death_causing_monster(dangerous_monster)
        
        # 验证死亡状态
        self.assertTrue(self.battle_manager.player_is_dead)
        self.assertTrue(hasattr(self.battle_manager, '_death_causing_monsters'))
        print("   ✅ 玩家死亡状态设置完成")
        
        # 3. 设置地图刷新模拟
        def mock_spawn_monsters(data_manager):
            self.mock_current_map.active_enemies.clear()
            new_monsters = [
                self.create_mock_enemy("新怪物A", 3, (10, 10)),
                self.create_mock_enemy("新怪物B", 4, (20, 20))
            ]
            self.mock_current_map.active_enemies.extend(new_monsters)
        
        self.mock_current_map.spawn_monsters = mock_spawn_monsters
        
        # 4. 模拟复活
        print("2. 模拟玩家复活...")
        self.battle_manager._revive_player()
        
        # 验证复活状态
        self.assertFalse(self.battle_manager.player_is_dead)
        self.assertEqual(self.mock_player.current_hp, self.mock_player.max_hp)
        print("   ✅ 玩家复活完成，血量已恢复")
        
        # 5. 验证地图刷新
        monster_names = [monster.name for monster in self.mock_current_map.active_enemies]
        self.assertIn("新怪物A", monster_names)
        self.assertIn("新怪物B", monster_names)
        print("   ✅ 地图怪物已刷新")
        
        # 6. 测试危险怪物避让
        print("3. 测试危险怪物避让...")
        self.assertTrue(self.battle_manager._is_dangerous_monster(dangerous_monster))
        print("   ✅ 危险怪物记录有效")
        
        print("\n🎉 完整死亡恢复流程测试通过！")


def run_tests():
    """运行所有测试"""
    print("开始测试玩家死亡恢复系统...")
    print("=" * 50)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestDeathRecoverySystem)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 50)
    if result.wasSuccessful():
        print("🎉 所有测试通过！死亡恢复系统工作正常。")
    else:
        print("❌ 部分测试失败，请检查实现。")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")


if __name__ == "__main__":
    run_tests()
