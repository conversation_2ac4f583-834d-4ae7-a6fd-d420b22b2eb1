# -*- coding: utf-8 -*-
"""
任务管理器
负责管理玩家的任务系统，包括任务接取、完成、奖励发放等功能
"""

from typing import Dict, List, Optional, Any
import time
import json
from enum import Enum


class QuestStatus(Enum):
    """任务状态枚举"""
    NOT_STARTED = "not_started"    # 未开始
    IN_PROGRESS = "in_progress"    # 进行中
    COMPLETED = "completed"        # 已完成
    FAILED = "failed"              # 已失败
    TURNED_IN = "turned_in"        # 已交付


class QuestType(Enum):
    """任务类型枚举"""
    MAIN = "main"                  # 主线任务
    SIDE = "side"                  # 支线任务
    DAILY = "daily"                # 日常任务
    WEEKLY = "weekly"              # 周常任务
    EVENT = "event"                # 活动任务
    ACHIEVEMENT = "achievement"    # 成就任务


class QuestManager:
    """
    任务管理器类
    负责管理玩家的任务系统
    """
    
    def __init__(self):
        """
        初始化任务管理器
        """
        # 当前进行中的任务
        self.active_quests: List[Dict[str, Any]] = []
        
        # 已完成的任务
        self.completed_quests: List[Dict[str, Any]] = []
        
        # 任务进度记录
        self.quest_progress: Dict[str, Dict[str, Any]] = {}
        
        # 日常任务重置时间
        self.daily_reset_time = 0
        
        # 周常任务重置时间
        self.weekly_reset_time = 0
    
    def accept_quest(self, quest_data: Dict[str, Any], player) -> bool:
        """
        接受任务
        
        参数:
            quest_data: 任务数据
            player: 玩家对象
            
        返回:
            bool: 是否成功接受
        """
        if not quest_data:
            return False
        
        quest_id = quest_data.get('id')
        if not quest_id:
            return False
        
        # 检查是否已经接受过该任务
        if self.has_quest(quest_id):
            return False
        
        # 检查是否已经完成过该任务（非重复任务）
        if not quest_data.get('repeatable', False) and self.is_quest_completed(quest_id):
            return False
        
        # 检查前置条件
        if not self._check_quest_requirements(quest_data, player):
            return False
        
        # 接受任务
        new_quest = quest_data.copy()
        new_quest['status'] = QuestStatus.IN_PROGRESS.value
        new_quest['accepted_time'] = time.time()
        new_quest['progress'] = {}
        
        # 初始化任务进度
        objectives = quest_data.get('objectives', [])
        for objective in objectives:
            objective_id = objective.get('id')
            if objective_id:
                new_quest['progress'][objective_id] = {
                    'current': 0,
                    'target': objective.get('target', 1),
                    'completed': False
                }
        
        self.active_quests.append(new_quest)
        self.quest_progress[quest_id] = new_quest['progress']
        
        return True
    
    def update_quest_progress(self, quest_id: str, objective_id: str, progress: int = 1) -> bool:
        """
        更新任务进度
        
        参数:
            quest_id: 任务ID
            objective_id: 目标ID
            progress: 进度增量
            
        返回:
            bool: 是否成功更新
        """
        quest = self.get_active_quest(quest_id)
        if not quest:
            return False
        
        if objective_id not in quest['progress']:
            return False
        
        objective_progress = quest['progress'][objective_id]
        if objective_progress['completed']:
            return False  # 已完成的目标不能再更新
        
        # 更新进度
        objective_progress['current'] = min(
            objective_progress['target'],
            objective_progress['current'] + progress
        )
        
        # 检查是否完成目标
        if objective_progress['current'] >= objective_progress['target']:
            objective_progress['completed'] = True
        
        # 检查任务是否全部完成
        if self._check_quest_completion(quest):
            quest['status'] = QuestStatus.COMPLETED.value
            quest['completed_time'] = time.time()
        
        return True
    
    def complete_quest(self, quest_id: str, player) -> Dict[str, Any]:
        """
        完成任务并发放奖励
        
        参数:
            quest_id: 任务ID
            player: 玩家对象
            
        返回:
            奖励信息字典
        """
        quest = self.get_active_quest(quest_id)
        if not quest or quest['status'] != QuestStatus.COMPLETED.value:
            return {'success': False, 'message': '任务未完成或不存在'}
        
        # 发放奖励
        rewards = self._give_quest_rewards(quest, player)
        
        # 移动任务到已完成列表
        quest['status'] = QuestStatus.TURNED_IN.value
        quest['turned_in_time'] = time.time()
        
        self.active_quests.remove(quest)
        self.completed_quests.append(quest)
        
        # 清理进度记录
        if quest_id in self.quest_progress:
            del self.quest_progress[quest_id]
        
        return {
            'success': True,
            'message': f'完成任务：{quest.get("name", "未知任务")}',
            'rewards': rewards
        }
    
    def abandon_quest(self, quest_id: str) -> bool:
        """
        放弃任务
        
        参数:
            quest_id: 任务ID
            
        返回:
            bool: 是否成功放弃
        """
        quest = self.get_active_quest(quest_id)
        if not quest:
            return False
        
        # 检查是否可以放弃
        if not quest.get('abandonable', True):
            return False
        
        # 移除任务
        self.active_quests.remove(quest)
        
        # 清理进度记录
        if quest_id in self.quest_progress:
            del self.quest_progress[quest_id]
        
        return True
    
    def _check_quest_requirements(self, quest_data: Dict[str, Any], player) -> bool:
        """
        检查任务接受条件
        
        参数:
            quest_data: 任务数据
            player: 玩家对象
            
        返回:
            bool: 是否满足条件
        """
        requirements = quest_data.get('requirements', {})
        
        # 检查等级要求
        min_level = requirements.get('min_level', 1)
        if player.level < min_level:
            return False
        
        max_level = requirements.get('max_level')
        if max_level and player.level > max_level:
            return False
        
        # 检查职业要求
        required_class = requirements.get('class')
        if required_class and player.character_class != required_class:
            return False
        
        # 检查前置任务
        prerequisite_quests = requirements.get('prerequisite_quests', [])
        for prereq_quest_id in prerequisite_quests:
            if not self.is_quest_completed(prereq_quest_id):
                return False
        
        # 检查物品要求
        required_items = requirements.get('items', [])
        for item_req in required_items:
            item_name = item_req.get('name')
            item_count = item_req.get('count', 1)
            if hasattr(player, 'inventory_manager'):
                if not player.inventory_manager.has_item(item_name, item_count):
                    return False
        
        return True
    
    def _check_quest_completion(self, quest: Dict[str, Any]) -> bool:
        """
        检查任务是否完成
        
        参数:
            quest: 任务数据
            
        返回:
            bool: 是否完成
        """
        progress = quest.get('progress', {})
        
        # 检查所有目标是否完成
        for objective_progress in progress.values():
            if not objective_progress.get('completed', False):
                return False
        
        return True
    
    def _give_quest_rewards(self, quest: Dict[str, Any], player) -> Dict[str, Any]:
        """
        发放任务奖励
        
        参数:
            quest: 任务数据
            player: 玩家对象
            
        返回:
            奖励信息
        """
        rewards_given = {
            'exp': 0,
            'gold': 0,
            'items': []
        }
        
        rewards = quest.get('rewards', {})
        
        # 经验奖励
        exp_reward = rewards.get('exp', 0)
        if exp_reward > 0:
            player.add_exp(exp_reward)
            rewards_given['exp'] = exp_reward
        
        # 金币奖励
        gold_reward = rewards.get('gold', 0)
        if gold_reward > 0:
            player.add_currency(gold_reward)
            rewards_given['gold'] = gold_reward
        
        # 物品奖励
        item_rewards = rewards.get('items', [])
        for item_reward in item_rewards:
            item_name = item_reward.get('name')
            item_count = item_reward.get('count', 1)
            item_data = item_reward.get('data', {})
            
            if hasattr(player, 'inventory_manager'):
                if player.inventory_manager.add_item(item_data, item_count):
                    rewards_given['items'].append({
                        'name': item_name,
                        'count': item_count
                    })
        
        return rewards_given
    
    def has_quest(self, quest_id: str) -> bool:
        """
        检查是否拥有指定任务
        
        参数:
            quest_id: 任务ID
            
        返回:
            bool: 是否拥有该任务
        """
        return any(quest.get('id') == quest_id for quest in self.active_quests)
    
    def is_quest_completed(self, quest_id: str) -> bool:
        """
        检查任务是否已完成
        
        参数:
            quest_id: 任务ID
            
        返回:
            bool: 是否已完成
        """
        return any(quest.get('id') == quest_id for quest in self.completed_quests)
    
    def get_active_quest(self, quest_id: str) -> Optional[Dict[str, Any]]:
        """
        获取活跃任务
        
        参数:
            quest_id: 任务ID
            
        返回:
            任务数据或None
        """
        for quest in self.active_quests:
            if quest.get('id') == quest_id:
                return quest
        return None
    
    def get_all_active_quests(self) -> List[Dict[str, Any]]:
        """
        获取所有活跃任务
        
        返回:
            任务列表
        """
        return self.active_quests.copy()
    
    def get_quests_by_type(self, quest_type: QuestType) -> List[Dict[str, Any]]:
        """
        获取指定类型的任务
        
        参数:
            quest_type: 任务类型
            
        返回:
            任务列表
        """
        return [quest for quest in self.active_quests if quest.get('type') == quest_type.value]
    
    def get_completed_quests(self) -> List[Dict[str, Any]]:
        """
        获取已完成的任务
        
        返回:
            已完成任务列表
        """
        return self.completed_quests.copy()
    
    def reset_daily_quests(self):
        """
        重置日常任务
        """
        current_time = time.time()
        
        # 移除过期的日常任务
        self.active_quests = [quest for quest in self.active_quests 
                             if quest.get('type') != QuestType.DAILY.value]
        
        # 更新重置时间
        self.daily_reset_time = current_time
    
    def reset_weekly_quests(self):
        """
        重置周常任务
        """
        current_time = time.time()
        
        # 移除过期的周常任务
        self.active_quests = [quest for quest in self.active_quests 
                             if quest.get('type') != QuestType.WEEKLY.value]
        
        # 更新重置时间
        self.weekly_reset_time = current_time
    
    def check_auto_reset(self):
        """
        检查并自动重置任务
        """
        current_time = time.time()
        
        # 检查日常任务重置（每天凌晨重置）
        if current_time - self.daily_reset_time >= 24 * 3600:
            self.reset_daily_quests()
        
        # 检查周常任务重置（每周一重置）
        if current_time - self.weekly_reset_time >= 7 * 24 * 3600:
            self.reset_weekly_quests()
    
    def get_quest_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        返回:
            统计信息字典
        """
        stats = {
            'total_active': len(self.active_quests),
            'total_completed': len(self.completed_quests),
            'by_type': {},
            'completion_rate': 0.0
        }
        
        # 按类型统计
        for quest_type in QuestType:
            active_count = len(self.get_quests_by_type(quest_type))
            completed_count = len([q for q in self.completed_quests 
                                 if q.get('type') == quest_type.value])
            stats['by_type'][quest_type.value] = {
                'active': active_count,
                'completed': completed_count
            }
        
        # 计算完成率
        total_quests = stats['total_active'] + stats['total_completed']
        if total_quests > 0:
            stats['completion_rate'] = stats['total_completed'] / total_quests * 100
        
        return stats
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式（用于保存）
        
        返回:
            字典格式的任务数据
        """
        return {
            'active_quests': self.active_quests,
            'completed_quests': self.completed_quests,
            'quest_progress': self.quest_progress,
            'daily_reset_time': self.daily_reset_time,
            'weekly_reset_time': self.weekly_reset_time
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """
        从字典格式加载（用于读取存档）
        
        参数:
            data: 字典格式的任务数据
        """
        self.active_quests = data.get('active_quests', [])
        self.completed_quests = data.get('completed_quests', [])
        self.quest_progress = data.get('quest_progress', {})
        self.daily_reset_time = data.get('daily_reset_time', 0)
        self.weekly_reset_time = data.get('weekly_reset_time', 0)