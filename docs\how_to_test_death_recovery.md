# 如何测试玩家死亡恢复系统

## 功能概述

新的死亡恢复系统确保玩家在死亡后不会陷入死亡循环，通过以下机制实现：

1. **自动地图刷新** - 玩家复活时重新生成所有怪物
2. **危险怪物记录** - 记住导致死亡的怪物，短期内避开
3. **智能寻怪** - 优先选择安全的怪物进行战斗

## 如何体验这个功能

### 方法1：正常游戏体验

1. **启动游戏**
   ```bash
   python main.py
   ```

2. **进入游戏**
   - 创建角色或加载存档
   - 进入主游戏界面

3. **开始自动战斗**
   - 点击战斗区域的"自动战斗"按钮
   - 系统会自动寻找怪物并开始战斗

4. **观察死亡恢复**
   - 如果玩家死亡，会看到10秒复活倒计时
   - 复活后地图怪物会自动刷新
   - 系统会避开刚才导致死亡的怪物类型

### 方法2：运行专门的测试

1. **运行测试脚本**
   ```bash
   python tests/test_death_recovery_system.py
   ```

2. **查看测试结果**
   - 测试会模拟完整的死亡恢复流程
   - 验证所有功能是否正常工作

## 观察要点

### 死亡时的提示信息

当玩家死亡时，你会看到类似这样的消息：
```
💀 战斗失败！玩家死亡...
⚠️ 记录危险怪物: 强力骷髅 (等级8)
⏱️ 复活倒计时: 10 秒
```

### 复活时的提示信息

玩家复活时，你会看到：
```
💚 玩家复活！血量已恢复到 100
🔄 玩家复活，正在重新刷新地图怪物...
🗑️ 已清除 5 个旧怪物
✅ 重新生成了 8 个新怪物
🌟 地图怪物刷新完成！新的冒险开始了！
✨ 复活完成，可以继续冒险了！
```

### 智能寻怪的提示信息

当系统寻找怪物时，你可能会看到：
```
✅ 找到安全目标: 小鹿 (距离: 12.3)
```

或者：
```
⚠️ 发现危险怪物 强力骷髅，建议谨慎应对
```

## 系统参数

### 可调整的参数

如果你想调整系统行为，可以修改以下参数：

**复活时间** (在 `game/core/battle_manager.py` 中)：
```python
self.revival_time = 10.0  # 复活时间（秒）
```

**危险怪物记录时效** (在 `_is_dangerous_monster` 方法中)：
```python
if current_time - death_record['death_time'] < 30.0:  # 30秒内的记录
```

**危险怪物等级容差** (在 `_is_dangerous_monster` 方法中)：
```python
abs(death_record['level'] - monster_level) <= 2  # 等级相差2级以内
```

## 调试模式

如果你想看到更详细的调试信息，可以：

1. **启用战斗管理器调试输出**
   ```python
   # 在 main.py 的 initialize_main_game() 函数中
   battle_manager = CoreBattleManager(debug_console_output=True)  # 改为 True
   ```

2. **修改调试配置文件**
   编辑 `game/config/debug_config.json`：
   ```json
   {
     "debug_settings": {
       "global_debug_enabled": {"value": true},
       "console_output": {
         "battle_manager": {"value": true}
       }
     }
   }
   ```

## 预期效果

### 成功的死亡恢复应该表现为：

1. **玩家死亡后**：
   - 显示复活倒计时
   - 记录导致死亡的怪物信息
   - 停止所有自动战斗

2. **玩家复活后**：
   - 血量恢复到满血
   - 地图上的怪物全部重新生成
   - 新怪物位置重新分布

3. **重新开始战斗时**：
   - 优先选择安全的怪物
   - 避开30秒内导致死亡的同类怪物
   - 如果遇到危险怪物会给出警告

### 避免死亡循环的效果：

- 玩家不会在同一个位置反复遭遇同一个强力怪物
- 每次复活都有新的环境和机会
- 系统会学习并避开危险的怪物类型

## 故障排除

如果功能不正常工作，请检查：

1. **控制台输出** - 查看是否有错误信息
2. **地图管理器** - 确保地图有怪物生成能力
3. **数据管理器** - 确保怪物数据正常加载
4. **调试配置** - 启用调试输出查看详细信息

## 反馈

如果你发现任何问题或有改进建议，请记录：
- 具体的错误信息
- 复现步骤
- 期望的行为
- 实际的行为

这个系统旨在提供更好的游戏体验，避免玩家因为遇到过强的怪物而反复死亡，同时保持游戏的挑战性和趣味性。
