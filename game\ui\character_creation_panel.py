import pygame
from game.ui.ui_panel import UIPanel
from game.core.resource_manager import get_game_asset_path

class CharacterCreationPanel(UIPanel):
    """角色创建界面面板"""

    def __init__(self, screen, position, size):
        """
        初始化角色创建面板

        Args:
            screen: 游戏屏幕对象
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
        """
        super().__init__(screen, position, size)

        # 设置面板属性
        self.title = "创建角色"
        self.show_border = False  # 全屏面板不显示边框
        self.show_title = False   # 标题单独绘制

        # 角色创建相关属性
        self.selected_class = "战士"    # 默认选择战士
        self.selected_gender = "男"     # 默认选择男性
        self.character_name = ""        # 角色名称
        self._name_input_active = False # 使用私有变量和setter/getter
        self.cursor_pos = 0
        self.cursor_visible = True      # 光标可见性
        self.cursor_timer = 0           # 光标闪烁计时器
        self.max_name_length_effective = 10 # 名称最大有效长度 (中文算2, 英文算1)

        # IME预编辑文本支持
        self.ime_editing_text = ""
        self.ime_editing_start = 0
        self.ime_editing_length = 0

        # 职业信息 (只包含描述)
        self.class_info = {
            "战士": {
                "description": "近战物理职业，拥有高生命值和防御力。\n适合新手玩家，战斗风格简单直接。"
            },
            "法师": {
                "description": "远程魔法职业，拥有强大的群体攻击，\n但生命值较低，操作要求较高。"
            },
            "道士": {
                "description": "辅助召唤职业，可以召唤宠物作战，\n拥有治疗和施毒能力，玩法多样。"
            }
        }

        # 回调函数
        self.on_create_character = None
        self.on_back_to_menu = None

        # 背景图片
        self.background_image = None
        self._load_background()

        # 创建UI组件
        self._create_ui_components()

    @property
    def name_input_active(self):
        return self._name_input_active

    def set_name_input_active(self, active_status: bool):
        """设置名称输入框激活状态并管理IME"""
        if self._name_input_active == active_status:
            return
        self._name_input_active = active_status
        if self._name_input_active:
            pygame.key.start_text_input()
            # 计算输入框的实际屏幕矩形
            input_rect_abs = pygame.Rect(
                self.rect.x + self.rect.centerx - self.name_input_width // 2,
                self.rect.y + self.name_section_y,
                self.name_input_width,
                self.name_input_height
            )
            pygame.key.set_text_input_rect(input_rect_abs)
            self.cursor_pos = len(self.character_name)
            print("📝 激活名称输入框")
        else:
            pygame.key.stop_text_input()
            self.ime_editing_text = ""
            self.ime_editing_start = 0
            self.ime_editing_length = 0
            print("📝 取消激活名称输入框")


    def _load_background(self):
        """加载背景图片"""
        try:
            # 使用资源管理器加载背景图片
            background_path = get_game_asset_path("images/background.png")
            self.background_image = pygame.image.load(background_path)
            self.background_image = pygame.transform.scale(self.background_image, self.size)
            print(f"✅ 角色创建界面背景图片加载成功")
        except Exception as e:
            print(f"❌ 角色创建界面背景图片加载失败: {e}")
            self.background_image = None

    def _create_ui_components(self):
        """创建UI组件"""
        screen_width, screen_height = self.size
        center_x = screen_width // 2

        self.title_y = 60
        self.class_section_y = 150
        self.class_button_width = 120
        self.class_button_height = 40
        self.class_button_spacing = 30
        self.gender_section_y = self.class_section_y + self.class_button_height + 50 # 调整间距
        self.gender_button_width = 80
        self.gender_button_height = 35
        self.name_section_y = self.gender_section_y + self.gender_button_height + 50 # 调整间距
        self.name_input_width = 250 # 加宽输入框
        self.name_input_height = 40
        self.description_section_y = self.name_section_y + self.name_input_height + 40 # 调整间距
        self.description_width = 500 # 加宽描述区域
        self.description_height = 80 # 调整描述区域高度
        self.button_section_y = self.description_section_y + self.description_height + 50 # 调整间距
        self.action_button_width = 120 # 加宽按钮
        self.action_button_height = 45

        self.clear_buttons()

        class_names = ["战士", "法师", "道士"]
        total_class_width = len(class_names) * self.class_button_width + (len(class_names) - 1) * self.class_button_spacing
        start_x_class = center_x - total_class_width // 2
        for i, class_name in enumerate(class_names):
            x = start_x_class + i * (self.class_button_width + self.class_button_spacing)
            bg_color = (100, 100, 180) if class_name == self.selected_class else (60, 60, 100)
            text_color = (255, 255, 255) if class_name == self.selected_class else (200, 200, 200)
            self.add_button(
                text=class_name,
                rect=(x, self.class_section_y, self.class_button_width, self.class_button_height),
                callback=lambda cls=class_name: self._select_class(cls),
                bg_color=bg_color, text_color=text_color, border_color=(120, 120, 120)
            )

        gender_names = ["男", "女"]
        total_gender_width = len(gender_names) * self.gender_button_width + (len(gender_names) - 1) * 20
        start_x_gender = center_x - total_gender_width // 2
        for i, gender in enumerate(gender_names):
            x = start_x_gender + i * (self.gender_button_width + 20)
            bg_color = (100, 100, 180) if gender == self.selected_gender else (60, 60, 100)
            text_color = (255, 255, 255) if gender == self.selected_gender else (200, 200, 200)
            self.add_button(
                text=gender,
                rect=(x, self.gender_section_y, self.gender_button_width, self.gender_button_height),
                callback=lambda g=gender: self._select_gender(g),
                bg_color=bg_color, text_color=text_color, border_color=(120, 120, 120)
            )

        create_x = center_x - self.action_button_width - 20 # 调整按钮间距
        self.add_button(
            text="创建角色",
            rect=(create_x, self.button_section_y, self.action_button_width, self.action_button_height),
            callback=self._create_character,
            bg_color=(80, 150, 80), text_color=(255, 255, 255), border_color=(100, 180, 100)
        )
        back_x = center_x + 20 # 调整按钮间距
        self.add_button(
            text="返回",
            rect=(back_x, self.button_section_y, self.action_button_width, self.action_button_height),
            callback=self._back_to_menu,
            bg_color=(150, 80, 80), text_color=(255, 255, 255), border_color=(180, 100, 100)
        )

    def _select_class(self, class_name):
        if class_name != self.selected_class:
            self.selected_class = class_name
            print(f"🎯 选择职业: {class_name}")
            self._create_ui_components() # 重新创建以更新按钮状态

    def _select_gender(self, gender):
        if gender != self.selected_gender:
            self.selected_gender = gender
            print(f"🎯 选择性别: {gender}")
            self._create_ui_components() # 重新创建以更新按钮状态

    def _calculate_effective_length(self, text: str) -> int:
        """计算名称的有效长度 (中文算2，英文或其他算1)"""
        length = 0
        for char_val in text:
            if '\u4e00' <= char_val <= '\u9fff': # 基本中文字符范围
                length += 2
            else:
                length += 1
        return length

    def _create_character(self):
        name_stripped = self.character_name.strip()
        if not name_stripped:
            print("❌ 请输入角色名称")
            # 你可能想在这里通过UI显示消息给玩家
            return

        effective_length = self._calculate_effective_length(name_stripped)
        if effective_length < 2: # 至少1个中文或2个英文
            print("❌ 角色名称太短 (至少1中文或2英文)")
            return
        if effective_length > self.max_name_length_effective:
            print(f"❌ 角色名称太长 (最大有效长度 {self.max_name_length_effective})")
            return

        print(f"✅ 创建角色: {name_stripped}, 职业: {self.selected_class}, 性别: {self.selected_gender}")
        if self.on_create_character:
            character_data = {
                'name': name_stripped,
                'class': self.selected_class,
                'gender': self.selected_gender
            }
            self.on_create_character(character_data)

    def _back_to_menu(self):
        print("🔙 返回主菜单")
        if self.on_back_to_menu:
            self.on_back_to_menu()

    def set_callbacks(self, on_create_character=None, on_back_to_menu=None):
        self.on_create_character = on_create_character
        self.on_back_to_menu = on_back_to_menu

    def handle_event(self, event):
        if not self.visible:
            return False

        # 优先处理按钮点击
        if super().handle_event(event):
            if self.name_input_active: # 如果按钮点击导致输入框应该失焦
                 self.set_name_input_active(False)
            return True

        # 处理鼠标点击以激活/取消激活输入框
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            # 计算输入框在屏幕上的绝对矩形
            input_rect_on_panel = pygame.Rect(
                self.rect.centerx - self.name_input_width // 2,
                self.name_section_y,
                self.name_input_width,
                self.name_input_height
            )
            input_rect_abs = input_rect_on_panel.move(self.rect.topleft)

            if input_rect_abs.collidepoint(event.pos):
                if not self.name_input_active:
                    self.set_name_input_active(True)
                # 精确光标定位
                relative_x = event.pos[0] - input_rect_abs.left - 5
                text_font = pygame.font.SysFont("SimHei", 18)
                temp_text = ""
                new_cursor_pos = 0
                for i, char_val in enumerate(self.character_name):
                    temp_text += char_val
                    text_width = text_font.size(temp_text)[0]
                    if text_width > relative_x:
                        # 判断更靠近哪个字符的边界
                        prev_char_width = text_font.size(temp_text[:-1])[0] if i > 0 else 0
                        if (relative_x - prev_char_width) < (text_width - relative_x):
                            new_cursor_pos = i
                        else:
                            new_cursor_pos = i + 1
                        break
                else: # 如果点击在所有文字之后
                    new_cursor_pos = len(self.character_name)
                self.cursor_pos = new_cursor_pos

                return True
            else:
                if self.name_input_active:
                    self.set_name_input_active(False)

        # 处理键盘输入事件 (仅当输入框激活时)
        if self.name_input_active:
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_BACKSPACE:
                    if self.ime_editing_text:
                        self.ime_editing_text = ""
                        self.ime_editing_start = 0
                        self.ime_editing_length = 0
                    elif self.cursor_pos > 0:
                        self.character_name = (
                            self.character_name[:self.cursor_pos - 1] +
                            self.character_name[self.cursor_pos:]
                        )
                        self.cursor_pos -= 1
                    return True
                elif event.key == pygame.K_DELETE:
                    if self.cursor_pos < len(self.character_name):
                        self.character_name = (
                            self.character_name[:self.cursor_pos] +
                            self.character_name[self.cursor_pos + 1:]
                        )
                    return True
                elif event.key == pygame.K_LEFT:
                    self.cursor_pos = max(0, self.cursor_pos - 1)
                    self.ime_editing_text = ""
                    return True
                elif event.key == pygame.K_RIGHT:
                    self.cursor_pos = min(len(self.character_name), self.cursor_pos + 1)
                    self.ime_editing_text = ""
                    return True
                elif event.key == pygame.K_HOME:
                    self.cursor_pos = 0
                    self.ime_editing_text = ""
                    return True
                elif event.key == pygame.K_END:
                    self.cursor_pos = len(self.character_name)
                    self.ime_editing_text = ""
                    return True
                elif event.key == pygame.K_RETURN or event.key == pygame.K_KP_ENTER:
                    self._create_character()
                    return True
                elif event.key == pygame.K_ESCAPE:
                    self.set_name_input_active(False)
                    return True

            elif event.type == pygame.TEXTINPUT:
                new_text_segment = event.text
                potential_text = (
                    self.character_name[:self.cursor_pos] +
                    new_text_segment +
                    self.character_name[self.cursor_pos:]
                )
                
                # 计算当前有效长度和新文本的有效长度
                current_length = self._calculate_effective_length(self.character_name)
                new_segment_length = self._calculate_effective_length(new_text_segment)
                potential_length = self._calculate_effective_length(potential_text)
                
                print(f"🔤 文本输入: '{new_text_segment}' (长度: {new_segment_length})")
                print(f"📏 当前长度: {current_length}, 新增长度: {new_segment_length}, 预期总长度: {potential_length}, 限制: {self.max_name_length_effective}")
                
                if potential_length <= self.max_name_length_effective:
                    self.character_name = potential_text
                    self.cursor_pos += len(new_text_segment)
                    print(f"✅ 文本输入成功: '{self.character_name}'")
                else:
                    print(f"❌ 名称长度超限 (当前: {potential_length}, 限制: {self.max_name_length_effective})")
                self.ime_editing_text = ""
                return True

            elif event.type == pygame.TEXTEDITING:
                self.ime_editing_text = event.text
                self.ime_editing_start = event.start
                self.ime_editing_length = event.length
                return True

        return False

    def update(self):
        """更新面板状态"""
        self.cursor_timer += 16
        if self.cursor_timer >= 500: # 光标闪烁频率调整为约0.5秒
            self.cursor_visible = not self.cursor_visible
            self.cursor_timer = 0

    def render(self):
        """渲染面板"""
        if not self.visible:
            return

        self._render_background()
        self._render_title()
        # 按钮的渲染由UIPanel的_render_buttons处理，这里不需要再调用
        # self._render_class_selection() # 这些是标签，不是按钮
        # self._render_gender_selection()# 这些是标签，不是按钮
        self._render_name_input()
        self._render_description()
        self._render_buttons() # 调用父类方法渲染所有已添加的按钮

    def _render_background(self):
        if self.background_image:
            self.screen.blit(self.background_image, self.rect.topleft) # 使用topleft确保全屏
            overlay = pygame.Surface(self.size, pygame.SRCALPHA) # 使用SRCALPHA创建带alpha通道的表面
            overlay.fill((0, 0, 0, 120)) # 更深的半透明黑色遮罩
            self.screen.blit(overlay, self.rect.topleft)
        else:
            start_color = (20, 20, 40)
            end_color = (60, 60, 80)
            for y in range(self.size[1]):
                ratio = y / self.size[1]
                r = int(start_color[0] * (1 - ratio) + end_color[0] * ratio)
                g = int(start_color[1] * (1 - ratio) + end_color[1] * ratio)
                b = int(start_color[2] * (1 - ratio) + end_color[2] * ratio)
                pygame.draw.line(self.screen, (r, g, b),
                               (self.rect.left, self.rect.top + y),
                               (self.rect.right, self.rect.top + y))

    def _render_title(self):
        title_font_main = pygame.font.SysFont("SimHei", 56, bold=True) # 主标题加大
        title_font_sub = pygame.font.SysFont("SimHei", 20)          # 副标题
        
        title_surface = title_font_main.render("创建角色", True, (255, 223, 100)) # 金黄色
        title_rect = title_surface.get_rect(center=(self.rect.centerx, self.title_y + 20)) # 调整Y
        
        # 简单的阴影效果
        shadow_offset = 2
        title_shadow_surface = title_font_main.render("创建角色", True, (0,0,0,100))
        self.screen.blit(title_shadow_surface, (title_rect.x + shadow_offset, title_rect.y + shadow_offset))
        self.screen.blit(title_surface, title_rect)

        subtitle_surface = title_font_sub.render("选择你的征途起点", True, (200, 200, 220)) # 淡雅颜色
        subtitle_rect = subtitle_surface.get_rect(center=(self.rect.centerx, self.title_y + 70)) # 调整Y
        self.screen.blit(subtitle_surface, subtitle_rect)


    def _render_name_input(self):
        label_font = pygame.font.SysFont("SimHei", 22, bold=True) # 加大标签字体
        text_font = pygame.font.SysFont("SimHei", 20) # 输入框字体
        placeholder_color = (130, 130, 150)
        text_color = (230, 230, 255) # 淡蓝色文本

        label_surface = label_font.render("角色昵称:", True, (220, 220, 255)) # 标签颜色
        # 将标签放在输入框左边
        input_box_center_x = self.rect.centerx
        input_rect_width = self.name_input_width
        
        label_rect = label_surface.get_rect(
            midright=(input_box_center_x - input_rect_width // 2 - 10, 
                      self.name_section_y + self.name_input_height // 2)
        )
        self.screen.blit(label_surface, label_rect)

        input_rect_visual = pygame.Rect( # 视觉上的输入框矩形
            input_box_center_x - input_rect_width // 2,
            self.name_section_y,
            input_rect_width,
            self.name_input_height
        )

        bg_color = (70, 70, 100) if self.name_input_active else (50, 50, 70)
        border_color = (130, 130, 180) if self.name_input_active else (90, 90, 110)
        pygame.draw.rect(self.screen, bg_color, input_rect_visual, border_radius=5)
        pygame.draw.rect(self.screen, border_color, input_rect_visual, 2, border_radius=5)

        padding_x = 8
        padding_y = (input_rect_visual.height - text_font.get_height()) // 2

        # 渲染逻辑
        text_to_display = self.character_name
        current_render_x = input_rect_visual.x + padding_x

        # 光标前的文本
        text_before_cursor_val = text_to_display[:self.cursor_pos]
        surface_before = text_font.render(text_before_cursor_val, True, text_color)
        self.screen.blit(surface_before, (current_render_x, input_rect_visual.y + padding_y))
        width_before = surface_before.get_width()
        cursor_render_x = current_render_x + width_before

        # IME 预编辑文本
        if self.name_input_active and self.ime_editing_text:
            surface_ime = text_font.render(self.ime_editing_text, True, text_color)
            self.screen.blit(surface_ime, (cursor_render_x, input_rect_visual.y + padding_y))
            ime_underline_y = input_rect_visual.y + padding_y + text_font.get_height() -1 # 微调下划线位置
            
            # 计算IME文本中被选中部分的起始和结束渲染X坐标
            ime_sel_start_x = cursor_render_x
            if self.ime_editing_start > 0:
                ime_sel_start_x += text_font.size(self.ime_editing_text[:self.ime_editing_start])[0]
            
            ime_sel_end_x = cursor_render_x
            if self.ime_editing_start + self.ime_editing_length > 0 :
                 ime_sel_end_x += text_font.size(self.ime_editing_text[:self.ime_editing_start + self.ime_editing_length])[0]


            pygame.draw.line(self.screen, (255, 255, 0),
                             (ime_sel_start_x, ime_underline_y),
                             (ime_sel_end_x, ime_underline_y), 2)
            width_ime = surface_ime.get_width()
            cursor_render_x_after_ime = cursor_render_x + width_ime # 光标在IME文本后
        else:
            width_ime = 0
            cursor_render_x_after_ime = cursor_render_x


        # 光标
        if self.name_input_active and self.cursor_visible:
            # 如果IME正在编辑，光标通常在IME文本的末尾（或者由IME控制）
            # 为简单起见，我们把它画在IME文本（如果存在）之后，或者在已确认文本之后
            final_cursor_x = cursor_render_x_after_ime if self.ime_editing_text else cursor_render_x
            pygame.draw.line(self.screen, text_color,
                             (final_cursor_x, input_rect_visual.y + padding_y -1),
                             (final_cursor_x, input_rect_visual.y + padding_y + text_font.get_height() +1), 2)


        # 光标后的文本
        text_after_cursor_val = text_to_display[self.cursor_pos:]
        surface_after = text_font.render(text_after_cursor_val, True, text_color)
        self.screen.blit(surface_after, (cursor_render_x_after_ime, input_rect_visual.y + padding_y))


        if not self.character_name and not self.name_input_active and not self.ime_editing_text:
            placeholder_surface = text_font.render("输入昵称 (2-5中/10英)", True, placeholder_color)
            self.screen.blit(placeholder_surface, (input_rect_visual.x + padding_x, input_rect_visual.y + padding_y))
            
        # 绘制名称长度提示
        name_hint_font = pygame.font.SysFont("SimHei", 14)
        name_hint_text = f"长度: {self._calculate_effective_length(self.character_name)}/{self.max_name_length_effective}"
        hint_color = (180, 180, 180) if self._calculate_effective_length(self.character_name) <= self.max_name_length_effective else (255,100,100)
        hint_surface = name_hint_font.render(name_hint_text, True, hint_color)
        hint_rect = hint_surface.get_rect(topright=(input_rect_visual.right - 5, input_rect_visual.bottom + 5))
        self.screen.blit(hint_surface, hint_rect)


    def _render_description(self):
        if self.selected_class not in self.class_info:
            return

        desc_rect = pygame.Rect(
            self.rect.centerx - self.description_width // 2,
            self.description_section_y,
            self.description_width,
            self.description_height
        )
        
        desc_bg_surface = pygame.Surface(desc_rect.size, pygame.SRCALPHA)
        desc_bg_surface.fill((30, 30, 50, 180)) # 更深的半透明背景
        self.screen.blit(desc_bg_surface, desc_rect.topleft)
        pygame.draw.rect(self.screen, (80, 80, 110), desc_rect, 2, border_radius=5) # 圆角边框

        class_data = self.class_info[self.selected_class]
        desc_font = pygame.font.SysFont("SimHei", 18) # 描述字体稍大
        text_color = (210, 210, 230) # 淡雅的文本颜色
        line_spacing = 25 # 行间距
        padding = 20 # 内边距

        y_offset = padding
        # 渲染职业描述
        for line in class_data["description"].split("\n"):
            if line.strip():
                text_surface = desc_font.render(line.strip(), True, text_color)
                self.screen.blit(text_surface, (desc_rect.left + padding, desc_rect.top + y_offset))
                y_offset += line_spacing
                if y_offset + line_spacing > desc_rect.height - padding : # 防止文本溢出
                    break
        
        # 安全地渲染属性信息（如果存在）
        if "stats" in class_data:
            stats_font = pygame.font.SysFont("SimHei", 14, bold=True)
            y_offset += 10  # 添加一些间距
            for line in class_data["stats"].split("\n"):
                if line.strip() and y_offset + line_spacing <= desc_rect.height - padding:
                    text_surface = stats_font.render(line.strip(), True, (255, 255, 150))
                    self.screen.blit(text_surface, (desc_rect.left + padding, desc_rect.top + y_offset))
                    y_offset += line_spacing