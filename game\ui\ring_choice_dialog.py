"""
戒指选择对话框
用于让玩家选择将戒指装备到戒指1还是戒指2槽位
"""

import pygame
import tkinter as tk
from tkinter import messagebox, simpledialog
from typing import Optional, Callable, Dict, Any


class RingChoiceDialog(simpledialog.Dialog):
    def __init__(self, parent, title, item_type="戒指"):
        self.item_type = item_type
        self.choice = None
        super().__init__(parent, title=title)

    def body(self, master):
        label_text = f"请选择要替换的{self.item_type}位置："
        tk.Label(master, text=label_text).grid(row=0, columnspan=2)

        self.button1 = tk.Button(master, text=f"{self.item_type}1", command=lambda: self.set_choice(f"{self.item_type}1"))
        self.button1.grid(row=1, column=0, padx=10, pady=10)

        self.button2 = tk.Button(master, text=f"{self.item_type}2", command=lambda: self.set_choice(f"{self.item_type}2"))
        self.button2.grid(row=1, column=1, padx=10, pady=10)
        
        return self.button1

    def set_choice(self, choice):
        self.choice = choice
        self.ok()
            
    def buttonbox(self):
        # 隐藏默认的OK/Cancel按钮
        pass

    @staticmethod
    def ask_which_ring_to_replace():
        return RingChoiceDialog.ask_which_item_to_replace("戒指")

    @staticmethod
    def ask_which_item_to_replace(item_type="戒指"):
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        dialog = RingChoiceDialog(root, title=f"选择{item_type}位置", item_type=item_type)
        return dialog.choice


if __name__ == '__main__':
    # 测试代码
    choice = RingChoiceDialog.ask_which_item_to_replace("手镯")
    print(f"选择的位置: {choice}")
    
    choice = RingChoiceDialog.ask_which_item_to_replace("戒指")
    print(f"选择的位置: {choice}") 