"""
背包数据管理
用于存储和管理玩家的背包物品数据
"""

import json
import os
from game.core.resource_manager import get_game_data_path

class Item:
    """
    物品基类
    """
    def __init__(self, id, name, icon_path=None, type="consumable", description="", stackable=True, max_stack_size=99):
        """
        初始化物品
        
        Args:
            id: 物品ID
            name: 物品名称
            icon_path: 物品图标路径
            type: 物品类型，可选值："equipment", "consumable", "skill_book"
            description: 物品描述
            stackable: 是否可堆叠
            max_stack_size: 最大堆叠数量
        """
        self.id = id
        self.name = name
        self.icon_path = icon_path
        self.type = type
        self.description = description
        self.stackable = stackable
        self.max_stack_size = max_stack_size
        
        # 兼容性属性
        self.max_stack = max_stack_size  # 兼容 max_stack 属性名
        
        # 物品图标 - 需要在游戏运行时加载
        self.icon = None
        
    def load_icon(self, resource_loader):
        """
        加载物品图标
        
        Args:
            resource_loader: 资源加载器
        """
        if self.icon_path:
            self.icon = resource_loader.load_image(self.icon_path)
        
    def can_stack_with(self, other_item):
        """
        检查是否可以与其他物品堆叠
        
        Args:
            other_item: 其他物品
            
        Returns:
            bool: 如果可以堆叠，则返回True
        """
        return (self.stackable and other_item.stackable and 
                self.id == other_item.id and 
                self.type == other_item.type)
                
    def __str__(self):
        return f"{self.name} ({self.type})"


class Equipment(Item):
    """
    装备类物品
    """
    def __init__(self, id, name, icon_path=None, description="", equipment_type="weapon", attributes=None, 
                 level_requirement=1, rarity="普通", category="", special="", sell_price=0, 
                 durability=None, max_durability=None):
        """
        初始化装备
        
        Args:
            id: 装备ID
            name: 装备名称
            icon_path: 装备图标路径
            description: 装备描述
            equipment_type: 装备类型，如"weapon", "armor"等
            attributes: 装备属性，字典格式
            level_requirement: 等级需求
            rarity: 品质/稀有度
            category: 装备类别
            special: 特殊效果描述
            sell_price: 售价
            durability: 当前耐久度
            max_durability: 最大耐久度
        """
        super().__init__(id, name, icon_path, "equipment", description, stackable=False)
        self.equipment_type = equipment_type
        self.attributes = attributes or {}
        self.level_requirement = level_requirement
        self.rarity = rarity
        self.category = category or equipment_type
        self.special = special
        self.sell_price = sell_price
        self.durability = durability
        self.max_durability = max_durability
        
    def __str__(self):
        return f"{self.name} ({self.equipment_type})"


class SkillBook(Item):
    """
    技能书类物品
    """
    def __init__(self, id, name, icon_path=None, description="", skill_id=None):
        """
        初始化技能书
        
        Args:
            id: 技能书ID
            name: 技能书名称
            icon_path: 技能书图标路径
            description: 技能书描述
            skill_id: 对应技能ID
        """
        super().__init__(id, name, icon_path, "skill_book", description, stackable=True, max_stack_size=10)
        self.skill_id = skill_id
        
    def __str__(self):
        return f"{self.name} (技能书)"


class UIInventoryManager:
    """
    UI背包管理器 - 专门为用户界面设计的背包系统
    支持分页显示、格子索引、可视化管理等UI功能
    """
    def __init__(self, resource_loader=None):
        """
        初始化UI背包管理器
        
        Args:
            resource_loader: 资源加载器，用于加载物品图标
        """
        # 物品数据，按类型分类
        self.equipment_items = {}  # key为格子索引(0-39)，value为物品对象
        self.consumable_items = {}  # key为格子索引，value为(物品对象, 数量)元组
        self.skill_book_items = {}  # key为格子索引，value为(物品对象, 数量)元组
        
        # 仓库物品数据
        self.warehouse_items = {}  # key为格子索引，value为(物品对象, 数量)元组
        
        # 每页格子数量和页数
        self.slots_per_page = 40  # 8*5=40
        self.current_equipment_page = 0
        self.current_consumable_page = 0
        self.current_skill_book_page = 0
        self.current_warehouse_page = 0
        
        # 资源加载器
        self.resource_loader = resource_loader
        
    def load_item_icons(self):
        """加载所有物品图标"""
        if not self.resource_loader:
            return
            
        # 加载装备图标
        for item in self.equipment_items.values():
            item.load_icon(self.resource_loader)
            
        # 加载道具图标
        for item, _ in self.consumable_items.values():
            item.load_icon(self.resource_loader)
            
        # 加载技能书图标
        for item, _ in self.skill_book_items.values():
            item.load_icon(self.resource_loader)
            
        # 加载仓库物品图标
        for item, _ in self.warehouse_items.values():
            item.load_icon(self.resource_loader)
    
    def add_item(self, item, quantity=1, container_type="consumable"):
        """
        添加物品到背包
        
        Args:
            item: 物品对象
            quantity: 物品数量
            container_type: 容器类型，可选值："equipment", "consumable", "skill_book", "warehouse"
            
        Returns:
            bool: 添加成功返回True，否则返回False
        """
        if container_type == "equipment":
            # 装备类型
            container = self.equipment_items
            # 装备不可堆叠，直接找一个空格子
            for i in range(self.slots_per_page):
                if i not in container:
                    container[i] = item
                    return True
        else:
            # 其他类型（可堆叠）
            if container_type == "consumable":
                container = self.consumable_items
            elif container_type == "skill_book":
                container = self.skill_book_items
            elif container_type == "warehouse":
                container = self.warehouse_items
            else:
                return False
                
            # 先尝试堆叠
            for slot_idx, (existing_item, existing_quantity) in container.items():
                if item.can_stack_with(existing_item) and existing_quantity + quantity <= item.max_stack_size:
                    container[slot_idx] = (existing_item, existing_quantity + quantity)
                    return True
                    
            # 如果无法堆叠，找一个空格子
            for i in range(self.slots_per_page):
                if i not in container:
                    container[i] = (item, quantity)
                    return True
                    
        # 背包已满
        return False
        
    def remove_item(self, slot_idx, quantity=1, container_type="consumable"):
        """
        从背包移除物品
        
        Args:
            slot_idx: 格子索引
            quantity: 移除数量
            container_type: 容器类型
            
        Returns:
            tuple: (移除的物品, 移除的数量)，如果移除失败则返回(None, 0)
        """
        if container_type == "equipment":
            # 装备类型
            container = self.equipment_items
            if slot_idx in container:
                item = container[slot_idx]
                del container[slot_idx]
                return (item, 1)
        else:
            # 其他类型（可堆叠）
            if container_type == "consumable":
                container = self.consumable_items
            elif container_type == "skill_book":
                container = self.skill_book_items
            elif container_type == "warehouse":
                container = self.warehouse_items
            else:
                return (None, 0)
                
            if slot_idx in container:
                item, existing_quantity = container[slot_idx]
                
                if quantity >= existing_quantity:
                    # 移除整个堆叠
                    del container[slot_idx]
                    return (item, existing_quantity)
                else:
                    # 移除部分堆叠
                    container[slot_idx] = (item, existing_quantity - quantity)
                    return (item, quantity)
                    
        return (None, 0)
        
    def move_item(self, from_slot, to_container, from_container="consumable", quantity=None):
        """
        移动物品从一个容器到另一个容器
        
        Args:
            from_slot: 源格子索引
            to_container: 目标容器类型
            from_container: 源容器类型
            quantity: 移动数量，如果为None则移动全部
            
        Returns:
            bool: 移动成功返回True，否则返回False
        """
        # 获取源容器
        source_container = None
        if from_container == "equipment":
            source_container = self.equipment_items
        elif from_container == "consumable":
            source_container = self.consumable_items
        elif from_container == "skill_book":
            source_container = self.skill_book_items
        elif from_container == "warehouse":
            source_container = self.warehouse_items
        else:
            return False
            
        # 检查源格子是否有物品
        if from_slot not in source_container:
            return False
            
        # 获取物品信息
        if from_container == "equipment":
            item = source_container[from_slot]
            item_quantity = 1
        else:
            item, item_quantity = source_container[from_slot]
            
        # 确定移动数量
        move_quantity = item_quantity if quantity is None else min(quantity, item_quantity)
        
        # 移除源物品
        removed_item, removed_quantity = self.remove_item(from_slot, move_quantity, from_container)
        if removed_item is None:
            return False
            
        # 添加到目标容器
        if removed_item.type == "equipment":
            target_container = "equipment"
        elif removed_item.type == "skill_book":
            target_container = "skill_book" if to_container != "warehouse" else "warehouse"
        else:
            target_container = "consumable" if to_container != "warehouse" else "warehouse"
            
        # 添加到目标容器
        return self.add_item(removed_item, removed_quantity, target_container)
        
    def get_items_by_page(self, page, container_type="consumable"):
        """
        获取指定页的物品
        
        Args:
            page: 页码，从0开始
            container_type: 容器类型
            
        Returns:
            dict: 格子索引到物品的映射
        """
        start_idx = page * self.slots_per_page
        end_idx = start_idx + self.slots_per_page
        
        # 获取容器
        if container_type == "equipment":
            container = self.equipment_items
        elif container_type == "consumable":
            container = self.consumable_items
        elif container_type == "skill_book":
            container = self.skill_book_items
        elif container_type == "warehouse":
            container = self.warehouse_items
        else:
            return {}
            
        # 筛选指定范围的物品
        page_items = {}
        for slot_idx, item_data in container.items():
            if start_idx <= slot_idx < end_idx:
                # 将全局索引转换为页内索引
                page_slot_idx = slot_idx - start_idx
                page_items[page_slot_idx] = item_data
                
        return page_items
        
    def get_total_pages(self, container_type="consumable"):
        """
        获取指定容器的总页数
        
        Args:
            container_type: 容器类型
            
        Returns:
            int: 总页数
        """
        # 获取容器
        if container_type == "equipment":
            container = self.equipment_items
        elif container_type == "consumable":
            container = self.consumable_items
        elif container_type == "skill_book":
            container = self.skill_book_items
        elif container_type == "warehouse":
            container = self.warehouse_items
        else:
            return 0
            
        # 计算总页数，至少有1页
        if not container:
            return 1
            
        max_slot_idx = max(container.keys()) if container else 0
        return max(1, (max_slot_idx // self.slots_per_page) + 1)
        
    def create_test_items(self):
        """创建测试物品数据"""
        # 创建一些测试装备
        sword = Equipment(1, "铁剑", None, "普通的铁剑", "weapon", {"攻击力": 10})
        armor = Equipment(2, "皮甲", None, "普通的皮甲", "armor", {"防御力": 5})
        helmet = Equipment(3, "头盔", None, "普通的头盔", "helmet", {"防御力": 3})
        
        # 创建一些测试道具
        health_potion = Item(101, "生命药水", None, "consumable", "恢复少量生命值", True, 99)
        mana_potion = Item(102, "魔法药水", None, "consumable", "恢复少量魔法值", True, 99)
        antidote = Item(103, "解毒剂", None, "consumable", "解除中毒状态", True, 99)
        
        # 创建一些测试技能书
        fireball_book = SkillBook(201, "火球术", None, "学习火球术", 1001)
        healing_book = SkillBook(202, "治疗术", None, "学习治疗术", 1002)
        teleport_book = SkillBook(203, "传送术", None, "学习传送术", 1003)
        
        # 添加测试装备
        self.add_item(sword, 1, "equipment")
        self.add_item(armor, 1, "equipment")
        self.add_item(helmet, 1, "equipment")
        
        # 添加测试道具
        self.add_item(health_potion, 10, "consumable")
        self.add_item(mana_potion, 5, "consumable")
        self.add_item(antidote, 3, "consumable")
        
        # 添加测试技能书
        self.add_item(fireball_book, 2, "skill_book")
        self.add_item(healing_book, 1, "skill_book")
        self.add_item(teleport_book, 1, "skill_book")
        
        # 添加测试仓库物品
        self.add_item(health_potion, 20, "warehouse")
        self.add_item(mana_potion, 15, "warehouse")
        
        print("已创建测试物品数据")
    
    def create_starter_items(self, character_class="战士", gender="男"):
        """
        为新角色创建初始物品
        
        Args:
            character_class: 角色职业
            gender: 角色性别
        """
        import json
        import os
        
        try:
            # 加载初始装备配置
            config_path = get_game_data_path("starter_equipment.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    starter_config = json.load(f)
            else:
                # 如果配置文件不存在，使用默认配置
                starter_config = self._get_default_starter_config()
            
            # 转换性别格式
            gender_key = "male" if gender == "男" else "female"
            
            # 获取该职业和性别的初始装备
            if character_class in starter_config["starter_equipment"]:
                char_config = starter_config["starter_equipment"][character_class][gender_key]
                
                # 添加初始消耗品到背包
                for consumable in char_config.get("consumables", []):
                    # 创建消耗品物品
                    item = Item(
                        id=hash(consumable["name"]) % 10000,
                        name=consumable["name"],
                        icon_path=None,
                        type="consumable",
                        description=f"新手{consumable['name']}",
                        stackable=True,
                        max_stack_size=consumable["quantity"]
                    )
                    self.add_item(item, consumable["quantity"], "consumable")
                
                print(f"✅ 为{character_class}({gender})创建了初始物品")
            else:
                print(f"⚠️ 未找到职业 {character_class} 的初始装备配置")
                
        except Exception as e:
            print(f"❌ 创建初始物品时出错: {e}")
            # 如果出错，创建默认的金创药
            health_potion = Item(101, "金创药(小量)", None, "consumable", "恢复少量生命值", True, 10)
            self.add_item(health_potion, 10, "consumable")
    
    def _get_default_starter_config(self):
        """获取默认的初始装备配置"""
        return {
            "starter_equipment": {
                "战士": {
                    "male": {
                        "weapon": "木剑",
                        "armor": "布衣(男)",
                        "consumables": [{"name": "金创药(小量)", "quantity": 10}]
                    },
                    "female": {
                        "weapon": "木剑",
                        "armor": "布衣(女)",
                        "consumables": [{"name": "金创药(小量)", "quantity": 10}]
                    }
                },
                "法师": {
                    "male": {
                        "weapon": "新手法杖",
                        "armor": "布衣(男)",
                        "consumables": [{"name": "金创药(小量)", "quantity": 10}]
                    },
                    "female": {
                        "weapon": "新手法杖",
                        "armor": "布衣(女)",
                        "consumables": [{"name": "金创药(小量)", "quantity": 10}]
                    }
                },
                "道士": {
                    "male": {
                        "weapon": "新手铃铛",
                        "armor": "布衣(男)",
                        "consumables": [{"name": "金创药(小量)", "quantity": 10}]
                    },
                    "female": {
                        "weapon": "新手铃铛",
                        "armor": "布衣(女)",
                        "consumables": [{"name": "金创药(小量)", "quantity": 10}]
                    }
                }
            }
        }
    
    def load_from_save_data(self, save_data):
        """
        从存档数据加载背包内容
        
        Args:
            save_data: 存档数据字典
        """
        try:
            inventory_data = save_data.get('inventory', {})
            
            # 如果有inventory字段且包含inventory列表，说明是从业务背包保存的数据
            if 'inventory' in inventory_data and isinstance(inventory_data['inventory'], list):
                items_list = inventory_data['inventory']
                
                # 将物品按类型分类并添加到对应容器
                for i, item_dict in enumerate(items_list):
                    if not isinstance(item_dict, dict):
                        continue
                        
                    # 创建Item对象
                    ui_item = Item(
                        item_dict.get('id', hash(item_dict.get('name', '')) % 10000),
                        item_dict.get('name', ''),
                        item_dict.get('icon_path'),
                        item_dict.get('type', 'consumable'),
                        item_dict.get('description', ''),
                        item_dict.get('stackable', True),
                        item_dict.get('max_stack', 99)
                    )
                    
                    quantity = item_dict.get('quantity', 1)
                    item_type = item_dict.get('type', 'consumable')
                    
                    # 确定容器类型并添加物品
                    if item_type == '装备' or item_type == 'equipment':
                        container_type = 'equipment'
                    elif item_type == '技能书' or item_type == 'skill_book':
                        container_type = 'skill_book'
                    else:
                        container_type = 'consumable'
                    
                    self.add_item(ui_item, quantity, container_type)
                
                print(f"✅ 从存档加载了 {len(items_list)} 个物品到背包")
            else:
                print("⚠️ 存档中没有找到背包数据")
                
        except Exception as e:
            print(f"❌ 加载背包数据时出错: {e}")
            import traceback
            traceback.print_exc() 