"""
装备图片缓存管理器
优化装备图片的加载和缓存，避免重复读取文件系统
"""
import os
import pygame
from typing import Dict, Optional, Tuple
from game.core.resource_manager import get_game_asset_path
import weakref


class EquipmentImageCache:
    """装备图片缓存管理器"""
    
    def __init__(self, max_cache_size: int = 100):
        """
        初始化图片缓存
        
        Args:
            max_cache_size: 最大缓存数量
        """
        self.max_cache_size = max_cache_size
        self._image_cache: Dict[str, pygame.Surface] = {}
        self._scaled_cache: Dict[Tuple[str, int, int], pygame.Surface] = {}
        self._access_order: list = []  # LRU访问顺序
        
        # 默认占位符图片
        self._placeholder_cache: Dict[Tuple[int, int], pygame.Surface] = {}
        
        # 图片存在性缓存（避免重复检查文件系统）
        self._exists_cache: Dict[str, bool] = {}
    
    def get_equipment_image(self, equipment_data: Dict, size: Tuple[int, int] = None) -> Optional[pygame.Surface]:
        """
        获取装备图片
        
        Args:
            equipment_data: 装备数据
            size: 目标尺寸，如果为None则返回原始尺寸
            
        Returns:
            装备图片表面，如果失败则返回None
        """
        if not equipment_data:
            return None
        
        # 生成图片路径
        icon_path = self._generate_icon_path(equipment_data)
        if not icon_path:
            return None
        
        # 如果需要缩放，检查缩放缓存
        if size:
            cache_key = (icon_path, size[0], size[1])
            if cache_key in self._scaled_cache:
                self._update_access_order(f"scaled_{cache_key}")
                return self._scaled_cache[cache_key]
        
        # 获取原始图片
        original_image = self._load_image(icon_path)
        if not original_image:
            return None
        
        # 如果需要缩放
        if size and (original_image.get_width() != size[0] or original_image.get_height() != size[1]):
            scaled_image = pygame.transform.scale(original_image, size)
            cache_key = (icon_path, size[0], size[1])
            
            # 添加到缩放缓存
            self._add_to_scaled_cache(cache_key, scaled_image)
            self._update_access_order(f"scaled_{cache_key}")
            
            return scaled_image
        
        return original_image
    
    def get_placeholder_image(self, size: Tuple[int, int], color: Tuple[int, int, int] = (100, 100, 100)) -> pygame.Surface:
        """
        获取占位符图片
        
        Args:
            size: 图片尺寸
            color: 背景颜色
            
        Returns:
            占位符图片表面
        """
        cache_key = (size[0], size[1])
        
        if cache_key not in self._placeholder_cache:
            placeholder = pygame.Surface(size, pygame.SRCALPHA)
            placeholder.fill(color)
            
            # 绘制简单的装备图标占位符
            center_x, center_y = size[0] // 2, size[1] // 2
            pygame.draw.rect(placeholder, (color[0] + 30, color[1] + 30, color[2] + 30),
                           (center_x - 8, center_y - 8, 16, 16))
            
            self._placeholder_cache[cache_key] = placeholder
        
        return self._placeholder_cache[cache_key]
    
    def preload_equipment_images(self, equipment_list: list, size: Tuple[int, int] = None):
        """
        预加载装备图片
        
        Args:
            equipment_list: 装备数据列表
            size: 目标尺寸
        """
        for equipment in equipment_list:
            if equipment:
                self.get_equipment_image(equipment, size)
    
    def clear_cache(self):
        """清空所有缓存"""
        self._image_cache.clear()
        self._scaled_cache.clear()
        self._access_order.clear()
        self._exists_cache.clear()
        
        # 保留占位符缓存，因为它们很轻量
        # self._placeholder_cache.clear()
    
    def get_cache_info(self) -> Dict[str, int]:
        """
        获取缓存信息
        
        Returns:
            缓存统计信息
        """
        return {
            'original_images': len(self._image_cache),
            'scaled_images': len(self._scaled_cache),
            'placeholders': len(self._placeholder_cache),
            'exists_cache': len(self._exists_cache),
            'total_memory_items': len(self._image_cache) + len(self._scaled_cache)
        }
    
    def _load_image(self, icon_path: str) -> Optional[pygame.Surface]:
        """加载原始图片"""
        # 检查原始图片缓存
        if icon_path in self._image_cache:
            self._update_access_order(icon_path)
            return self._image_cache[icon_path]
        
        # 检查文件是否存在（使用缓存）
        if not self._file_exists(icon_path):
            return None
        
        try:
            # 构建完整路径
            full_path = get_game_asset_path(f"images/equipment/{icon_path}")
            
            # 加载图片
            image = pygame.image.load(full_path).convert_alpha()
            
            # 添加到缓存
            self._add_to_image_cache(icon_path, image)
            self._update_access_order(icon_path)
            
            return image
            
        except Exception as e:
            print(f"加载装备图片失败: {icon_path}, 错误: {e}")
            # 将失败结果也缓存起来，避免重复尝试
            self._exists_cache[icon_path] = False
            return None
    
    def _file_exists(self, icon_path: str) -> bool:
        """检查文件是否存在（带缓存）"""
        if icon_path in self._exists_cache:
            return self._exists_cache[icon_path]
        
        full_path = get_game_asset_path(f"images/equipment/{icon_path}")
        exists = os.path.exists(full_path)
        
        # 缓存结果
        self._exists_cache[icon_path] = exists
        
        return exists
    
    def _generate_icon_path(self, equipment_data: Dict) -> Optional[str]:
        """生成装备图标路径"""
        # 优先使用配置的图标路径
        icon_path = equipment_data.get('icon_path')
        if icon_path:
            return icon_path
        
        # 根据装备名称和槽位生成路径
        item_name = equipment_data.get('name', '')
        slot = equipment_data.get('slot', '')
        
        if not item_name or not slot:
            return None
        
        # 槽位到目录的映射
        slot_to_directory = {
            '武器': '武器',
            '头盔': '头盔',
            '胸甲': '防具',
            '护腿': '防具',
            '靴子': '防具',
            '护符': '项链',
            '戒指': '戒指',
            '手镯': '手镯'
        }
        
        directory = slot_to_directory.get(slot)
        if directory:
            return f"{directory}/{item_name}.png"
        
        return None
    
    def _add_to_image_cache(self, key: str, image: pygame.Surface):
        """添加图片到原始缓存"""
        # 如果缓存已满，移除最久未使用的项
        if len(self._image_cache) >= self.max_cache_size:
            self._remove_lru_item()
        
        self._image_cache[key] = image
    
    def _add_to_scaled_cache(self, key: Tuple[str, int, int], image: pygame.Surface):
        """添加图片到缩放缓存"""
        # 缩放缓存使用更小的限制
        max_scaled_cache = self.max_cache_size // 2
        
        if len(self._scaled_cache) >= max_scaled_cache:
            # 移除最久未使用的缩放图片
            for access_key in self._access_order[:]:
                if access_key.startswith("scaled_"):
                    cache_key = eval(access_key[7:])  # 移除 "scaled_" 前缀
                    if cache_key in self._scaled_cache:
                        del self._scaled_cache[cache_key]
                        self._access_order.remove(access_key)
                        break
        
        self._scaled_cache[key] = image
    
    def _update_access_order(self, key: str):
        """更新访问顺序（LRU）"""
        if key in self._access_order:
            self._access_order.remove(key)
        self._access_order.append(key)
    
    def _remove_lru_item(self):
        """移除最久未使用的项"""
        if not self._access_order:
            return
        
        # 找到最久未使用的原始图片
        for key in self._access_order[:]:
            if not key.startswith("scaled_") and key in self._image_cache:
                del self._image_cache[key]
                self._access_order.remove(key)
                break


# 创建全局图片缓存实例
equipment_image_cache = EquipmentImageCache() 