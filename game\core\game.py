import pygame
import sys
from game.ui.ui_manager import UIManager
from game.models.player_refactored import Player
from game.core.map_manager import MapManager
from game.core.battle_manager import BattleManager
from game.managers.rank_manager import Rank<PERSON>anager
from game.managers.data_manager import DataManager
from game.managers.potion_effects_manager import PotionEffectsManager

class Game:
    """
    游戏主类，负责管理游戏主循环和其他组件
    """
    def __init__(self):
        # 设置游戏窗口大小
        self.screen_width = 1024
        self.screen_height = 768
        self.screen = pygame.display.set_mode((self.screen_width, self.screen_height))
        pygame.display.set_caption("2D角色成长游戏")
        
        # 设置游戏时钟
        self.clock = pygame.time.Clock()
        self.fps = 60
        
        # 创建游戏组件
        self.data_manager = DataManager()
        self.player = Player()
        self.map_manager = MapManager(self.data_manager)
        self.battle_manager = BattleManager(self.data_manager)
        self.rank_manager = RankManager()
        
        # 创建药水效果管理器
        self.potion_effects_manager = PotionEffectsManager(self.player)
        
        # 初始化UI管理器
        self.ui_manager = UIManager(self.screen, self.player, 
                                  self.map_manager, self.battle_manager, 
                                  self.rank_manager, self.potion_effects_manager)
        
        # 游戏运行标志
        self.running = True
        
        # 游戏初始化
        self.initialize_game()
        
        # 启动药水效果管理器
        self.potion_effects_manager.start()
    
    def initialize_game(self):
        """
        初始化游戏数据和状态
        """
        # 加载游戏数据
        self.data_manager.load_game_data()
        
        # 初始化玩家数据
        player_data = self.data_manager.get_player_data()
        if player_data:
            self.player.load_data(player_data)
        
        # 初始化地图数据
        map_data_from_save = self.data_manager.get_player_data().get("map_manager_data") # 尝试从玩家存档中获取地图管理器数据
        if map_data_from_save:
            self.map_manager.load_data(map_data_from_save)
        
        # 如果 MapManager 的 available_maps 仍为空 (例如新游戏或存档中没有地图数据)
        # 则从 maps_config.json 加载默认地图配置
        if not self.map_manager.available_maps:
            maps_config_data = self.data_manager.get_map_data() # 这会从 maps_config.json 读取
            if maps_config_data and "maps" in maps_config_data:
                self.map_manager.available_maps = maps_config_data["maps"]
            elif maps_config_data and isinstance(maps_config_data, list): # 兼容直接是列表的情况
                 self.map_manager.available_maps = maps_config_data
            else:
                print("警告: 未能从 maps_config.json 加载地图数据，或格式不正确。")
                self.map_manager.available_maps = [] # 确保为空列表
        
        # 初始化排行榜数据
        rank_data = self.data_manager.get_rank_data()
        if rank_data:
            self.rank_manager.load_data(rank_data)
    
    def handle_events(self):
        """
        处理游戏事件
        """
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            
            # 将事件传递给UI管理器处理
            self.ui_manager.handle_event(event)
    
    def update(self):
        """
        更新游戏状态
        """
        # 更新战斗管理器
        self.battle_manager.update()
        
        # 更新UI管理器
        self.ui_manager.update()
    
    def render(self):
        """
        渲染游戏画面
        """
        # 清空屏幕
        self.screen.fill((30, 30, 30))
        
        # 渲染UI
        self.ui_manager.render()
        
        # 更新屏幕
        pygame.display.flip()
    
    def save_game(self):
        """
        保存游戏数据
        """
        # 保存玩家数据
        self.data_manager.save_player_data(self.player.get_data())
        
        # 保存地图数据
        self.data_manager.save_map_data(self.map_manager.get_data())
        
        # 保存排行榜数据
        self.data_manager.save_rank_data(self.rank_manager.get_data())
    
    def run(self):
        """
        运行游戏主循环
        """
        while self.running:
            # 处理事件
            self.handle_events()
            
            # 更新游戏状态
            self.update()
            
            # 渲染游戏画面
            self.render()
            
            # 控制游戏帧率
            self.clock.tick(self.fps)
        
        # 退出前保存游戏
        self.save_game()
        
        # 清理药水效果管理器
        if hasattr(self, 'potion_effects_manager'):
            self.potion_effects_manager.cleanup()