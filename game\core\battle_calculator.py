"""
战斗计算器
处理战斗过程中的伤害计算、命中判定、暴击判定等
"""
import random
import math
from game.core.luck_system import LuckSystem

class BattleCalculator:
    """
    战斗计算器类
    负责处理战斗中的各种数值计算
    """
    
    # 基础计算参数
    BASE_HIT_CHANCE = 0.8       # 基础命中率
    MAX_HIT_CHANCE = 0.95       # 最大命中率
    MIN_HIT_CHANCE = 0.2        # 最小命中率
    
    BASE_CRITICAL_CHANCE = 0.05  # 基础暴击率
    MAX_CRITICAL_CHANCE = 0.5    # 最大暴击率
    MIN_CRITICAL_CHANCE = 0.01   # 最小暴击率
    
    BASE_DODGE_CHANCE = 0.05     # 基础闪避率
    MAX_DODGE_CHANCE = 0.4       # 最大闪避率
    MIN_DODGE_CHANCE = 0.01      # 最小闪避率
    
    @staticmethod
    def calculate_hit_chance(attacker, defender):
        """
        计算命中率
        
        Args:
            attacker: 攻击者
            defender: 防御者
            
        Returns:
            命中率(0.0-1.0)
        """
        # 基础命中率 + 攻击者命中值与防御者敏捷值的差异影响
        accuracy = attacker.stats.get_accuracy()
        agility = defender.stats.get_agility()
        
        hit_chance = BattleCalculator.BASE_HIT_CHANCE + (accuracy - agility) * 0.01
        
        # 确保命中率在合理范围内
        return max(BattleCalculator.MIN_HIT_CHANCE, min(BattleCalculator.MAX_HIT_CHANCE, hit_chance))
    
    @staticmethod
    def calculate_critical_chance(attacker, defender):
        """
        计算暴击率
        
        Args:
            attacker: 攻击者
            defender: 防御者
            
        Returns:
            暴击率(0.0-1.0)
        """
        # 基础暴击率
        critical_chance = attacker.stats.critical_rate
        
        # 确保暴击率在合理范围内
        return max(BattleCalculator.MIN_CRITICAL_CHANCE, min(BattleCalculator.MAX_CRITICAL_CHANCE, critical_chance))
    
    @staticmethod
    def calculate_dodge_chance(defender, attacker):
        """
        计算闪避率
        
        Args:
            defender: 防御者
            attacker: 攻击者
            
        Returns:
            闪避率(0.0-1.0)
        """
        # 基础闪避率 + 敏捷与命中值的差异影响
        agility = defender.stats.get_agility()
        accuracy = attacker.stats.get_accuracy()
        
        dodge_chance = BattleCalculator.BASE_DODGE_CHANCE + (agility - accuracy) * 0.005
        
        # 确保闪避率在合理范围内
        return max(BattleCalculator.MIN_DODGE_CHANCE, min(BattleCalculator.MAX_DODGE_CHANCE, dodge_chance))
    
    @staticmethod
    def calculate_damage(attacker, defender, skill_power=1.0, damage_type="physical"):
        """
        计算伤害值
        
        Args:
            attacker: 攻击者
            defender: 防御者
            skill_power: 技能威力倍率
            damage_type: 伤害类型 (physical/magic/tao/true)
            
        Returns:
            (基础伤害, 是否暴击, 最终伤害)
        """
        # 判断是否命中
        if not BattleCalculator.check_hit(attacker, defender):
            return 0, False, 0
        
        # 判断是否暴击
        is_critical = BattleCalculator.check_critical(attacker, defender)
        
        # 根据伤害类型计算基础伤害（幸运值已经在各个攻击方法中处理）
        if damage_type == "physical":
            base_damage = attacker.calculate_attack(is_critical) * skill_power
        elif damage_type == "magic":
            base_damage = attacker.calculate_magic_attack(skill_power, is_critical)
        elif damage_type == "tao":
            base_damage = attacker.calculate_tao_attack(skill_power, is_critical)
        else:  # true damage
            base_damage = int(attacker.stats.get_attack_range()[1] * skill_power)
            if is_critical:
                base_damage = int(base_damage * attacker.stats.critical_damage)
        
        # 计算最终伤害
        final_damage = defender.take_damage(base_damage, damage_type, is_critical)
        
        return base_damage, is_critical, final_damage
    
    @staticmethod
    def check_hit(attacker, defender):
        """
        检查攻击是否命中
        
        Args:
            attacker: 攻击者
            defender: 防御者
            
        Returns:
            是否命中
        """
        # 如果防御者已经死亡，总是命中
        if not defender.is_alive:
            return True
            
        # 检查是否闪避
        if BattleCalculator.check_dodge(defender, attacker):
            return False
            
        # 计算命中率
        hit_chance = BattleCalculator.calculate_hit_chance(attacker, defender)
        
        # 随机判断是否命中
        return random.random() < hit_chance
    
    @staticmethod
    def check_dodge(defender, attacker):
        """
        检查是否闪避
        
        Args:
            defender: 防御者
            attacker: 攻击者
            
        Returns:
            是否闪避
        """
        # 如果防御者已经死亡，不能闪避
        if not defender.is_alive:
            return False
            
        # 计算闪避率
        dodge_chance = BattleCalculator.calculate_dodge_chance(defender, attacker)
        
        # 随机判断是否闪避
        return random.random() < dodge_chance
    
    @staticmethod
    def check_critical(attacker, defender):
        """
        检查是否暴击
        
        Args:
            attacker: 攻击者
            defender: 防御者
            
        Returns:
            是否暴击
        """
        # 计算暴击率
        critical_chance = BattleCalculator.calculate_critical_chance(attacker, defender)
        
        # 随机判断是否暴击
        return random.random() < critical_chance
    
    @staticmethod
    def calculate_combat_value(entity):
        """
        计算战斗力
        
        Args:
            entity: 战斗实体
            
        Returns:
            战斗力数值
        """
        if not hasattr(entity, 'stats'):
            return 0
        
        stats = entity.stats
        
        # 检查stats是否有必要的方法
        if not hasattr(stats, 'get_max_hp'):
            return 0
        
        # 基础属性贡献
        try:
            hp_factor = stats.get_max_hp() * 0.2
            mp_factor = stats.get_max_mp() * 0.1
        except Exception as e:
            return 0
        
        # 攻击属性贡献
        attack_factor = (stats.attack_min + stats.attack_max) * 0.5
        magic_factor = (stats.magic_min + stats.magic_max) * 0.4
        tao_factor = (stats.tao_min + stats.tao_max) * 0.4
        
        # 防御属性贡献
        defense_factor = (stats.defense_min + stats.defense_max) * 0.3
        magic_defense_factor = stats.magic_defense * 0.3
        
        # 命中与闪避贡献
        accuracy_factor = stats.get_accuracy() * 0.2
        agility_factor = stats.get_agility() * 0.2
        
        # 其他属性贡献
        critical_factor = stats.critical_rate * 100
        speed_factor = stats.attack_speed * 10
        
        # 计算总战斗力（移除幸运值和诅咒值的影响）
        combat_value = (
            hp_factor + mp_factor +
            attack_factor + magic_factor + tao_factor +
            defense_factor + magic_defense_factor +
            accuracy_factor + agility_factor +
            critical_factor + speed_factor
        )
        
        # 取整
        return math.ceil(combat_value)
    
    @staticmethod
    def calculate_xp_reward(attacker, defender, won=True):
        """
        计算经验奖励
        
        Args:
            attacker: 攻击者（获得经验的一方）
            defender: 防御者（被击败的一方）
            won: 是否获胜
            
        Returns:
            经验值奖励
        """
        if not won:
            # 失败时不获得经验
            return 0
        
        # 基础经验值为1
        base_xp = 1
        
        # VIP等级倍率修正（如果攻击者有VIP等级）
        vip_modifier = 1.0
        if hasattr(attacker, 'vip_level'):
            # VIP等级每级增加10%经验奖励
            vip_modifier = 1.0 + (attacker.vip_level * 0.1)
        
        # 计算最终经验值
        xp = base_xp * vip_modifier
        
        return int(xp)
    
    @staticmethod
    def calculate_gold_reward(attacker, defender, won=True):
        """
        计算金币奖励
        
        Args:
            attacker: 攻击者（获得金币的一方）
            defender: 防御者（被击败的一方）
            won: 是否获胜
            
        Returns:
            金币奖励
        """
        if not won:
            # 失败时不获得金币
            return 0
            
        # 基础金币为1
        base_gold = 1
        
        # VIP等级倍率修正（如果攻击者有VIP等级）
        vip_modifier = 1.0
        if hasattr(attacker, 'vip_level'):
            # VIP等级每级增加10%金币奖励
            vip_modifier = 1.0 + (attacker.vip_level * 0.1)
        
        # 计算最终金币
        gold = base_gold * vip_modifier
        
        return int(gold)