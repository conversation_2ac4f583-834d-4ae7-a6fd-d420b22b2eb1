#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
萝卜传奇游戏 - 智能EXE打包工具
支持生成带控制台和无控制台版本的单文件exe
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

class GamePackager:
    def __init__(self):
        self.project_root = Path.cwd()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.specs_dir = self.project_root / "specs"
        
        # 确保specs目录存在
        self.specs_dir.mkdir(exist_ok=True)
        
        # 打包配置
        self.app_name = "萝卜传奇"
        self.main_script = "main.py"
        self.icon_path = "game/assets/images/icon.png"
        
    def print_banner(self):
        """打印程序横幅"""
        print("=" * 60)
        print("🎮 萝卜传奇游戏 - 智能EXE打包工具 🎮")
        print("=" * 60)
        print()
        
    def check_environment(self):
        """检查打包环境"""
        print("🔍 检查打包环境...")
        
        # 检查Python环境
        python_version = sys.version_info
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            pyi_version = PyInstaller.__version__
            print(f"✅ PyInstaller已安装: {pyi_version}")
            
            # 检查PyInstaller版本兼容性
            if pyi_version.startswith('6.'):
                print("⚠️  检测到PyInstaller 6.x版本，可能存在兼容性问题")
                print("   建议使用PyInstaller 5.x版本以获得更好的稳定性")
            elif pyi_version.startswith('5.'):
                print("✅ PyInstaller版本良好，兼容性佳")
            
        except ImportError:
            print("❌ PyInstaller未安装，正在安装...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller==5.13.2"], check=True)
            print("✅ PyInstaller安装完成")
            
        # 检查主脚本
        if not (self.project_root / self.main_script).exists():
            print(f"❌ 找不到主脚本: {self.main_script}")
            return False
            
        # 检查游戏资源
        game_dir = self.project_root / "game"
        if not game_dir.exists():
            print("❌ 找不到游戏目录: game/")
            return False
            
        print("✅ 环境检查完成")
        print()
        return True
        
    def clean_build_files(self):
        """清理旧的构建文件"""
        print("🧹 清理旧的构建文件...")
        
        dirs_to_clean = [self.dist_dir, self.build_dir]
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"✅ 清理完成: {dir_path}")
                
        print()
        
    def get_all_data_files(self):
        """获取所有需要打包的数据文件"""
        data_files = []
        
        # 使用绝对路径，确保能正确找到文件
        base_path = str(self.project_root)
        
        # 检查并添加存在的目录
        potential_dirs = [
            ("game/assets", "game/assets"),
            ("game/data", "game/data"),
            ("ui", "ui"),
            ("logs", "logs"),
            ("saves", "saves"),
        ]
        
        for src_dir, dst_dir in potential_dirs:
            src_path = self.project_root / src_dir
            if src_path.exists():
                # 使用绝对路径
                abs_src = str(src_path)
                data_files.append((abs_src, dst_dir))
                print(f"📁 添加数据目录: {src_dir} -> {dst_dir}")
            else:
                # 如果目录不存在，创建空目录（对于logs和saves）
                if src_dir in ["logs", "saves"]:
                    src_path.mkdir(exist_ok=True)
                    data_files.append((str(src_path), dst_dir))
                    print(f"📁 创建并添加目录: {src_dir} -> {dst_dir}")
                else:
                    print(f"⚠️  跳过不存在的目录: {src_dir}")
        
        return data_files
        
    def get_hidden_imports(self):
        """获取隐藏导入的模块列表"""
        hidden_imports = [
            # 游戏核心模块
            "game.core.game",
            "game.core.data_manager", 
            "game.core.resource_manager",
            "game.core.battle_manager",
            "game.core.battle_calculator",
            "game.core.map_manager",
            "game.core.pathfinding",
            "game.core.luck_system",
            "game.core.log_manager",
            "game.core.log_config",
            "game.core.user_message_manager",
            "game.core.error_message_converter",
            "game.core.enemy_image_manager",
            "game.core.monster_distribution_manager",
            
            # UI模块
            "game.ui.ui_manager",
            "game.ui.ui_panel",
            "game.ui.start_menu_panel",
            "game.ui.character_creation_panel",
            "game.ui.inventory_panel",
            "game.ui.equipment_panel",
            "game.ui.battle_panel",
            "game.ui.skill_panel",
            "game.ui.map_panel",
            "game.ui.info_panel",
            "game.ui.log_panel",
            "game.ui.rank_panel",
            "game.ui.item_slot",
            "game.ui.font_manager",
            "game.ui.damage_text_manager",
            "game.ui.announcement_panel",
            "game.ui.auto_potion_panel",
            "game.ui.medicine_panel",
            "game.ui.warehouse_panel",
            "game.ui.boss_refresh_panel",
            
            # 数据模块
            "game.data.inventory",
            "game.data.equipment_loader",
            
            # 管理器模块
            "game.managers.inventory_manager",
            "game.managers.equipment_manager",
            "game.managers.skill_manager",
            "game.managers.save_load_manager",
            "game.managers.quest_manager",
            "game.managers.checkin_manager",
            "game.managers.points_shop_manager",
            
            # 模型模块
            "game.models.player_refactored",
            "game.models.enemy",
            "game.models.battle_entity",
            "game.models.character_stats",
            "game.models.rank_manager",
            "game.models.skill_loader",
            "game.models.monster_data_loader",
            
            # 系统模块
            "game.systems.item_generator",
            
            # UI外部模块
            "ui.checkin_panel",
            "ui.points_shop_panel",
            
            # 第三方库
            "pygame",
            "tkinter",
            "tkinter.ttk",
            "tkinter.messagebox",
            "tkinter.filedialog",
            "pyperclip",
            "PIL",
            "PIL.Image",
            "PIL.ImageTk",
            
            # 必需的标准库模块
            "email",
            "email.mime",
            "email.mime.text",
            "pkg_resources",
            "setuptools",
        ]
        
        return hidden_imports
        
    def get_excluded_modules(self):
        """获取要排除的模块列表"""
        excludes = [
            # 科学计算库（游戏不需要）
            "numpy",
            "scipy",
            "matplotlib",
            "pandas",
            "sklearn",
            "tensorflow",
            "torch",
            
            # 开发工具
            "pytest",
            "doctest",
            "pydoc",
            
            # 网络相关（游戏不需要）
            "urllib3",
            "requests",
            
            # 注意：不要排除Python标准库模块如email, http, unittest等
            # 这些可能被其他模块依赖
        ]
        
        return excludes
        
    def create_spec_file(self, console=True):
        """创建PyInstaller的spec文件"""
        spec_name = "game_console.spec" if console else "game_windowed.spec"
        exe_name = f"{self.app_name}_控制台版" if console else f"{self.app_name}_窗口版"
        
        # 使用绝对路径确保能找到主脚本
        main_script_path = str(self.project_root / self.main_script)
        
        # 检查图标文件是否存在
        icon_path = self.project_root / self.icon_path
        if icon_path.exists():
            icon_full_path = str(icon_path)
        else:
            icon_full_path = None
            print(f"⚠️  图标文件不存在: {self.icon_path}，将使用默认图标")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件配置
datas = {self.get_all_data_files()}

# 隐藏导入模块
hiddenimports = {self.get_hidden_imports()}

# 排除的模块
excludes = {self.get_excluded_modules()}

a = Analysis(
    [r'{main_script_path}'],
    pathex=[r'{str(self.project_root)}'],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{exe_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console={console},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon={f"r'{icon_full_path}'" if icon_full_path else "None"},
)
'''
        
        spec_file = self.specs_dir / spec_name
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
            
        return spec_file
        
    def build_exe(self, console=True):
        """构建exe文件"""
        version_name = "控制台版" if console else "窗口版"
        print(f"🔨 开始构建 {version_name}...")
        
        # 创建spec文件
        spec_file = self.create_spec_file(console)
        print(f"✅ 创建spec文件: {spec_file}")
        
        # 运行PyInstaller
        cmd = [
            "pyinstaller",
            "--clean",
            "--noconfirm",
            str(spec_file)
        ]
        
        print(f"📦 执行打包命令: {' '.join(cmd)}")
        print("⏳ 打包进行中，请耐心等待...")
        
        start_time = time.time()
        
        try:
            # 从项目根目录运行PyInstaller
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, 
                                  cwd=str(self.project_root))
            end_time = time.time()
            
            print(f"✅ {version_name} 打包完成！")
            print(f"⏱️  用时: {end_time - start_time:.1f} 秒")
            
            # 查找生成的exe文件
            exe_name = f"{self.app_name}_控制台版.exe" if console else f"{self.app_name}_窗口版.exe"
            exe_path = self.dist_dir / exe_name
            
            if exe_path.exists():
                file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📁 输出文件: {exe_path}")
                print(f"📏 文件大小: {file_size:.1f} MB")
                return exe_path
            else:
                print("❌ 找不到生成的exe文件")
                return None
                
        except subprocess.CalledProcessError as e:
            print(f"❌ 打包失败: {e}")
            print("\n📋 详细错误信息:")
            if e.stdout:
                print("=" * 50)
                print("标准输出:")
                print(e.stdout)
            if e.stderr:
                print("=" * 50)
                print("错误输出:")
                print(e.stderr)
            
            print("\n💡 可能的解决方案:")
            print("1. 检查所有依赖是否已安装: pip install -r requirements.txt")
            print("2. 确保所有游戏模块导入正常")
            print("3. 运行 python test_packaging.py 进行诊断")
            print("4. 检查项目路径中是否有中文字符或特殊字符")
            
            return None
            
    def show_menu(self):
        """显示主菜单"""
        while True:
            print("\n" + "=" * 40)
            print("🎯 请选择要生成的exe版本:")
            print("=" * 40)
            print("1. 🖥️  控制台版 (带调试窗口)")
            print("2. 🎮 窗口版 (纯净游戏体验)")
            print("3. 🚀 生成两个版本")
            print("4. 🧹 清理构建文件")
            print("0. ❌ 退出")
            print("=" * 40)
            
            choice = input("请输入选择 (0-4): ").strip()
            
            if choice == "1":
                return "console"
            elif choice == "2":
                return "windowed"
            elif choice == "3":
                return "both"
            elif choice == "4":
                self.clean_build_files()
                print("✅ 清理完成")
            elif choice == "0":
                return "exit"
            else:
                print("❌ 无效选择，请重新输入")
                
    def run(self):
        """运行打包程序"""
        self.print_banner()
        
        # 检查环境
        if not self.check_environment():
            input("按回车键退出...")
            return
            
        while True:
            choice = self.show_menu()
            
            if choice == "exit":
                print("👋 再见!")
                break
            elif choice == "console":
                self.clean_build_files()
                exe_path = self.build_exe(console=True)
                if exe_path:
                    self.offer_test_run(exe_path)
            elif choice == "windowed":
                self.clean_build_files()
                exe_path = self.build_exe(console=False)
                if exe_path:
                    self.offer_test_run(exe_path)
            elif choice == "both":
                self.clean_build_files()
                print("🔄 开始生成两个版本...")
                
                # 生成控制台版
                console_exe = self.build_exe(console=True)
                
                # 生成窗口版
                windowed_exe = self.build_exe(console=False)
                
                # 总结
                print("\n" + "=" * 50)
                print("🎉 所有版本生成完成!")
                print("=" * 50)
                if console_exe:
                    print(f"🖥️  控制台版: {console_exe}")
                if windowed_exe:
                    print(f"🎮 窗口版: {windowed_exe}")
                print("=" * 50)
                
                if console_exe or windowed_exe:
                    test_choice = input("\n🚀 是否测试运行? (1=控制台版, 2=窗口版, 0=跳过): ").strip()
                    if test_choice == "1" and console_exe:
                        self.test_run_exe(console_exe)
                    elif test_choice == "2" and windowed_exe:
                        self.test_run_exe(windowed_exe)
                        
    def offer_test_run(self, exe_path):
        """提供测试运行选项"""
        test = input(f"\n🚀 是否立即测试运行 {exe_path.name}? (y/n): ").strip().lower()
        if test in ['y', 'yes', '是']:
            self.test_run_exe(exe_path)
            
    def test_run_exe(self, exe_path):
        """测试运行exe文件"""
        print(f"🎮 启动游戏: {exe_path.name}")
        try:
            subprocess.Popen([str(exe_path)], cwd=str(exe_path.parent))
            print("✅ 游戏已启动")
        except Exception as e:
            print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    packager = GamePackager()
    packager.run() 