{"categories": {"武器": {"name": "武器", "description": "各种职业的基础武器", "icon": "weapon", "items": [{"item_id": "木剑", "name": "木剑", "description": "初学者使用的基础木制长剑", "gold_cost": 100, "stock": -1, "level_requirement": 1, "class_requirement": ["战士"]}, {"item_id": "铁剑", "name": "铁剑", "description": "坚固耐用的铁制长剑", "gold_cost": 500, "stock": -1, "level_requirement": 5, "class_requirement": ["战士"]}]}, "防具": {"name": "防具", "description": "各种职业的基础防护装备", "icon": "armor", "items": [{"item_id": "布衣(男)", "name": "布衣", "description": "简单的布制衣物，提供基础防护", "gold_cost": 80, "stock": -1, "level_requirement": 1, "class_requirement": ["法师", "道士"]}, {"item_id": "轻型盔甲(男)", "name": "轻型盔甲", "description": "轻便的皮革护甲，平衡防护与灵活", "gold_cost": 300, "stock": -1, "level_requirement": 3, "class_requirement": ["战士"]}, {"item_id": "中型盔甲(男)", "name": "中型盔甲", "description": "坚固的金属护甲，提供良好防护", "gold_cost": 800, "stock": -1, "level_requirement": 8, "class_requirement": ["战士"]}]}, "饰品": {"name": "饰品", "description": "提升属性的各种饰品", "icon": "accessory", "items": [{"item_id": "生铁戒指", "name": "生铁戒指", "description": "简单的铁制戒指", "gold_cost": 200, "stock": -1, "level_requirement": 1}, {"item_id": "铜戒指", "name": "铜戒指", "description": "精致的铜制戒指，提升攻击力", "gold_cost": 600, "stock": -1, "level_requirement": 5}, {"item_id": "小手镯", "name": "小手镯", "description": "精美的小手镯", "gold_cost": 250, "stock": -1, "level_requirement": 2}, {"item_id": "大手镯", "name": "大手镯", "description": "华丽的大手镯", "gold_cost": 750, "stock": -1, "level_requirement": 7}]}, "药品": {"name": "药品", "description": "基础的恢复药品", "icon": "potion", "items": [{"item_id": "鹿血", "name": "鹿血", "description": "珍贵的鹿血，快速恢复生命和魔法", "gold_cost": 25, "stock": -1, "stackable": true, "max_stack": 20}]}}, "shop_settings": {"refresh_time": "00:00", "currency_type": "gold", "max_daily_purchases": -1, "discount_enabled": false, "bulk_purchase_enabled": true}}