class RankManager:
    """
    排行榜管理器，负责游戏排行榜的管理
    """
    def __init__(self):
        # 初始化排行榜数据
        self.ranks = {
            "battle_power": [],  # 战力排行榜
            "level": []          # 等级排行榜
        }
        
        # 排行榜显示数量限制
        self.max_rank_display = 10
        
        # 当前显示的排行榜类型
        self.current_rank_type = "battle_power"
        
        # 当前排行榜页码
        self.current_page = 0
    
    def load_data(self, data):
        """
        从数据中加载排行榜信息
        """
        if "battle_power" in data:
            self.ranks["battle_power"] = data["battle_power"]
        
        if "level" in data:
            self.ranks["level"] = data["level"]
    
    def get_data(self):
        """
        获取排行榜数据，用于保存
        """
        return self.ranks
    
    def update_player_rank(self, player_data):
        """
        更新玩家在排行榜中的排名
        """
        # 更新战力排行榜
        self._update_rank("battle_power", player_data)
        
        # 更新等级排行榜
        self._update_rank("level", player_data)
    
    def _update_rank(self, rank_type, player_data):
        """
        更新指定类型排行榜中的玩家排名
        """
        # 根据排行榜类型获取排序字段
        sort_field = rank_type
        
        # 检查玩家是否已在排行榜中
        player_in_rank = False
        for i, rank_data in enumerate(self.ranks[rank_type]):
            if rank_data["name"] == player_data["name"]:
                # 更新玩家数据
                self.ranks[rank_type][i][sort_field] = player_data[sort_field]
                if rank_type == "battle_power":
                    self.ranks[rank_type][i]["level"] = player_data["level"]
                elif rank_type == "level":
                    self.ranks[rank_type][i]["battle_power"] = player_data["battle_power"]
                player_in_rank = True
                break
        
        # 如果玩家不在排行榜中，则添加
        if not player_in_rank:
            new_rank_data = {
                "name": player_data["name"],
                "battle_power": player_data["battle_power"],
                "level": player_data["level"]
            }
            self.ranks[rank_type].append(new_rank_data)
        
        # 根据排行榜类型排序
        self.ranks[rank_type].sort(key=lambda x: x[sort_field], reverse=True)
        
        # 更新排名
        for i, rank_data in enumerate(self.ranks[rank_type]):
            rank_data["rank"] = i + 1
    
    def get_current_ranks(self):
        """
        获取当前显示的排行榜数据
        """
        start_index = self.current_page * self.max_rank_display
        end_index = start_index + self.max_rank_display
        
        return self.ranks[self.current_rank_type][start_index:end_index]
    
    def get_player_rank(self, player_name, rank_type=None):
        """
        获取玩家在指定排行榜中的排名
        """
        if rank_type is None:
            rank_type = self.current_rank_type
        
        for rank_data in self.ranks[rank_type]:
            if rank_data["name"] == player_name:
                return rank_data["rank"]
        
        return None
    
    def set_current_rank_type(self, rank_type):
        """
        设置当前显示的排行榜类型
        """
        if rank_type in self.ranks:
            self.current_rank_type = rank_type
            self.current_page = 0
            return True
        return False
    
    def next_page(self):
        """
        显示下一页排行榜
        """
        max_page = (len(self.ranks[self.current_rank_type]) - 1) // self.max_rank_display
        
        if self.current_page < max_page:
            self.current_page += 1
            return True
        return False
    
    def prev_page(self):
        """
        显示上一页排行榜
        """
        if self.current_page > 0:
            self.current_page -= 1
            return True
        return False
    
    def get_total_pages(self):
        """
        获取当前排行榜的总页数
        """
        return (len(self.ranks[self.current_rank_type]) - 1) // self.max_rank_display + 1 