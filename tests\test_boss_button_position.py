#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Boss按钮位置测试

测试Boss状态按钮是否正确放置在小地图下方10个像素的位置
"""

import sys
import os
import pygame
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from game.ui.battle_panel import BattlePanel
from game.core.battle_manager import BattleManager


def test_boss_button_position():
    """测试Boss按钮位置"""
    print("\n=== 测试Boss按钮位置 ===")
    
    # 初始化pygame
    pygame.init()
    screen = pygame.display.set_mode((1200, 800))
    
    # 创建战斗管理器和面板
    battle_manager = BattleManager(debug_console_output=False)
    player = Mock()
    player.hp = 100
    player.max_hp = 100
    player.current_hp = 100
    player.mp = 50
    player.max_mp = 50
    player.current_mp = 50
    player.character_class = "战士"
    player.gender = "男"
    
    map_manager = Mock()
    potion_effects_manager = Mock()
    
    # 创建战斗面板（中央区域）
    battle_panel = BattlePanel(
        screen=screen,
        battle_manager=battle_manager,
        player=player,
        position=(300, 0),  # 左侧300px是地图区域
        size=(600, 600),    # 中央区域大小
        map_manager=map_manager,
        potion_effects_manager=potion_effects_manager
    )
    
    # 获取小地图信息
    minimap_rect = battle_panel.minimap_rect
    minimap_size = battle_panel.minimap_size
    
    print(f"✅ 小地图信息:")
    print(f"   - 小地图位置: {minimap_rect}")
    print(f"   - 小地图大小: {minimap_size}")
    print(f"   - 小地图底部Y坐标: {minimap_rect.bottom}")
    
    # 查找Boss按钮
    boss_button = None
    for button in battle_panel.buttons:
        if "Boss状态" in button["text"]:
            boss_button = button
            break
    
    if boss_button:
        button_rect = button["rect"]
        
        print(f"\n✅ Boss按钮信息:")
        print(f"   - 按钮位置: {button_rect}")
        print(f"   - 按钮大小: {button_rect.size}")
        
        # 计算预期位置
        expected_x = battle_panel.rect.right - minimap_size[0] - 20  # 与小地图左对齐
        expected_y = minimap_rect.bottom + 10  # 小地图下方10个像素
        
        print(f"\n✅ 位置验证:")
        print(f"   - 预期X位置: {expected_x}")
        print(f"   - 实际X位置: {button_rect.x}")
        print(f"   - X位置正确: {button_rect.x == expected_x}")
        
        print(f"   - 预期Y位置: {expected_y}")
        print(f"   - 实际Y位置: {button_rect.y}")
        print(f"   - Y位置正确: {button_rect.y == expected_y}")
        
        # 检查与小地图的关系
        distance_from_minimap = button_rect.y - minimap_rect.bottom
        print(f"   - 距离小地图底部: {distance_from_minimap}px")
        
        # 检查是否与小地图左对齐
        x_alignment = button_rect.x == minimap_rect.x
        print(f"   - 与小地图左对齐: {x_alignment}")
        
        # 检查是否在面板内
        in_panel = battle_panel.rect.contains(button_rect)
        print(f"   - 在面板内: {in_panel}")
        
        # 综合评估
        position_correct = (
            button_rect.x == expected_x and
            button_rect.y == expected_y and
            distance_from_minimap == 10 and
            x_alignment and
            in_panel
        )
        
        if position_correct:
            print("\n🎉 Boss按钮位置完全正确！")
        else:
            print("\n❌ Boss按钮位置需要调整")
            
    else:
        print("❌ 未找到Boss状态按钮")
    
    pygame.quit()


def test_visual_verification():
    """可视化验证Boss按钮位置"""
    print("\n=== 可视化验证Boss按钮位置 ===")
    
    # 初始化pygame
    pygame.init()
    screen = pygame.display.set_mode((1200, 800))
    pygame.display.set_caption("Boss按钮位置验证")
    
    # 创建战斗面板
    battle_manager = BattleManager(debug_console_output=False)
    player = Mock()
    player.hp = 100
    player.max_hp = 100
    player.current_hp = 100
    player.mp = 50
    player.max_mp = 50
    player.current_mp = 50
    player.character_class = "战士"
    player.gender = "男"
    
    battle_panel = BattlePanel(
        screen=screen,
        battle_manager=battle_manager,
        player=player,
        position=(300, 0),
        size=(600, 600),
        map_manager=Mock(),
        potion_effects_manager=Mock()
    )
    
    print("✅ 创建了可视化验证窗口")
    print("   - 可以观察Boss按钮是否在小地图下方")
    print("   - 可以检查按钮与小地图的对齐关系")
    print("   - 窗口将运行3秒进行观察")
    
    # 事件循环（运行3秒）
    clock = pygame.time.Clock()
    running = True
    start_time = pygame.time.get_ticks()
    test_duration = 3000  # 3秒
    
    while running and (pygame.time.get_ticks() - start_time) < test_duration:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            else:
                battle_panel.handle_event(event)
        
        # 更新
        battle_panel.update()
        
        # 清屏
        screen.fill((30, 30, 30))
        
        # 渲染战斗面板
        battle_panel.render()
        
        # 添加辅助线和说明
        minimap_rect = battle_panel.minimap_rect
        
        # 绘制小地图边框（高亮）
        pygame.draw.rect(screen, (0, 255, 0), minimap_rect, 2)
        
        # 绘制Boss按钮边框（高亮）
        for button in battle_panel.buttons:
            if "Boss状态" in button["text"]:
                button_rect = button["rect"]
                pygame.draw.rect(screen, (255, 255, 0), button_rect, 2)
                
                # 绘制连接线
                line_start = (minimap_rect.centerx, minimap_rect.bottom)
                line_end = (button_rect.centerx, button_rect.top)
                pygame.draw.line(screen, (255, 0, 0), line_start, line_end, 1)
                break
        
        # 添加说明文字
        font = pygame.font.SysFont("SimHei", 16)
        instructions = [
            "Boss按钮位置验证",
            "绿色框: 小地图区域",
            "黄色框: Boss按钮",
            "红线: 连接关系",
            "按钮应在小地图正下方10px"
        ]
        
        for i, text in enumerate(instructions):
            color = (255, 255, 255) if i == 0 else (200, 200, 200)
            text_surface = font.render(text, True, color)
            screen.blit(text_surface, (50, 50 + i * 25))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    print("✅ 可视化验证完成")


def test_different_panel_sizes():
    """测试不同面板尺寸下的Boss按钮位置"""
    print("\n=== 测试不同面板尺寸下的Boss按钮位置 ===")
    
    pygame.init()
    screen = pygame.display.set_mode((1200, 800))
    
    test_sizes = [
        (500, 500, "小面板"),
        (600, 600, "中等面板"),
        (800, 700, "大面板")
    ]
    
    for width, height, size_name in test_sizes:
        print(f"\n--- {size_name} ({width}x{height}) ---")
        
        # 创建战斗面板
        battle_manager = BattleManager(debug_console_output=False)
        player = Mock()
        player.character_class = "战士"
        player.gender = "男"
        
        battle_panel = BattlePanel(
            screen=screen,
            battle_manager=battle_manager,
            player=player,
            position=(100, 50),
            size=(width, height),
            map_manager=Mock(),
            potion_effects_manager=Mock()
        )
        
        # 获取小地图和Boss按钮信息
        minimap_rect = battle_panel.minimap_rect
        
        boss_button = None
        for button in battle_panel.buttons:
            if "Boss状态" in button["text"]:
                boss_button = button
                break
        
        if boss_button:
            button_rect = boss_button["rect"]
            distance = button_rect.y - minimap_rect.bottom
            x_aligned = button_rect.x == minimap_rect.x
            
            print(f"   - 小地图: {minimap_rect}")
            print(f"   - Boss按钮: {button_rect}")
            print(f"   - 距离小地图: {distance}px")
            print(f"   - X轴对齐: {x_aligned}")
            print(f"   - 位置正确: {distance == 10 and x_aligned}")
        else:
            print(f"   - ❌ 未找到Boss按钮")
    
    pygame.quit()


def run_all_tests():
    """运行所有Boss按钮位置测试"""
    print("开始测试Boss按钮位置...")
    print("=" * 60)
    
    try:
        test_boss_button_position()
        test_different_panel_sizes()
        test_visual_verification()
        
        print("\n" + "=" * 60)
        print("🎉 Boss按钮位置测试完成！")
        print("\n📝 位置说明:")
        print("- ✅ Boss按钮现在位于小地图正下方10个像素")
        print("- ✅ 按钮与小地图左对齐")
        print("- ✅ 不会被地图或其他元素遮挡")
        print("- ✅ 在不同面板尺寸下都能正确定位")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
