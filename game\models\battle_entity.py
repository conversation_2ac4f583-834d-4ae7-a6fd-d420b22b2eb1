"""
战斗实体系统
定义了参与战斗的基本实体类型
"""
from game.models.character_stats import CharacterStats
import random
import uuid

class BattleEntity:
    """
    战斗实体基类，定义了战斗中所有实体的通用属性和方法
    包括玩家角色、NPC、怪物等
    """
    def __init__(self, name="未命名"):
        self.id = str(uuid.uuid4())  # 唯一ID
        self.name = name            # 实体名称
        self.stats = CharacterStats()  # 角色属性
        
        # 当前状态
        self.current_hp = self.stats.max_hp
        self.current_mp = self.stats.max_mp
        
        # 战斗状态
        self.is_alive = True
        self.status_effects = []  # 状态效果列表
        self.buffs = []          # 增益效果列表
        self.debuffs = []        # 减益效果列表
        
        # 战斗中的临时属性
        self.combat_value = 0    # 战斗力
        self.turn_count = 0      # 回合计数
        self.last_action = None  # 上一次行动
        
    def take_damage(self, damage, damage_type="physical", critical=False):
        """
        受到伤害，返回实际伤害值
        
        Args:
            damage: 伤害值
            damage_type: 伤害类型 (physical/magic/tao/true)
            critical: 是否暴击
            
        Returns:
            实际造成的伤害值
        """
        # 如果是真实伤害，直接扣除生命值
        if damage_type == "true":
            actual_damage = damage
        else:
            # 根据伤害类型计算实际伤害
            if damage_type == "physical":
                defense = random.randint(self.stats.defense_min, self.stats.defense_max)
                actual_damage = max(1, damage - defense)
            elif damage_type == "magic":
                actual_damage = max(1, damage - self.stats.magic_defense)
            elif damage_type == "tao":
                # 道术伤害减半防御
                defense = random.randint(self.stats.defense_min, self.stats.defense_max) // 2
                actual_damage = max(1, damage - defense)
            else:
                actual_damage = damage
            
            # 百分比伤害减免
            if self.stats.percent_defense > 0:
                actual_damage = int(actual_damage * (1 - self.stats.percent_defense))
                
        # 确保至少造成1点伤害
        actual_damage = max(1, actual_damage)
        
        # 扣除生命值
        self.current_hp = max(0, self.current_hp - actual_damage)
        
        # 装备耐久度损坏（仅对玩家）
        self._handle_equipment_durability_damage()
        
        # 检查是否死亡
        if self.current_hp <= 0:
            self.is_alive = False
            
        return actual_damage
    
    def _handle_equipment_durability_damage(self):
        """处理装备耐久度损坏"""
        # 只对玩家进行装备耐久度损坏
        if hasattr(self, 'equipment_manager') and hasattr(self.equipment_manager, 'damage_equipment'):
            import random
            
            # 5% 概率损坏装备
            if random.random() < 0.05:
                # 随机选择一个装备槽位进行损坏
                equipped_slots = []
                for slot, item in self.equipment_manager.equipped_items.items():
                    if item is not None:
                        equipped_slots.append(slot)
                
                if equipped_slots:
                    # 随机选择一个有装备的槽位
                    slot_to_damage = random.choice(equipped_slots)
                    
                    # 损坏1点耐久度
                    self.equipment_manager.damage_equipment(slot_to_damage, 1)
                    
                    # 可选：显示损坏信息（如果有日志系统）
                    if hasattr(self, 'name'):
                        damaged_item = self.equipment_manager.equipped_items[slot_to_damage]
                        current_durability = damaged_item.get('current_durability', damaged_item.get('durability', 100))
                        print(f"🔧 {self.name}的{damaged_item.get('name', '装备')}耐久度-1 (剩余: {current_durability})")
    
    def heal(self, amount):
        """
        恢复生命值
        
        Args:
            amount: 恢复量
            
        Returns:
            实际恢复的生命值
        """
        # 如果已经死亡，无法恢复
        if not self.is_alive:
            return 0
            
        # 计算实际恢复量
        max_heal = self.stats.get_max_hp() - self.current_hp
        actual_heal = min(amount, max_heal)
        
        # 恢复生命值
        self.current_hp += actual_heal
        
        return actual_heal
    
    def use_mp(self, amount):
        """
        消耗魔法值
        
        Args:
            amount: 消耗量
            
        Returns:
            是否成功消耗
        """
        if self.current_mp >= amount:
            self.current_mp -= amount
            return True
        return False
    
    def restore_mp(self, amount):
        """
        恢复魔法值
        
        Args:
            amount: 恢复量
            
        Returns:
            实际恢复的魔法值
        """
        max_restore = self.stats.get_max_mp() - self.current_mp
        actual_restore = min(amount, max_restore)
        
        self.current_mp += actual_restore
        
        return actual_restore
    
    def is_hit(self, attacker):
        """
        判断是否被命中
        
        Args:
            attacker: 攻击者
            
        Returns:
            是否命中
        """
        # 攻击者的命中率 vs 防御者的闪避率
        hit_chance = 0.8 + (attacker.stats.get_accuracy() - self.stats.get_agility()) * 0.01
        
        # 确保命中率在合理范围内
        hit_chance = max(0.2, min(0.95, hit_chance))
        
        # 随机判断是否命中
        return random.random() < hit_chance
    
    def is_critical(self):
        """
        判断是否暴击
        
        Returns:
            是否暴击
        """
        return random.random() < self.stats.critical_rate
    
    def calculate_attack(self, is_critical=False):
        """
        计算攻击伤害
        
        Args:
            is_critical: 是否暴击
            
        Returns:
            攻击伤害值
        """
        from game.core.luck_system import LuckSystem
        
        # 获取攻击范围
        attack_min, attack_max = self.stats.get_attack_range()
        
        # 获取幸运值（如果有的话）
        luck_value = getattr(self.stats, 'get_total_luck', lambda: 0)()
        
        # 使用幸运值计算伤害
        damage = LuckSystem.calculate_damage_with_luck(attack_min, attack_max, luck_value)
        
        # 如果暴击，增加伤害
        if is_critical:
            damage = int(damage * self.stats.critical_damage)
            
        return damage
    
    def calculate_magic_attack(self, magic_power, is_critical=False):
        """
        计算魔法攻击伤害
        
        Args:
            magic_power: 技能魔法强度
            is_critical: 是否暴击
            
        Returns:
            魔法攻击伤害值
        """
        from game.core.luck_system import LuckSystem
        
        # 获取魔法攻击范围
        magic_min, magic_max = self.stats.get_magic_range()
        
        # 获取幸运值（如果有的话）
        luck_value = getattr(self.stats, 'get_total_luck', lambda: 0)()
        
        # 使用幸运值计算基础伤害
        base_damage = LuckSystem.calculate_damage_with_luck(magic_min, magic_max, luck_value)
        damage = base_damage * magic_power
        
        # 如果暴击，增加伤害
        if is_critical:
            damage = int(damage * self.stats.critical_damage)
            
        return damage
    
    def calculate_tao_attack(self, tao_power, is_critical=False):
        """
        计算道术攻击伤害
        
        Args:
            tao_power: 技能道术强度
            is_critical: 是否暴击
            
        Returns:
            道术攻击伤害值
        """
        from game.core.luck_system import LuckSystem
        
        # 获取道术攻击范围
        tao_min, tao_max = self.stats.get_tao_range()
        
        # 获取幸运值（如果有的话）
        luck_value = getattr(self.stats, 'get_total_luck', lambda: 0)()
        
        # 使用幸运值计算基础伤害
        base_damage = LuckSystem.calculate_damage_with_luck(tao_min, tao_max, luck_value)
        damage = base_damage * tao_power
        
        # 如果暴击，增加伤害
        if is_critical:
            damage = int(damage * self.stats.critical_damage)
            
        return damage
    
    def reset_combat(self):
        """
        重置战斗状态
        """
        self.current_hp = self.stats.get_max_hp()
        self.current_mp = self.stats.get_max_mp()
        self.is_alive = True
        self.status_effects = []
        self.buffs = []
        self.debuffs = []
        self.turn_count = 0
        self.last_action = None
    
    def add_status_effect(self, effect):
        """
        添加状态效果
        
        Args:
            effect: 状态效果对象
        """
        self.status_effects.append(effect)
    
    def remove_status_effect(self, effect_id):
        """
        移除状态效果
        
        Args:
            effect_id: 状态效果ID
        """
        self.status_effects = [e for e in self.status_effects if e.id != effect_id]
    
    def process_turn_start(self):
        """
        回合开始时的处理
        """
        self.turn_count += 1
        
        # 处理状态效果
        for effect in self.status_effects[:]:
            effect.on_turn_start(self)
            if effect.is_expired():
                self.status_effects.remove(effect)
    
    def process_turn_end(self):
        """
        回合结束时的处理
        """
        # 处理状态效果
        for effect in self.status_effects[:]:
            effect.on_turn_end(self)
            if effect.is_expired():
                self.status_effects.remove(effect)
    
    def to_dict(self):
        """
        将实体转换为字典
        
        Returns:
            实体的字典表示
        """
        return {
            "id": self.id,
            "name": self.name,
            "stats": self.stats.to_dict(),
            "current_hp": self.current_hp,
            "current_mp": self.current_mp,
            "is_alive": self.is_alive,
            "combat_value": self.combat_value
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        从字典创建实体
        
        Args:
            data: 实体字典数据
            
        Returns:
            创建的实体对象
        """
        entity = cls(data.get("name", "未命名"))
        entity.id = data.get("id", str(uuid.uuid4()))
        
        # 设置属性
        if "stats" in data:
            entity.stats = CharacterStats.from_dict(data["stats"])
        
        # 设置当前状态
        entity.current_hp = data.get("current_hp", entity.stats.max_hp)
        entity.current_mp = data.get("current_mp", entity.stats.max_mp)
        entity.is_alive = data.get("is_alive", True)
        entity.combat_value = data.get("combat_value", 0)
        
        return entity 