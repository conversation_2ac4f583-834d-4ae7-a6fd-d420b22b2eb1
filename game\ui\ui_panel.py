import pygame

class UIPanel:
    """
    UI面板基类，所有UI面板都继承自这个类
    """
    def __init__(self, screen, position, size):
        # 屏幕引用
        self.screen = screen
        
        # 面板位置和大小
        self.position = position
        self.size = size
        self.rect = pygame.Rect(position, size)
        
        # 面板可见性
        self.visible = True
        
        # 面板显示选项 - 新增，支持自定义显示
        self.show_border = True
        self.show_title = True
        
        # 面板背景色
        self.background_color = (50, 50, 50)
        
        # 面板边框
        self.border_color = (100, 100, 100)
        self.border_width = 2
        
        # 字体
        self.title_font = pygame.font.SysFont("SimHei", 16, bold=True)
        self.normal_font = pygame.font.SysFont("SimHei", 14)
        self.small_font = pygame.font.SysFont("SimHei", 12)
        
        # 文本颜色
        self.title_color = (255, 255, 255)
        self.normal_color = (200, 200, 200)
        self.highlight_color = (255, 255, 0)
        
        # 面板标题
        self.title = "面板"
        self.title_height = 20
        
        # 按钮列表
        self.buttons = []
    
    def handle_event(self, event):
        """
        处理事件
        """
        # 如果面板不可见，则不处理事件
        if not self.visible:
            return False
        
        # 鼠标点击事件
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            # 检查是否点击了按钮
            for button in self.buttons:
                if button["rect"].collidepoint(event.pos):
                    # 执行按钮回调函数
                    button["callback"]()
                    return True
        
        return False
    
    def update(self):
        """
        更新面板状态
        """
        pass
    
    def render(self):
        """
        渲染面板
        """
        if not self.visible:
            return
        
        # 绘制面板背景
        pygame.draw.rect(self.screen, self.background_color, self.rect)
        
        # 绘制面板边框（如果启用）
        if self.show_border:
            pygame.draw.rect(self.screen, self.border_color, self.rect, self.border_width)
        
        # 绘制面板标题（如果启用）
        if self.show_title and self.title:
            title_surface = self.title_font.render(self.title, True, self.title_color)
            title_rect = title_surface.get_rect(midtop=(self.rect.centerx, self.rect.top + 5))
            self.screen.blit(title_surface, title_rect)
        
        # 绘制按钮
        self._render_buttons()
    
    def _render_buttons(self):
        """
        渲染按钮
        """
        for button in self.buttons:
            # 绘制按钮背景
            pygame.draw.rect(self.screen, button["bg_color"], button["rect"])
            
            # 绘制按钮边框
            pygame.draw.rect(self.screen, button["border_color"], button["rect"], 1)
            
            # 绘制按钮文本
            text_surface = self.normal_font.render(button["text"], True, button["text_color"])
            text_rect = text_surface.get_rect(center=button["rect"].center)
            self.screen.blit(text_surface, text_rect)
    
    def add_button(self, text, rect, callback, bg_color=(70, 70, 70), text_color=(200, 200, 200), border_color=(100, 100, 100)):
        """
        添加按钮
        """
        # 如果rect是元组，转换为实际的pygame.Rect
        if isinstance(rect, (tuple, list)) and len(rect) == 4:
            # 对于全屏面板（如开始菜单），直接使用绝对坐标
            if self.rect.topleft == (0, 0) and self.rect.size == (1280, 720):
                button_rect = pygame.Rect(rect[0], rect[1], rect[2], rect[3])
            else:
                # 对于普通面板，使用相对坐标
                button_rect = pygame.Rect(
                    self.rect.left + rect[0],
                    self.rect.top + rect[1],
                    rect[2],
                    rect[3]
                )
        else:
            button_rect = rect
        
        button = {
            "text": text,
            "rect": button_rect,
            "callback": callback,
            "bg_color": bg_color,
            "text_color": text_color,
            "border_color": border_color
        }
        
        self.buttons.append(button)
    
    def remove_button(self, text):
        """
        移除按钮
        """
        for i, button in enumerate(self.buttons):
            if button["text"] == text:
                return self.buttons.pop(i)
        return None
    
    def clear_buttons(self):
        """
        清除所有按钮
        """
        self.buttons = []
    
    def set_position(self, position):
        """
        设置面板位置
        """
        self.position = position
        self.rect.topleft = position
        
        # 更新按钮位置
        for button in self.buttons:
            button["rect"].topleft = (
                self.rect.left + (button["rect"].left - self.rect.left),
                self.rect.top + (button["rect"].top - self.rect.top)
            )
    
    def set_size(self, size):
        """
        设置面板大小
        """
        self.size = size
        self.rect.size = size 