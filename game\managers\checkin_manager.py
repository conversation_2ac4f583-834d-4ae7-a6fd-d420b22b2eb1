import json
import os
from datetime import datetime, date
from typing import Dict, Any, List, Optional

class CheckinManager:
    """
    签到管理器
    负责处理每日签到逻辑、奖励发放和签到状态管理
    """
    
    def __init__(self):
        """
        初始化签到管理器
        """
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        加载签到配置文件
        
        Returns:
            Dict[str, Any]: 签到配置数据
        """
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'checkin_config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载签到配置失败: {e}")
            # 返回默认配置
            return {
                "daily_reward": {"gold": 5000, "points": 10},
                "consecutive_rewards": [],
                "monthly_rewards": {"rewards": []}
            }
    
    def can_checkin_today(self, player) -> bool:
        """
        检查玩家今天是否可以签到
        
        Args:
            player: 玩家对象
            
        Returns:
            bool: 是否可以签到
        """
        try:
            # 获取今天的日期字符串
            today = date.today().strftime('%Y-%m-%d')
            
            # 检查玩家是否有签到数据
            if not hasattr(player, 'daily_checkin'):
                return True
                
            # 检查最后签到日期
            last_checkin = player.daily_checkin.get('last_checkin_date', '')
            
            # 如果今天还没有签到，则可以签到
            return last_checkin != today
            
        except Exception as e:
            print(f"检查签到状态时出错: {e}")
            return False
    
    def perform_checkin(self, player) -> Dict[str, Any]:
        """
        执行签到操作
        
        Args:
            player: 玩家对象
            
        Returns:
            Dict[str, Any]: 签到结果信息
        """
        try:
            # 检查是否可以签到
            if not self.can_checkin_today(player):
                return {
                    'success': False,
                    'message': '今天已经签到过了！',
                    'rewards': {}
                }
            
            # 初始化签到数据（如果不存在）
            if not hasattr(player, 'daily_checkin'):
                player.daily_checkin = {
                    'last_checkin_date': '',
                    'consecutive_days': 0,
                    'total_checkin_days': 0,
                    'monthly_checkin_count': 0,
                    'checkin_rewards_claimed': []
                }
            
            today = date.today().strftime('%Y-%m-%d')
            yesterday = self._get_yesterday_date()
            
            # 更新连续签到天数
            if player.daily_checkin['last_checkin_date'] == yesterday:
                # 连续签到
                player.daily_checkin['consecutive_days'] += 1
            else:
                # 重新开始计算连续签到
                player.daily_checkin['consecutive_days'] = 1
            
            # 更新签到数据
            player.daily_checkin['last_checkin_date'] = today
            player.daily_checkin['total_checkin_days'] += 1
            
            # 更新月度签到计数（简化处理，实际应该按月重置）
            current_month = datetime.now().strftime('%Y-%m')
            if not hasattr(player.daily_checkin, 'current_month') or player.daily_checkin.get('current_month') != current_month:
                player.daily_checkin['current_month'] = current_month
                player.daily_checkin['monthly_checkin_count'] = 1
            else:
                player.daily_checkin['monthly_checkin_count'] += 1
            
            # 发放每日奖励
            rewards = self._give_daily_rewards(player)
            
            # 检查并发放连续签到奖励
            consecutive_rewards = self._check_consecutive_rewards(player)
            if consecutive_rewards:
                rewards.update(consecutive_rewards)
            
            # 检查并发放月度奖励
            monthly_rewards = self._check_monthly_rewards(player)
            if monthly_rewards:
                rewards.update(monthly_rewards)
            
            return {
                'success': True,
                'message': f'签到成功！连续签到{player.daily_checkin["consecutive_days"]}天',
                'rewards': rewards,
                'consecutive_days': player.daily_checkin['consecutive_days'],
                'total_days': player.daily_checkin['total_checkin_days']
            }
            
        except Exception as e:
            print(f"执行签到时出错: {e}")
            return {
                'success': False,
                'message': '签到失败，请稍后重试',
                'rewards': {}
            }
    
    def _get_yesterday_date(self) -> str:
        """
        获取昨天的日期字符串
        
        Returns:
            str: 昨天的日期 (YYYY-MM-DD)
        """
        from datetime import timedelta
        yesterday = date.today() - timedelta(days=1)
        return yesterday.strftime('%Y-%m-%d')
    
    def _give_daily_rewards(self, player) -> Dict[str, Any]:
        """
        发放每日签到奖励
        
        Args:
            player: 玩家对象
            
        Returns:
            Dict[str, Any]: 奖励信息
        """
        rewards = {}
        daily_reward = self.config.get('daily_reward', {})
        
        # 发放金币
        gold_reward = daily_reward.get('gold', 0)
        if gold_reward > 0:
            if hasattr(player, 'add_currency'):
                player.add_currency(gold_reward)
            elif hasattr(player, 'currencies'):
                player.currencies['gold'] = player.currencies.get('gold', 0) + gold_reward
            rewards['gold'] = gold_reward
        
        # 发放积分
        points_reward = daily_reward.get('points', 0)
        if points_reward > 0:
            if hasattr(player, 'add_points'):
                player.add_points(points_reward)
            elif hasattr(player, 'currencies'):
                player.currencies['points'] = player.currencies.get('points', 0) + points_reward
            rewards['points'] = points_reward
        
        return rewards
    
    def _check_consecutive_rewards(self, player) -> Optional[Dict[str, Any]]:
        """
        检查并发放连续签到奖励
        
        Args:
            player: 玩家对象
            
        Returns:
            Optional[Dict[str, Any]]: 连续签到奖励信息
        """
        consecutive_days = player.daily_checkin['consecutive_days']
        consecutive_rewards = self.config.get('consecutive_rewards', [])
        
        for reward_config in consecutive_rewards:
            required_days = reward_config.get('days', 0)
            if consecutive_days == required_days:
                # 发放连续签到奖励
                reward = reward_config.get('reward', {})
                return self._give_rewards(player, reward, f'consecutive_{required_days}')
        
        return None
    
    def _check_monthly_rewards(self, player) -> Optional[Dict[str, Any]]:
        """
        检查并发放月度签到奖励
        
        Args:
            player: 玩家对象
            
        Returns:
            Optional[Dict[str, Any]]: 月度奖励信息
        """
        monthly_count = player.daily_checkin.get('monthly_checkin_count', 0)
        monthly_rewards = self.config.get('monthly_rewards', {}).get('rewards', [])
        
        for reward_config in monthly_rewards:
            required_days = reward_config.get('days', 0)
            if monthly_count == required_days:
                # 检查是否已经领取过这个月度奖励
                reward_key = f'monthly_{required_days}_{datetime.now().strftime("%Y-%m")}'
                if reward_key not in player.daily_checkin.get('checkin_rewards_claimed', []):
                    reward = reward_config.get('reward', {})
                    result = self._give_rewards(player, reward, reward_key)
                    # 记录已领取的奖励
                    if 'checkin_rewards_claimed' not in player.daily_checkin:
                        player.daily_checkin['checkin_rewards_claimed'] = []
                    player.daily_checkin['checkin_rewards_claimed'].append(reward_key)
                    return result
        
        return None
    
    def _give_rewards(self, player, reward_config: Dict[str, Any], reward_type: str) -> Dict[str, Any]:
        """
        发放奖励
        
        Args:
            player: 玩家对象
            reward_config: 奖励配置
            reward_type: 奖励类型标识
            
        Returns:
            Dict[str, Any]: 发放的奖励信息
        """
        rewards = {}
        
        # 发放金币
        if 'gold' in reward_config:
            gold_amount = reward_config['gold']
            if hasattr(player, 'add_currency'):
                player.add_currency(gold_amount)
            elif hasattr(player, 'currencies'):
                player.currencies['gold'] = player.currencies.get('gold', 0) + gold_amount
            rewards['gold'] = rewards.get('gold', 0) + gold_amount
        
        # 发放积分
        if 'points' in reward_config:
            points_amount = reward_config['points']
            if hasattr(player, 'add_points'):
                player.add_points(points_amount)
            elif hasattr(player, 'currencies'):
                player.currencies['points'] = player.currencies.get('points', 0) + points_amount
            rewards['points'] = rewards.get('points', 0) + points_amount
        
        # 发放元宝
        if 'yuanbao' in reward_config:
            yuanbao_amount = reward_config['yuanbao']
            if hasattr(player, 'currencies'):
                player.currencies['yuanbao'] = player.currencies.get('yuanbao', 0) + yuanbao_amount
            rewards['yuanbao'] = rewards.get('yuanbao', 0) + yuanbao_amount
        
        # 发放物品
        if 'items' in reward_config:
            items = reward_config['items']
            rewards['items'] = []
            for item in items:
                item_name = item.get('name')
                item_count = item.get('count', 1)
                # 这里需要根据实际的物品系统来添加物品
                # 暂时只记录物品信息
                rewards['items'].append({
                    'name': item_name,
                    'count': item_count
                })
        
        # 添加奖励描述
        if 'description' in reward_config:
            rewards['description'] = reward_config['description']
        
        return rewards
    
    def get_checkin_calendar(self, player) -> Dict[str, Any]:
        """
        获取签到日历数据
        
        Args:
            player: 玩家对象
            
        Returns:
            Dict[str, Any]: 签到日历信息
        """
        try:
            # 获取当前月份的签到状态
            current_date = datetime.now()
            year = current_date.year
            month = current_date.month
            
            # 获取本月的天数
            import calendar
            days_in_month = calendar.monthrange(year, month)[1]
            
            # 构建日历数据
            calendar_data = []
            for day in range(1, days_in_month + 1):
                date_str = f"{year:04d}-{month:02d}-{day:02d}"
                is_checked = False
                
                # 检查这一天是否已签到（这里简化处理，实际需要更复杂的逻辑）
                if hasattr(player, 'daily_checkin'):
                    last_checkin = player.daily_checkin.get('last_checkin_date', '')
                    if date_str <= last_checkin:
                        # 简化判断：假设连续签到
                        consecutive_days = player.daily_checkin.get('consecutive_days', 0)
                        today = date.today().strftime('%Y-%m-%d')
                        if date_str <= today:
                            is_checked = True
                
                calendar_data.append({
                    'day': day,
                    'date': date_str,
                    'is_checked': is_checked,
                    'is_today': date_str == date.today().strftime('%Y-%m-%d')
                })
            
            return {
                'year': year,
                'month': month,
                'days': calendar_data,
                'consecutive_days': player.daily_checkin.get('consecutive_days', 0) if hasattr(player, 'daily_checkin') else 0,
                'total_days': player.daily_checkin.get('total_checkin_days', 0) if hasattr(player, 'daily_checkin') else 0,
                'monthly_count': player.daily_checkin.get('monthly_checkin_count', 0) if hasattr(player, 'daily_checkin') else 0
            }
            
        except Exception as e:
            print(f"获取签到日历时出错: {e}")
            return {
                'year': datetime.now().year,
                'month': datetime.now().month,
                'days': [],
                'consecutive_days': 0,
                'total_days': 0,
                'monthly_count': 0
            }
    
    def get_consecutive_rewards(self) -> List[Dict[str, Any]]:
        """
        获取连续签到奖励配置
        
        Returns:
            List[Dict[str, Any]]: 连续签到奖励列表
        """
        return self.config.get('consecutive_rewards', [])
    
    def get_monthly_rewards(self) -> List[Dict[str, Any]]:
        """
        获取月度签到奖励配置
        
        Returns:
            List[Dict[str, Any]]: 月度签到奖励列表
        """
        return self.config.get('monthly_rewards', {}).get('rewards', [])