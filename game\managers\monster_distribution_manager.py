import json
import os
import random
from typing import List, Tuple, Set, Dict, Any
from game.core.resource_manager import get_game_data_path


class MonsterDistributionManager:
    """
    怪物分布管理器
    负责加载配置并提供统一的怪物分散分布算法
    """
    
    def __init__(self):
        self.config = self._load_config()
        self.occupied_positions = set()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载怪物分布配置"""
        try:
            config_path = get_game_data_path("monster_distribution_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get("monster_distribution", {})
            else:
                print(f"配置文件不存在: {config_path}，使用默认配置")
                return self._get_default_config()
        except Exception as e:
            print(f"加载怪物分布配置失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "min_distance_settings": {
                "normal_monsters": {"value": 3},
                "elite_monsters": {"value": 4},
                "boss_monsters": {"value": 8},
                "player_spawn_safe_zone": {"value": 5}
            },
            "distribution_algorithm": {
                "grid_size": {"value": 8},
                "max_attempts": {"value": 100},
                "grid_attempts_ratio": {"value": 0.7}
            },
            "density_control": {
                "weight_divisor": {"value": 15},
                "max_monsters_per_type": {"value": 8}
            },
            "debug_settings": {
                "show_distribution_info": {"value": True}
            }
        }
    
    def get_min_distance(self, monster_type: str) -> int:
        """获取指定怪物类型的最小距离"""
        type_mapping = {
            "普通": "normal_monsters",
            "精英": "elite_monsters",
            "首领": "boss_monsters",
            "世界BOSS": "boss_monsters"
        }
        
        config_key = type_mapping.get(monster_type, "normal_monsters")
        return self.config.get("min_distance_settings", {}).get(config_key, {}).get("value", 3)
    
    def get_safe_zone_radius(self) -> int:
        """获取玩家出生点安全区域半径"""
        return self.config.get("min_distance_settings", {}).get("player_spawn_safe_zone", {}).get("value", 5)
    
    def get_grid_size(self) -> int:
        """获取网格大小"""
        return self.config.get("distribution_algorithm", {}).get("grid_size", {}).get("value", 8)
    
    def get_max_attempts(self) -> int:
        """获取最大尝试次数"""
        return self.config.get("distribution_algorithm", {}).get("max_attempts", {}).get("value", 100)
    
    def get_grid_ratio(self) -> float:
        """获取网格化分布尝试比例"""
        return self.config.get("distribution_algorithm", {}).get("grid_attempts_ratio", {}).get("value", 0.7)
    
    def reset_positions(self):
        """重置占用位置记录"""
        self.occupied_positions.clear()
    
    def is_position_valid(self, pos_x: int, pos_y: int, min_dist: int, 
                         spawn_point: Tuple[int, int] = None) -> bool:
        """
        检查位置是否有效
        
        参数:
            pos_x: X坐标
            pos_y: Y坐标
            min_dist: 最小距离
            spawn_point: 玩家出生点
            
        返回:
            位置是否有效
        """
        # 检查与玩家出生点的距离
        if spawn_point:
            spawn_x, spawn_y = spawn_point
            safe_zone = self.get_safe_zone_radius()
            if abs(pos_x - spawn_x) < safe_zone and abs(pos_y - spawn_y) < safe_zone:
                return False
        
        # 检查与其他怪物的距离
        for occupied_x, occupied_y in self.occupied_positions:
            distance = ((pos_x - occupied_x) ** 2 + (pos_y - occupied_y) ** 2) ** 0.5
            if distance < min_dist:
                return False
        
        return True
    
    def find_scattered_position(self, map_width: int, map_height: int, 
                              monster_type: str = "普通", 
                              spawn_point: Tuple[int, int] = None,
                              is_boss: bool = False) -> Tuple[int, int]:
        """
        寻找分散的位置
        
        参数:
            map_width: 地图宽度
            map_height: 地图高度
            monster_type: 怪物类型
            spawn_point: 玩家出生点
            is_boss: 是否为BOSS
            
        返回:
            位置坐标 (x, y)
        """
        min_dist = self.get_min_distance(monster_type)
        max_attempts = self.get_max_attempts()
        grid_size = self.get_grid_size()
        grid_ratio = self.get_grid_ratio()
        
        grid_attempts = int(max_attempts * grid_ratio)
        
        for attempt in range(max_attempts):
            if is_boss:
                # BOSS使用特殊区域生成
                pos_x, pos_y = self._generate_boss_position(map_width, map_height)
            elif attempt < grid_attempts:
                # 网格化分布
                pos_x, pos_y = self._generate_grid_position(map_width, map_height, grid_size)
            else:
                # 随机分布
                pos_x, pos_y = self._generate_random_position(map_width, map_height)
            
            # 确保位置在地图范围内
            pos_x = max(1, min(pos_x, map_width - 1))
            pos_y = max(1, min(pos_y, map_height - 1))
            
            # 检查位置是否有效
            if self.is_position_valid(pos_x, pos_y, min_dist, spawn_point):
                self.occupied_positions.add((pos_x, pos_y))
                return pos_x, pos_y
        
        # 降级处理：返回随机位置
        pos_x = random.randint(3, map_width - 3)
        pos_y = random.randint(3, map_height - 3)
        self.occupied_positions.add((pos_x, pos_y))
        
        if self.config.get("debug_settings", {}).get("show_distribution_info", {}).get("value", True):
            print(f"警告: 无法为{monster_type}怪物找到合适的分散位置，使用随机位置 ({pos_x}, {pos_y})")
        
        return pos_x, pos_y
    
    def _generate_boss_position(self, map_width: int, map_height: int) -> Tuple[int, int]:
        """为BOSS生成特殊位置（角落区域）"""
        # 定义四个角落区域
        corner_regions = [
            (int(map_width * 0.1), int(map_width * 0.33), int(map_height * 0.1), int(map_height * 0.33)),  # 左上
            (int(map_width * 0.67), int(map_width * 0.9), int(map_height * 0.1), int(map_height * 0.33)), # 右上
            (int(map_width * 0.1), int(map_width * 0.33), int(map_height * 0.67), int(map_height * 0.9)), # 左下
            (int(map_width * 0.67), int(map_width * 0.9), int(map_height * 0.67), int(map_height * 0.9))  # 右下
        ]
        
        x_min, x_max, y_min, y_max = random.choice(corner_regions)
        pos_x = random.randint(max(1, x_min), min(x_max, map_width - 1))
        pos_y = random.randint(max(1, y_min), min(y_max, map_height - 1))
        
        return pos_x, pos_y
    
    def _generate_grid_position(self, map_width: int, map_height: int, grid_size: int) -> Tuple[int, int]:
        """使用网格化分布生成位置"""
        grid_x = random.randint(0, (map_width - 1) // grid_size)
        grid_y = random.randint(0, (map_height - 1) // grid_size)
        
        # 在网格内随机偏移
        offset_x = random.randint(1, min(grid_size - 1, 3))
        offset_y = random.randint(1, min(grid_size - 1, 3))
        
        pos_x = grid_x * grid_size + offset_x
        pos_y = grid_y * grid_size + offset_y
        
        return pos_x, pos_y
    
    def _generate_random_position(self, map_width: int, map_height: int) -> Tuple[int, int]:
        """生成随机位置"""
        pos_x = random.randint(3, map_width - 3)
        pos_y = random.randint(3, map_height - 3)
        return pos_x, pos_y
    
    def calculate_monster_count(self, weight: int, is_boss: bool = False) -> int:
        """
        计算怪物生成数量
        
        参数:
            weight: 怪物权重
            is_boss: 是否为BOSS
            
        返回:
            生成数量
        """
        if is_boss:
            return 1
        
        weight_divisor = self.config.get("density_control", {}).get("weight_divisor", {}).get("value", 15)
        max_monsters = self.config.get("density_control", {}).get("max_monsters_per_type", {}).get("value", 8)
        
        base_count = max(1, int(weight / weight_divisor))
        return min(base_count, max_monsters)
    
    def get_distribution_summary(self) -> str:
        """获取分布情况摘要"""
        total_positions = len(self.occupied_positions)
        if total_positions == 0:
            return "暂无怪物分布数据"
        
        return f"共 {total_positions} 个怪物分布在不同位置，分散度良好"
    
    def print_distribution_info(self, map_name: str):
        """打印分布信息（如果启用调试）"""
        # 只有在明确启用调试信息时才输出
        debug_enabled = self.config.get("debug_settings", {}).get("show_distribution_info", {}).get("value", False)
        if debug_enabled:
            print(f"地图 {map_name} 怪物分布完成: {self.get_distribution_summary()}")


# 全局实例
monster_distribution_manager = MonsterDistributionManager() 