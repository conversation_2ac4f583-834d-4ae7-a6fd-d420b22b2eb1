"""
仓库面板
用于显示和管理仓库中的物品
"""
import pygame
from game.ui.item_slot import ItemSlot

class WarehousePanel:
    """仓库面板类，用于显示和管理仓库物品"""
    
    def __init__(self, screen, inventory_manager, position=(0, 0), size=(600, 500)):
        """
        初始化仓库面板
        
        Args:
            screen: 屏幕对象
            inventory_manager: 背包管理器
            position: 面板位置 (x, y)
            size: 面板大小 (width, height)
        """
        self.screen = screen
        self.inventory_manager = inventory_manager
        self.position = position
        self.size = size
        self.rect = pygame.Rect(position, size)
        
        # 面板可见性
        self.visible = False
        
        # 当前选中的物品格子索引
        self.selected_slot_idx = None
        
        # 标题区域
        title_height = 30
        self.title_area = pygame.Rect(
            self.rect.left, 
            self.rect.top, 
            self.rect.width, 
            title_height
        )
        
        # 关闭按钮
        close_button_size = 20
        self.close_button_rect = pygame.Rect(
            self.rect.right - close_button_size - 5, 
            self.rect.top + 5, 
            close_button_size, 
            close_button_size
        )
        
        # 页码显示区域
        self.page_display_rect = pygame.Rect(
            self.title_area.right - 80 - close_button_size - 10, 
            self.title_area.top + 5, 
            50, 
            title_height - 10
        )
        
        # 计算网格格子大小和间距
        self.grid_cols = 8
        self.grid_rows = 5
        self.grid_spacing = 2  # 减小间距让更多空间给格子
        
        # 物品网格区域
        grid_margin_top = 10
        grid_margin_bottom = 50  # 为底部按钮留出空间
        available_width = self.rect.width - 20  # 左右各留10px边距
        available_height = self.rect.height - title_height - grid_margin_top - grid_margin_bottom
        
        # 根据可用空间计算最大可能的格子大小
        max_width_per_slot = (available_width - (self.grid_cols - 1) * self.grid_spacing) // self.grid_cols
        max_height_per_slot = (available_height - (self.grid_rows - 1) * self.grid_spacing) // self.grid_rows
        
        # 选择较小的值确保格子是正方形，并且不会超出边界
        slot_size = min(max_width_per_slot, max_height_per_slot)
        # 确保有最小大小
        slot_size = max(slot_size, 40)  # 最小40像素
        
        self.slot_width = slot_size
        self.slot_height = slot_size
        
        # 重新计算网格区域以适应格子大小
        required_width = self.grid_cols * self.slot_width + (self.grid_cols - 1) * self.grid_spacing
        required_height = self.grid_rows * self.slot_height + (self.grid_rows - 1) * self.grid_spacing
        
        # 调整网格区域位置，使其居中
        grid_start_x = self.rect.left + (self.rect.width - required_width) // 2
        grid_start_y = self.title_area.bottom + grid_margin_top
        
        self.grid_area = pygame.Rect(
            grid_start_x,
            grid_start_y,
            required_width,
            required_height
        )
        
        # 物品格子实例
        self.item_slots = {}
        
        # 底部按钮区域
        button_height = 30
        self.button_area = pygame.Rect(
            self.rect.left + 10,
            self.grid_area.bottom + 10,
            self.rect.width - 20,
            button_height
        )
        
        # 底部按钮
        button_width = 60
        button_spacing = 5
        button_y = self.button_area.top
        
        self.take_button_rect = pygame.Rect(
            self.button_area.left,
            button_y,
            button_width,
            button_height
        )
        
        self.refresh_button_rect = pygame.Rect(
            self.take_button_rect.right + button_spacing,
            button_y,
            button_width,
            button_height
        )
        
        # 翻页按钮位于右侧
        page_button_width = 25
        
        self.prev_page_button_rect = pygame.Rect(
            self.button_area.right - page_button_width * 2 - button_spacing,
            button_y,
            page_button_width,
            button_height
        )
        
        self.next_page_button_rect = pygame.Rect(
            self.button_area.right - page_button_width,
            button_y,
            page_button_width,
            button_height
        )
        
        # 当前页码
        self.current_page = self.inventory_manager.current_warehouse_page
        
        # 加载字体
        self.title_font = pygame.font.SysFont("SimHei", 16)
        self.button_font = pygame.font.SysFont("SimHei", 14)
        self.page_font = pygame.font.SysFont("SimHei", 14)
        
        # 颜色
        self.background_color = (50, 50, 50)
        self.title_color = (220, 220, 220)
        self.button_color = (70, 70, 70)
        self.button_hover_color = (90, 90, 90)
        self.button_text_color = (220, 220, 220)
        self.close_button_color = (180, 70, 70)
        
        # 背包面板引用
        self.inventory_panel = None
    
    def set_inventory_panel(self, inventory_panel):
        """设置背包面板引用"""
        self.inventory_panel = inventory_panel
    
    def toggle_visibility(self):
        """切换可见性"""
        self.visible = not self.visible
        if self.visible:
            # 初始化物品格子
            self._init_item_slots()
            # 加载当前页的物品
            self._load_current_page_items()
    
    def _init_item_slots(self):
        """初始化物品格子"""
        self.item_slots = {}
        
        for row in range(self.grid_rows):
            for col in range(self.grid_cols):
                # 计算格子位置
                x = self.grid_area.left + col * (self.slot_width + self.grid_spacing)
                y = self.grid_area.top + row * (self.slot_height + self.grid_spacing)
                
                # 计算格子索引
                slot_idx = row * self.grid_cols + col
                
                # 创建物品格子
                self.item_slots[slot_idx] = ItemSlot(
                    self.screen, 
                    (x, y), 
                    (self.slot_width, self.slot_height)
                )
    
    def _load_current_page_items(self):
        """加载当前页的物品"""
        # 获取当前页的物品
        page_items = self.inventory_manager.get_items_by_page(self.current_page, "warehouse")
        
        # 清空所有格子
        for slot in self.item_slots.values():
            slot.clear_item()
        
        # 填充物品
        for slot_idx, item_data in page_items.items():
            if slot_idx >= len(self.item_slots):
                continue
                
            # 仓库物品都是元组格式(物品, 数量)
            item, quantity = item_data
            self.item_slots[slot_idx].set_item(item, quantity)
    
    def render(self):
        """渲染仓库面板"""
        if not self.visible:
            return
            
        # 绘制面板背景
        pygame.draw.rect(self.screen, self.background_color, self.rect)
        pygame.draw.rect(self.screen, (100, 100, 100), self.rect, 2)
        
        # 绘制标题区域
        pygame.draw.rect(self.screen, (70, 70, 70), self.title_area)
        
        # 绘制标题
        title_text = self.title_font.render("仓库", True, self.title_color)
        title_text_rect = title_text.get_rect(
            midleft=(self.title_area.left + 10, self.title_area.centery)
        )
        self.screen.blit(title_text, title_text_rect)
        
        # 绘制页码显示
        page_text = f"{self.current_page + 1}"
        page_surface = self.page_font.render(page_text, True, self.title_color)
        page_rect = page_surface.get_rect(center=self.page_display_rect.center)
        self.screen.blit(page_surface, page_rect)
        
        # 绘制关闭按钮
        pygame.draw.rect(self.screen, self.close_button_color, self.close_button_rect)
        close_text = self.title_font.render("X", True, (255, 255, 255))
        close_text_rect = close_text.get_rect(center=self.close_button_rect.center)
        self.screen.blit(close_text, close_text_rect)
        
        # 绘制物品网格区域
        pygame.draw.rect(self.screen, (60, 60, 60), self.grid_area)
        
        # 绘制物品格子
        for slot in self.item_slots.values():
            slot.render()
        
        # 绘制底部按钮区域
        pygame.draw.rect(self.screen, (60, 60, 60), self.button_area)
        
        # 绘制底部按钮
        # 取出按钮
        pygame.draw.rect(self.screen, self.button_color, self.take_button_rect)
        take_text = self.button_font.render("取出", True, self.button_text_color)
        take_text_rect = take_text.get_rect(center=self.take_button_rect.center)
        self.screen.blit(take_text, take_text_rect)
        
        # 刷新按钮
        pygame.draw.rect(self.screen, self.button_color, self.refresh_button_rect)
        refresh_text = self.button_font.render("刷新", True, self.button_text_color)
        refresh_text_rect = refresh_text.get_rect(center=self.refresh_button_rect.center)
        self.screen.blit(refresh_text, refresh_text_rect)
        
        # 翻页按钮
        pygame.draw.rect(self.screen, self.button_color, self.prev_page_button_rect)
        prev_text = self.button_font.render("<<", True, self.button_text_color)
        prev_text_rect = prev_text.get_rect(center=self.prev_page_button_rect.center)
        self.screen.blit(prev_text, prev_text_rect)
        
        pygame.draw.rect(self.screen, self.button_color, self.next_page_button_rect)
        next_text = self.button_font.render(">>", True, self.button_text_color)
        next_text_rect = next_text.get_rect(center=self.next_page_button_rect.center)
        self.screen.blit(next_text, next_text_rect)
    
    def handle_event(self, event):
        """处理事件 - 修复版，完全消费鼠标事件防止穿透"""
        if not self.visible:
            return False
        
        # 处理鼠标点击事件
        if event.type == pygame.MOUSEBUTTONDOWN:
            # 先检查是否点击在面板区域内
            if self.rect.collidepoint(event.pos):
                # 在面板内部的点击
                if event.button == 1:  # 左键
                    # 关闭按钮
                    if self.close_button_rect.collidepoint(event.pos):
                        self.visible = False
                        return True
                        
                    # 翻页按钮
                    elif self.prev_page_button_rect.collidepoint(event.pos):
                        # 上一页
                        if self.current_page > 0:
                            self.current_page -= 1
                            self.inventory_manager.current_warehouse_page = self.current_page
                            self._load_current_page_items()
                        return True
                        
                    elif self.next_page_button_rect.collidepoint(event.pos):
                        # 下一页
                        total_pages = self.inventory_manager.get_total_pages("warehouse")
                        if self.current_page < total_pages - 1:
                            self.current_page += 1
                            self.inventory_manager.current_warehouse_page = self.current_page
                            self._load_current_page_items()
                        return True
                        
                    # 底部按钮
                    elif self.take_button_rect.collidepoint(event.pos):
                        self._on_take_button_click()
                        return True
                        
                    elif self.refresh_button_rect.collidepoint(event.pos):
                        self._on_refresh_button_click()
                        return True
                        
                    # 物品格子
                    for slot_idx, slot in self.item_slots.items():
                        if slot.rect.collidepoint(event.pos):
                            # 先清除所有其他槽的选中状态
                            for other_idx, other_slot in self.item_slots.items():
                                if other_idx != slot_idx:
                                    other_slot.selected = False

                            # 切换当前槽的选中状态
                            slot.toggle_selected()

                            # 记录选中的格子索引
                            if slot.selected:
                                self.selected_slot_idx = slot_idx
                            else:
                                self.selected_slot_idx = None
                            return True
                
                # 在面板内的任何点击都应该被消费，防止穿透
                return True
            else:
                # 点击在面板外部，不处理但也不关闭面板（由UIManager处理）
                return False
                    
        return False
    
    def update(self):
        """更新仓库面板状态"""
        if not self.visible:
            return
    
    def _on_take_button_click(self):
        """处理取出按钮点击"""
        if self.selected_slot_idx is not None and self.inventory_panel:
            slot = self.item_slots.get(self.selected_slot_idx)
            if slot and slot.item:
                print(f"取出物品: {slot.item.name}")
                
                # 移动物品到背包
                global_slot_idx = self.current_page * self.inventory_manager.slots_per_page + self.selected_slot_idx
                
                # 确定目标容器类型
                target_container = None
                if slot.item.type == "equipment":
                    target_container = "equipment"
                elif slot.item.type == "skill_book":
                    target_container = "skill_book"
                else:
                    target_container = "consumable"
                    
                success = self.inventory_manager.move_item(
                    global_slot_idx, 
                    target_container, 
                    "warehouse"
                )
                
                if success:
                    print(f"物品 {slot.item.name} 已成功取出到背包")
                    # 重新加载当前页
                    self._load_current_page_items()
                    # 如果背包面板可见，也重新加载背包物品
                    if self.inventory_panel.visible:
                        self.inventory_panel._load_current_page_items()
                else:
                    print(f"无法取出到背包，背包可能已满")
    
    def _on_refresh_button_click(self):
        """处理刷新按钮点击"""
        print("刷新仓库")
        self._load_current_page_items() 