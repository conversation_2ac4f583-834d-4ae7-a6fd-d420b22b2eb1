#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立技能测试系统
不依赖游戏中的角色状态，可以独立测试各种技能的释放效果
"""

import sys
import os
import json
import random
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SummonedCreature:
    """召唤物类"""

    def __init__(self, creature_type: str, level: int, duration: int = -1):
        self.creature_type = creature_type
        self.level = level
        self.duration = duration  # -1表示永久
        self.remaining_time = duration

        # 根据类型设置属性
        if creature_type == "skeleton":
            self.name = "骷髅"
            self.hp = 50 + level * 10
            self.attack = 5 + level * 2
            self.defense = 2 + level
        elif creature_type == "beast":
            self.name = "神兽"
            self.hp = 80 + level * 15
            self.attack = 8 + level * 3
            self.defense = 5 + level * 2
        else:
            self.name = "未知召唤物"
            self.hp = 30
            self.attack = 3
            self.defense = 1

        self.max_hp = self.hp

    def __str__(self):
        if self.duration == -1:
            return f"{self.name} Lv.{self.level} (HP:{self.hp}/{self.max_hp}, 攻击:{self.attack}, 防御:{self.defense}) [永久]"
        else:
            return f"{self.name} Lv.{self.level} (HP:{self.hp}/{self.max_hp}, 攻击:{self.attack}, 防御:{self.defense}) [剩余:{self.remaining_time}秒]"

class MockPlayer:
    """模拟玩家类，用于技能测试"""
    
    def __init__(self, name: str, character_class: str, level: int = 10):
        self.name = name
        self.character_class = character_class
        self.level = level
        
        # 基础属性
        self.生命值 = 100
        self.魔法值 = 50
        self.攻击下限 = 5
        self.攻击上限 = 10
        self.魔法攻击下限 = 3
        self.魔法攻击上限 = 8
        self.道术攻击下限 = 2
        self.道术攻击上限 = 6
        self.防御力 = 2
        self.魔抗 = 1
        self.准确 = 5
        self.敏捷 = 3
        self.幸运 = 2
        self.暴击率 = 0.05
        
        # 技能相关
        self.learned_skills = []
        self.skill_cooldowns = {}

        # 🔧 新增：召唤物系统
        self.summoned_creatures = []
        # 🔧 全局设置：所有角色只能召唤1只
        self.max_summons = 1
        
    def add_skill(self, skill_data: Dict[str, Any]):
        """添加技能"""
        self.learned_skills.append(skill_data)
        
    def get_skill(self, skill_name: str) -> Dict[str, Any]:
        """获取技能"""
        for skill in self.learned_skills:
            if skill.get('name') == skill_name:
                return skill
        return None
        
    def has_skill(self, skill_name: str) -> bool:
        """检查是否拥有技能"""
        return self.get_skill(skill_name) is not None

class SkillTester:
    """独立技能测试器"""
    
    def __init__(self):
        self.skills_data = self.load_skills_data()
        
    def load_skills_data(self) -> Dict[str, Any]:
        """加载技能数据"""
        try:
            with open('game/data/skills.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载技能数据失败: {e}")
            return {}
    
    def create_test_player(self, character_class: str = "战士", level: int = 10) -> MockPlayer:
        """创建测试玩家"""
        player = MockPlayer(f"测试{character_class}", character_class, level)
        
        # 根据职业调整属性
        if character_class == "战士":
            player.攻击下限 = 8
            player.攻击上限 = 15
            player.生命值 = 150
        elif character_class == "法师":
            player.魔法攻击下限 = 10
            player.魔法攻击上限 = 18
            player.魔法值 = 100
        elif character_class == "道士":
            player.道术攻击下限 = 6
            player.道术攻击上限 = 12
            player.魔法值 = 80
            
        return player
    
    def add_skill_to_player(self, player: MockPlayer, skill_category: str, skill_id: str, level: int = 1):
        """给玩家添加技能"""
        if skill_category not in self.skills_data:
            print(f"❌ 技能类别不存在: {skill_category}")
            return False
            
        if skill_id not in self.skills_data[skill_category]:
            print(f"❌ 技能不存在: {skill_id}")
            return False
            
        skill_data = self.skills_data[skill_category][skill_id].copy()
        skill_data['level'] = min(level, skill_data.get('max_level', 3))
        skill_data['learned'] = True
        
        player.add_skill(skill_data)
        print(f"✅ 为 {player.name} 添加技能: {skill_data['name']} Lv.{skill_data['level']}")
        return True
    
    def calculate_passive_skill_effects(self, player: MockPlayer) -> Dict[str, Any]:
        """计算被动技能效果"""
        bonuses = {}
        
        for skill in player.learned_skills:
            if skill.get('type') != '被动':
                continue
                
            skill_level = skill.get('level', 0)
            if skill_level <= 0:
                continue
                
            effects = skill.get('effects', [])
            if not effects or len(effects) < skill_level:
                continue
                
            # 获取当前等级的效果
            current_effect = effects[skill_level - 1]
            effect_type = current_effect.get('type')
            effect_value = current_effect.get('value', 0)
            
            # 应用效果
            if effect_type == 'accuracy':
                bonuses['准确'] = bonuses.get('准确', 0) + effect_value
            elif effect_type == 'damage_percent':
                bonuses['伤害加成'] = bonuses.get('伤害加成', 0) + effect_value / 100.0
            elif effect_type == 'extra_damage':
                bonuses['额外伤害'] = bonuses.get('额外伤害', 0) + effect_value
            elif effect_type == 'defense_boost':
                bonuses['防御力'] = bonuses.get('防御力', 0) + effect_value
            elif effect_type == 'hp_boost':
                bonuses['生命值'] = bonuses.get('生命值', 0) + effect_value
            elif effect_type == 'mp_boost':
                bonuses['魔法值'] = bonuses.get('魔法值', 0) + effect_value
            elif effect_type == 'critical_rate':
                bonuses['暴击率'] = bonuses.get('暴击率', 0) + effect_value / 100.0
                
            print(f"🎯 被动技能 {skill['name']} Lv.{skill_level}: {effect_type} +{effect_value}")
        
        return bonuses
    
    def apply_passive_effects(self, player: MockPlayer, bonuses: Dict[str, Any]):
        """应用被动技能效果到玩家属性"""
        for attr_name, bonus_value in bonuses.items():
            if hasattr(player, attr_name):
                current_value = getattr(player, attr_name)
                setattr(player, attr_name, current_value + bonus_value)
                print(f"🔧 应用被动效果: {attr_name} {current_value} → {current_value + bonus_value}")
    
    def calculate_skill_damage(self, player: MockPlayer, skill: Dict[str, Any], target=None) -> Dict[str, Any]:
        """计算技能伤害"""
        if skill.get('type') != '攻击':
            return {'success': False, 'message': '不是攻击技能'}
        
        # 检查MP消耗
        mp_cost = skill.get('mana_cost', 0)
        if player.魔法值 < mp_cost:
            return {'success': False, 'message': 'MP不足'}
        
        # 基础伤害
        base_damage = skill.get('base_damage', 0)
        damage_multiplier = skill.get('damage_multiplier', 1.0)
        skill_level = skill.get('level', 1)
        
        # 根据技能攻击类型计算伤害
        attack_type = skill.get('attack_type', 'physical')
        if attack_type == 'physical':
            player_attack = (player.攻击下限 + player.攻击上限) / 2
        elif attack_type == 'magic':
            player_attack = (player.魔法攻击下限 + player.魔法攻击上限) / 2
        elif attack_type == 'tao':
            player_attack = (player.道术攻击下限 + player.道术攻击上限) / 2
        else:
            player_attack = 0
        
        # 计算总伤害
        total_damage = int((base_damage + player_attack * damage_multiplier) * (1 + skill_level * 0.1))
        
        # 应用被动技能加成
        bonuses = self.calculate_passive_skill_effects(player)
        damage_bonus = bonuses.get('伤害加成', 0)
        extra_damage = bonuses.get('额外伤害', 0)
        
        final_damage = int(total_damage * (1 + damage_bonus) + extra_damage)
        
        # 消耗MP
        player.魔法值 -= mp_cost
        
        return {
            'success': True,
            'damage': final_damage,
            'base_damage': total_damage,
            'bonus_damage': final_damage - total_damage,
            'mp_cost': mp_cost,
            'message': f'{skill["name"]} 造成 {final_damage} 点伤害'
        }
    
    def calculate_heal_amount(self, player: MockPlayer, skill: Dict[str, Any]) -> Dict[str, Any]:
        """计算治疗量"""
        # 检查MP消耗
        mp_cost = skill.get('mana_cost', 0)
        if player.魔法值 < mp_cost:
            return {'success': False, 'message': 'MP不足'}

        skill_level = skill.get('level', 1)
        effects = skill.get('effects', [])

        if not effects or len(effects) < skill_level:
            return {'success': False, 'message': '技能效果数据错误'}

        # 获取当前等级的治疗效果
        heal_effect = effects[skill_level - 1]
        base_heal = heal_effect.get('value', 20)

        # 根据道术攻击力计算治疗量
        tao_power = (player.道术攻击下限 + player.道术攻击上限) / 2
        total_heal = int(base_heal + tao_power * 0.5)  # 道术攻击力影响治疗量

        # 应用治疗
        old_hp = player.生命值
        max_hp = 150  # 假设最大生命值
        player.生命值 = min(player.生命值 + total_heal, max_hp)
        actual_heal = player.生命值 - old_hp

        # 消耗MP
        player.魔法值 -= mp_cost

        return {
            'success': True,
            'heal': actual_heal,
            'total_heal': total_heal,
            'mp_cost': mp_cost,
            'message': f'{skill["name"]} 恢复 {actual_heal} 生命值'
        }

    def summon_creature(self, player: MockPlayer, skill: Dict[str, Any]) -> Dict[str, Any]:
        """召唤生物"""
        # 检查MP消耗
        mp_cost = skill.get('mana_cost', 0)
        if player.魔法值 < mp_cost:
            return {'success': False, 'message': 'MP不足'}

        # 检查召唤数量限制
        if len(player.summoned_creatures) >= player.max_summons:
            return {'success': False, 'message': f'召唤物数量已达上限({player.max_summons})'}

        skill_level = skill.get('level', 1)
        effects = skill.get('effects', [])

        if not effects or len(effects) < skill_level:
            return {'success': False, 'message': '技能效果数据错误'}

        # 获取当前等级的召唤效果
        summon_effect = effects[skill_level - 1]
        creature_type = summon_effect.get('value', 'skeleton')
        duration = summon_effect.get('duration', -1)

        # 创建召唤物（等级基于玩家等级）
        creature_level = max(1, player.level // 2)
        creature = SummonedCreature(creature_type, creature_level, duration)

        # 添加到玩家的召唤物列表
        player.summoned_creatures.append(creature)

        # 消耗MP
        player.魔法值 -= mp_cost

        return {
            'success': True,
            'creature': creature,
            'mp_cost': mp_cost,
            'message': f'{skill["name"]} 召唤了 {creature.name} Lv.{creature.level}'
        }

    def use_skill(self, player: MockPlayer, skill_name: str, target=None) -> Dict[str, Any]:
        """使用技能"""
        skill = player.get_skill(skill_name)
        if not skill:
            return {'success': False, 'message': f'未学会技能: {skill_name}'}

        skill_type = skill.get('type')

        if skill_type == '攻击':
            return self.calculate_skill_damage(player, skill, target)
        elif skill_type == '被动':
            return {'success': True, 'message': f'{skill_name} 是被动技能，自动生效'}
        elif skill_type == '辅助' and any(effect.get('type') == 'heal' for effect in skill.get('effects', [])):
            # 治疗类技能
            return self.calculate_heal_amount(player, skill)
        elif skill_type == '召唤':
            # 召唤类技能
            return self.summon_creature(player, skill)
        else:
            return {'success': False, 'message': f'未知技能类型: {skill_type}'}

def interactive_skill_test():
    """交互式技能测试"""
    print("=== 交互式技能测试系统 ===")

    tester = SkillTester()

    # 显示可用技能
    print(f"\n📋 可用技能列表:")
    for category, skills in tester.skills_data.items():
        print(f"\n{category}:")
        for skill_id, skill_data in skills.items():
            print(f"   {skill_id}: {skill_data['name']} (最大等级: {skill_data.get('max_level', 1)})")

    # 创建测试角色
    print(f"\n👤 创建测试角色:")
    class_name = input("请输入职业 (战士/法师/道士): ").strip() or "战士"
    level = int(input("请输入等级 (1-50): ").strip() or "10")

    player = tester.create_test_player(class_name, level)
    print(f"✅ 创建角色: {player.name} Lv.{player.level}")

    while True:
        print(f"\n🎮 技能测试菜单:")
        print(f"1. 添加技能")
        print(f"2. 查看角色状态")
        print(f"3. 使用技能")
        print(f"4. 查看被动技能效果")
        print(f"5. 管理召唤物")
        print(f"6. 重置角色")
        print(f"0. 退出")

        choice = input("请选择操作: ").strip()

        if choice == "1":
            add_skill_interactive(tester, player)
        elif choice == "2":
            show_player_status(player)
        elif choice == "3":
            use_skill_interactive(tester, player)
        elif choice == "4":
            show_passive_effects(tester, player)
        elif choice == "5":
            manage_summons(player)
        elif choice == "6":
            player = tester.create_test_player(class_name, level)
            print(f"✅ 角色已重置")
        elif choice == "0":
            break
        else:
            print("❌ 无效选择")

def add_skill_interactive(tester: SkillTester, player):
    """交互式添加技能"""
    print(f"\n📚 添加技能:")

    # 显示技能类别
    categories = list(tester.skills_data.keys())
    for i, category in enumerate(categories, 1):
        print(f"{i}. {category}")

    try:
        cat_choice = int(input("选择技能类别: ")) - 1
        if cat_choice < 0 or cat_choice >= len(categories):
            print("❌ 无效选择")
            return

        category = categories[cat_choice]
        skills = tester.skills_data[category]

        # 显示该类别的技能
        skill_list = list(skills.items())
        for i, (skill_id, skill_data) in enumerate(skill_list, 1):
            print(f"{i}. {skill_data['name']} (ID: {skill_id})")

        skill_choice = int(input("选择技能: ")) - 1
        if skill_choice < 0 or skill_choice >= len(skill_list):
            print("❌ 无效选择")
            return

        skill_id, skill_data = skill_list[skill_choice]
        max_level = skill_data.get('max_level', 3)
        level = int(input(f"技能等级 (1-{max_level}): ") or "1")
        level = max(1, min(level, max_level))

        tester.add_skill_to_player(player, category, skill_id, level)

    except (ValueError, IndexError):
        print("❌ 输入错误")

def show_player_status(player):
    """显示角色状态"""
    print(f"\n📊 {player.name} 状态:")
    print(f"   职业: {player.character_class}")
    print(f"   等级: {player.level}")
    print(f"   生命值: {player.生命值}")
    print(f"   魔法值: {player.魔法值}")
    print(f"   攻击力: {player.攻击下限}-{player.攻击上限}")
    print(f"   魔法攻击: {player.魔法攻击下限}-{player.魔法攻击上限}")
    print(f"   道术攻击: {player.道术攻击下限}-{player.道术攻击上限}")
    print(f"   准确: {player.准确}")
    print(f"   暴击率: {player.暴击率:.1%}")

    print(f"\n🎯 已学技能:")
    if not player.learned_skills:
        print("   无")
    else:
        for skill in player.learned_skills:
            print(f"   {skill['name']} Lv.{skill['level']} ({skill['type']})")

    print(f"\n👹 召唤物:")
    if not player.summoned_creatures:
        print("   无")
    else:
        for i, creature in enumerate(player.summoned_creatures, 1):
            print(f"   {i}. {creature}")

def use_skill_interactive(tester: SkillTester, player):
    """交互式使用技能"""
    if not player.learned_skills:
        print("❌ 角色没有学会任何技能")
        return

    print(f"\n⚔️ 使用技能:")
    for i, skill in enumerate(player.learned_skills, 1):
        print(f"{i}. {skill['name']} Lv.{skill['level']} ({skill['type']})")

    try:
        choice = int(input("选择要使用的技能: ")) - 1
        if choice < 0 or choice >= len(player.learned_skills):
            print("❌ 无效选择")
            return

        skill = player.learned_skills[choice]
        result = tester.use_skill(player, skill['name'])

        print(f"✅ 技能使用结果:")
        print(f"   {result['message']}")
        if 'damage' in result:
            print(f"   伤害: {result['damage']}")
        if 'heal' in result:
            print(f"   治疗: {result['heal']}")
            if 'total_heal' in result:
                print(f"   理论治疗量: {result['total_heal']}")
        if 'creature' in result:
            print(f"   召唤物: {result['creature']}")
        if 'mp_cost' in result:
            print(f"   消耗MP: {result['mp_cost']}")
            print(f"   剩余MP: {player.魔法值}")

    except (ValueError, IndexError):
        print("❌ 输入错误")

def show_passive_effects(tester: SkillTester, player):
    """显示被动技能效果"""
    print(f"\n🔧 被动技能效果:")
    bonuses = tester.calculate_passive_skill_effects(player)

    if not bonuses:
        print("   无被动技能效果")
    else:
        print("   当前被动加成:")
        for attr, value in bonuses.items():
            if attr == '伤害加成':
                print(f"     {attr}: +{value:.1%}")
            else:
                print(f"     {attr}: +{value}")

def manage_summons(player):
    """管理召唤物"""
    print(f"\n👹 召唤物管理:")

    if not player.summoned_creatures:
        print("   当前没有召唤物")
        return

    print(f"   当前召唤物 ({len(player.summoned_creatures)}/{player.max_summons}):")
    for i, creature in enumerate(player.summoned_creatures, 1):
        print(f"   {i}. {creature}")

    print(f"\n操作选项:")
    print(f"1. 解散召唤物")
    print(f"2. 解散所有召唤物")
    print(f"0. 返回")

    choice = input("请选择操作: ").strip()

    if choice == "1":
        try:
            index = int(input(f"选择要解散的召唤物 (1-{len(player.summoned_creatures)}): ")) - 1
            if 0 <= index < len(player.summoned_creatures):
                creature = player.summoned_creatures.pop(index)
                print(f"✅ 已解散 {creature.name}")
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 输入错误")
    elif choice == "2":
        count = len(player.summoned_creatures)
        player.summoned_creatures.clear()
        print(f"✅ 已解散所有召唤物 ({count}个)")

def main():
    """主测试函数"""
    print("=== 独立技能测试系统 ===")

    tester = SkillTester()

    # 创建不同职业的测试玩家
    warrior = tester.create_test_player("战士", 15)
    mage = tester.create_test_player("法师", 12)
    taoist = tester.create_test_player("道士", 10)
    
    print(f"\n📊 创建测试角色:")
    print(f"   {warrior.name}: 攻击力 {warrior.攻击下限}-{warrior.攻击上限}, 生命值 {warrior.生命值}")
    print(f"   {mage.name}: 魔法攻击 {mage.魔法攻击下限}-{mage.魔法攻击上限}, 魔法值 {mage.魔法值}")
    print(f"   {taoist.name}: 道术攻击 {taoist.道术攻击下限}-{taoist.道术攻击上限}, 魔法值 {taoist.魔法值}")
    
    # 为战士添加技能
    print(f"\n🎯 为战士添加技能:")
    tester.add_skill_to_player(warrior, "战士技能", "skill_1", 2)  # 基本剑法
    tester.add_skill_to_player(warrior, "战士技能", "skill_2", 1)  # 攻杀剑术
    
    # 为法师添加技能
    print(f"\n🔮 为法师添加技能:")
    tester.add_skill_to_player(mage, "魔法技能", "skill_1", 2)    # 火球术
    tester.add_skill_to_player(mage, "魔法技能", "skill_2", 1)    # 治愈术
    
    # 为道士添加技能
    print(f"\n⚡ 为道士添加技能:")
    tester.add_skill_to_player(taoist, "道术技能", "skill_1", 1)  # 治愈术
    tester.add_skill_to_player(taoist, "道术技能", "skill_2", 2)  # 精神力战法
    
    # 测试被动技能效果
    print(f"\n🔧 测试被动技能效果:")
    warrior_bonuses = tester.calculate_passive_skill_effects(warrior)
    if warrior_bonuses:
        print(f"   战士被动加成: {warrior_bonuses}")
        tester.apply_passive_effects(warrior, warrior_bonuses)
    
    # 测试技能释放
    print(f"\n⚔️ 测试技能释放:")
    
    # 战士攻击技能
    if warrior.has_skill("攻杀剑术"):
        result = tester.use_skill(warrior, "攻杀剑术")
        if result['success']:
            print(f"   {warrior.name} 使用攻杀剑术: {result['message']}")
            print(f"     基础伤害: {result.get('base_damage', 0)}")
            print(f"     加成伤害: {result.get('bonus_damage', 0)}")
    
    # 法师魔法技能
    if mage.has_skill("火球术"):
        result = tester.use_skill(mage, "火球术")
        if result['success']:
            print(f"   {mage.name} 使用火球术: {result['message']}")
            print(f"     消耗MP: {result.get('mp_cost', 0)}, 剩余MP: {mage.魔法值}")
    
    # 道士治疗技能
    if taoist.has_skill("治愈术"):
        taoist.生命值 = 80  # 模拟受伤
        print(f"   {taoist.name} 受伤，生命值: {taoist.生命值}/150")
        result = tester.use_skill(taoist, "治愈术")
        if result['success']:
            print(f"   {taoist.name} 使用治愈术: {result['message']}")
            print(f"     理论治疗量: {result.get('total_heal', 0)}")
            print(f"     实际治疗量: {result.get('heal', 0)}")
            print(f"     当前生命值: {taoist.生命值}/150")
            print(f"     剩余MP: {taoist.魔法值}")

    # 测试召唤技能
    print(f"\n👹 测试召唤技能:")

    # 为道士添加召唤技能
    tester.add_skill_to_player(taoist, "道术技能", "skill_5", 2)  # 召唤骷髅

    # 使用召唤技能
    if taoist.has_skill("召唤骷髅"):
        print(f"   {taoist.name} 当前MP: {taoist.魔法值}")
        result = tester.use_skill(taoist, "召唤骷髅")
        if result['success']:
            print(f"   {taoist.name} 使用召唤骷髅: {result['message']}")
            print(f"     召唤物详情: {result['creature']}")
            print(f"     消耗MP: {result['mp_cost']}, 剩余MP: {taoist.魔法值}")
            print(f"     当前召唤物数量: {len(taoist.summoned_creatures)}/{taoist.max_summons}")

        # 再次召唤测试数量限制
        print(f"\n   尝试召唤更多骷髅...")
        for i in range(3):
            result = tester.use_skill(taoist, "召唤骷髅")
            if result['success']:
                print(f"     第{i+2}次召唤成功: {result['creature'].name}")
            else:
                print(f"     第{i+2}次召唤失败: {result['message']}")
                break
    
    # 技能对比测试
    print(f"\n📊 技能效果对比:")
    
    # 创建无技能的战士对比
    no_skill_warrior = tester.create_test_player("战士", 15)
    
    # 模拟普通攻击伤害对比
    base_attack = (no_skill_warrior.攻击下限 + no_skill_warrior.攻击上限) / 2
    skilled_attack = (warrior.攻击下限 + warrior.攻击上限) / 2
    
    print(f"   无技能战士平均攻击: {base_attack}")
    print(f"   有技能战士平均攻击: {skilled_attack}")
    if skilled_attack > base_attack:
        improvement = ((skilled_attack - base_attack) / base_attack) * 100
        print(f"   攻击力提升: +{improvement:.1f}%")

if __name__ == "__main__":
    print("🎮 独立技能测试系统")
    print("1. 自动演示模式")
    print("2. 交互式测试模式")

    mode = input("请选择模式 (1/2): ").strip()

    if mode == "2":
        interactive_skill_test()
    else:
        main()
        print(f"\n✅ 独立技能测试完成！")

    print(f"\n💡 使用说明:")
    print(f"   - 自动演示模式: 预设角色和技能的快速演示")
    print(f"   - 交互式模式: 可以自定义角色、添加技能、实时测试")
    print(f"   - 不依赖游戏存档，可以测试任何技能组合")
